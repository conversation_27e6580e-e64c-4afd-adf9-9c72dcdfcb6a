# -*- coding: utf-8 -*-
"""
from datetime import datetime
import json
import os
import re

from collections import defaultdict, Counter
import ast
import hashlib

# -*- coding: utf-8 -*-
أداة التحليل الذكي المتقدم للكود
Advanced Smart Code Analyzer
"""


class AdvancedCodeAnalyzer:

    def __init__(self):
        self.analysis_results = {
            'files_analyzed': 0,
            'total_lines': 0,
            'functions': [],
            'classes': [],
            'imports': [],
            'duplicates': [],
            'complexity_metrics': {},
            'code_smells': [],
            'suggestions': [],
            'patterns': {}
        }

    def analyze_project(self, project_path="."):
        """تحليل شامل للمشروع"""
        print("🔬 بدء التحليل الذكي المتقدم...")

        python_files = self._find_python_files(project_path)

        for file_path in python_files:
            if 'venv' in file_path or '__pycache__' in file_path:
                continue

            try:
                self._analyze_file(file_path)
            except Exception as e:
                print(f"❌ خطأ في تحليل {file_path}: {e}")

        self._detect_patterns()
        self._generate_suggestions()

        print(f"✅ تم تحليل {self.analysis_results['files_analyzed']} ملف")
        return self.analysis_results

    def _find_python_files(self, path):
        python_files = []
        for root, dirs, files in os.walk(path):
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))
        return python_files

    def _analyze_file(self, file_path):
        """تحليل ملف واحد"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            tree = ast.parse(content)
            self.analysis_results['files_analyzed'] += 1
            self.analysis_results['total_lines'] += len(content.splitlines())

            # تحليل العقد
            for node in ast.walk(tree):
                self._analyze_node(node, file_path)

            # تحليل التعقيد
            complexity = self._calculate_complexity(tree)
            self.analysis_results['complexity_metrics'][file_path] = complexity

            # اكتشاف Code Smells
            smells = self._detect_code_smells(content, file_path)
            self.analysis_results['code_smells'].extend(smells)

        except Exception as e:
            print(f"❌ خطأ في تحليل {file_path}: {e}")

    def _analyze_node(self, node, file_path):
        if isinstance(node, ast.FunctionDef):
            func_info = {
                'name': node.name,
                'file': file_path,
                'line': node.lineno,
                'args_count': len(node.args.args),
                'decorators': [d.id if isinstance(d, ast.Name) else str(d) for d in node.decorator_list],
                'docstring': ast.get_docstring(node),
                'complexity': self._calculate_function_complexity(node)
            }
            self.analysis_results['functions'].append(func_info)

        elif isinstance(node, ast.ClassDef):
            class_info = {
                'name': node.name,
                'file': file_path,
                'line': node.lineno,
                'methods': [n.name for n in node.body if isinstance(n, ast.FunctionDef)],
                'bases': [b.id if isinstance(b, ast.Name) else str(b) for b in node.bases],
                'docstring': ast.get_docstring(node)
            }
            self.analysis_results['classes'].append(class_info)

        elif isinstance(node, (ast.Import, ast.ImportFrom)):
            import_info = {
                'file': file_path,
                'line': node.lineno,
                'module': getattr(node, 'module', None),
                'names': [alias.name for alias in node.names] if hasattr(node, 'names') else []
            }
            self.analysis_results['imports'].append(import_info)

    def _calculate_complexity(self, tree):
        """حساب تعقيد الكود"""
        complexity = {
            'cyclomatic': 1,  # البداية من 1
            'cognitive': 0,
            'nesting_depth': 0
        }

        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.With, ast.Try)):
                complexity['cyclomatic'] += 1
                complexity['cognitive'] += 1
            elif isinstance(node, (ast.And, ast.Or)):
                complexity['cyclomatic'] += 1

        return complexity

    def _calculate_function_complexity(self, func_node):
        complexity = 1
        for node in ast.walk(func_node):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.With, ast.Try)):
                complexity += 1
        return complexity

    def _detect_code_smells(self, content, file_path):
        """اكتشاف Code Smells"""
        smells = []
        lines = content.splitlines()

        # Long Method
        for i, line in enumerate(lines, 1):
            if len(line) > 120:
                smells.append({
                    'type': 'Long Line',
                    'file': file_path,
                    'line': i,
                    'message': f'السطر طويل جداً ({len(line)} حرف)'
                })

        # TODO/FIXME comments
        for i, line in enumerate(lines, 1):
            if 'TODO' in line or 'FIXME' in line or 'محذوف' in line:
                smells.append({
                    'type': 'Technical Debt',
                    'file': file_path,
                    'line': i,
                    'message': 'يحتوي على TODO أو FIXME'
                })

        # Duplicate strings
        string_pattern = r'["\']([^"\']{10,})["\']'
        strings = re.findall(string_pattern, content)
        string_counts = Counter(strings)
        for string, count in string_counts.items():
            if count > 2:
                smells.append({
                    'type': 'Duplicate String',
                    'file': file_path,
                    'line': 0,
                    'message': f'النص "{string}" مكرر {count} مرات'
                })

        return smells

    def _detect_patterns(self):
        # تحليل أنماط الدوال
        func_names = [f['name'] for f in self.analysis_results['functions']]
        name_patterns = Counter(func_names)

        # البحث عن الدوال المكررة
        duplicates = [(name, count) for name, count in name_patterns.items() if count > 1]

        self.analysis_results['patterns'] = {
            'duplicate_functions': duplicates,
            'common_prefixes': self._find_common_prefixes(func_names),
            'naming_conventions': self._analyze_naming_conventions()
        }

    def _find_common_prefixes(self, names):
        """العثور على البادئات الشائعة"""
        prefixes = defaultdict(int)
        for name in names:
            if '_' in name:
                prefix = name.split('_')[0]
                prefixes[prefix] += 1

        return [(prefix, count) for prefix, count in prefixes.items() if count > 2]

    def _analyze_naming_conventions(self):
        conventions = {
            'snake_case_functions': 0,
            'camelCase_functions': 0,
            'PascalCase_classes': 0,
            'inconsistent_naming': []
        }

        for func in self.analysis_results['functions']:
            name = func['name']
            if '_' in name and name.islower():
                conventions['snake_case_functions'] += 1
            elif name[0].islower() and any(c.isupper() for c in name[1:]):
                conventions['camelCase_functions'] += 1
            else:
                conventions['inconsistent_naming'].append(name)

        for cls in self.analysis_results['classes']:
            name = cls['name']
            if name[0].isupper():
                conventions['PascalCase_classes'] += 1

        return conventions

    def _generate_suggestions(self):
        """إنتاج اقتراحات التحسين"""
        suggestions = []

        # اقتراحات بناءً على التعقيد
        for file_path, complexity in self.analysis_results['complexity_metrics'].items():
            if complexity['cyclomatic'] > 10:
                suggestions.append({
                    'type': 'High Complexity',
                    'file': file_path,
                    'message': f'الملف معقد جداً (تعقيد: {complexity["cyclomatic"]}). يُنصح بتقسيمه.',
                    'priority': 'high'
                })

        # اقتراحات بناءً على الدوال المكررة
        for func_name, count in self.analysis_results['patterns']['duplicate_functions']:
            suggestions.append({
                'type': 'Duplicate Function',
                'message': f'الدالة "{func_name}" مكررة {count} مرات. يُنصح بتوحيدها.',
                'priority': 'medium'
            })

        # اقتراحات بناءً على Code Smells
        smell_counts = Counter(smell['type'] for smell in self.analysis_results['code_smells'])
        for smell_type, count in smell_counts.items():
            if count > 5:
                suggestions.append({
                    'type': 'Code Smell Pattern',
                    'message': f'يوجد {count} حالة من "{smell_type}". يُنصح بمعالجتها.',
                    'priority': 'medium'
                })

        self.analysis_results['suggestions'] = suggestions

    def generate_report(self, output_file='analysis_report.json'):
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'files_analyzed': self.analysis_results['files_analyzed'],
                'total_lines': self.analysis_results['total_lines'],
                'total_functions': len(self.analysis_results['functions']),
                'total_classes': len(self.analysis_results['classes']),
                'code_smells_count': len(self.analysis_results['code_smells']),
                'suggestions_count': len(self.analysis_results['suggestions'])
            },
            'details': self.analysis_results
        }

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        print(f"📊 تم حفظ تقرير التحليل في: {output_file}")
        return report

if __name__ == "__main__":
    analyzer = AdvancedCodeAnalyzer()
    results = analyzer.analyze_project()
    report = analyzer.generate_report()

    print("\n📊 ملخص التحليل:")
    print(f"📁 الملفات المحللة: {results['files_analyzed']}")
    print(f"📝 إجمالي الأسطر: {results['total_lines']:,}")
    print(f"🔧 الدوال: {len(results['functions'])}")
    print(f"🏗️ الفئات: {len(results['classes'])}")
    print(f"⚠️ Code Smells: {len(results['code_smells'])}")
    print(f"💡 الاقتراحات: {len(results['suggestions'])}")