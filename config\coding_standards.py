# -*- coding: utf-8 -*-
"""
معايير التنسيق والبرمجة الموحدة
Unified Coding Standards and Formatting Guidelines
"""

# معايير التنسيق
FORMATTING_STANDARDS = {
    # طول السطر الأقصى
    'max_line_length': 100,
    
    # المسافات البادئة
    'indent_size': 4,
    'use_spaces': True,
    
    # الأسطر الفارغة
    'blank_lines_top_level': 2,
    'blank_lines_method': 1,
    
    # الاقتباسات
    'quote_style': 'double',  # استخدام علامات اقتباس مزدوجة
    
    # الفواصل
    'trailing_comma': True,
    
    # المسافات حول العمليات
    'spaces_around_operators': True,
    'spaces_after_comma': True,
}

# معايير التسمية
NAMING_CONVENTIONS = {
    # أسماء المتغيرات والدوال
    'variable_style': 'snake_case',
    'function_style': 'snake_case',
    
    # أسماء الفئات
    'class_style': 'PascalCase',
    
    # أسماء الثوابت
    'constant_style': 'UPPER_CASE',
    
    # أسماء الملفات
    'file_style': 'snake_case',
    
    # أسماء المجلدات
    'directory_style': 'snake_case',
}

# معايير التوثيق
DOCUMENTATION_STANDARDS = {
    # نمط التعليقات
    'comment_style': 'arabic_first',  # العربية أولاً ثم الإنجليزية
    
    # توثيق الدوال
    'docstring_style': 'google',
    'docstring_language': 'arabic',
    
    # التعليقات المضمنة
    'inline_comments': True,
    'comment_spacing': 2,  # مسافتان قبل التعليق المضمن
}

# معايير الاستيرادات
IMPORT_STANDARDS = {
    # ترتيب الاستيرادات
    'import_order': [
        'standard_library',
        'third_party',
        'local_application',
    ],
    
    # تجميع الاستيرادات
    'group_imports': True,
    'sort_imports': True,
    
    # أسطر فارغة بين المجموعات
    'lines_between_groups': 1,
}

# معايير معالجة الأخطاء
ERROR_HANDLING_STANDARDS = {
    # استخدام try-except محدد
    'specific_exceptions': True,
    
    # رسائل الخطأ بالعربية
    'arabic_error_messages': True,
    
    # تسجيل الأخطاء
    'log_errors': True,
    'log_level': 'ERROR',
}

# معايير الأداء
PERFORMANCE_STANDARDS = {
    # تجنب الحلقات المعقدة
    'max_nested_loops': 3,
    
    # تجنب الدوال الطويلة
    'max_function_length': 50,
    
    # تجنب الفئات الكبيرة
    'max_class_length': 500,
    
    # استخدام list comprehensions عند الإمكان
    'prefer_comprehensions': True,
}

# قواعد خاصة بـ PyQt5
PYQT_STANDARDS = {
    # تسمية الواجهات
    'widget_naming': 'descriptive',  # أسماء وصفية
    
    # تنظيم الإشارات والفتحات
    'signal_slot_organization': True,
    
    # استخدام layouts
    'prefer_layouts': True,
    
    # معالجة الأحداث
    'event_handling_style': 'override_methods',
}

# قواعد قاعدة البيانات
DATABASE_STANDARDS = {
    # تسمية الجداول
    'table_naming': 'snake_case',
    
    # تسمية الأعمدة
    'column_naming': 'snake_case',
    
    # استخدام المعاملات
    'use_parameters': True,
    
    # إغلاق الاتصالات
    'close_connections': True,
}


def get_standard(category, key=None):
    """الحصول على معيار محدد"""
    standards_map = {
        'formatting': FORMATTING_STANDARDS,
        'naming': NAMING_CONVENTIONS,
        'documentation': DOCUMENTATION_STANDARDS,
        'imports': IMPORT_STANDARDS,
        'error_handling': ERROR_HANDLING_STANDARDS,
        'performance': PERFORMANCE_STANDARDS,
        'pyqt': PYQT_STANDARDS,
        'database': DATABASE_STANDARDS,
    }
    
    if category not in standards_map:
        return None
    
    if key is None:
        return standards_map[category]
    
    return standards_map[category].get(key)


def validate_naming(name, style):
    """التحقق من صحة التسمية حسب النمط المحدد"""
    import re
    
    patterns = {
        'snake_case': r'^[a-z][a-z0-9_]*$',
        'PascalCase': r'^[A-Z][a-zA-Z0-9]*$',
        'UPPER_CASE': r'^[A-Z][A-Z0-9_]*$',
        'camelCase': r'^[a-z][a-zA-Z0-9]*$',
    }
    
    if style not in patterns:
        return False
    
    return bool(re.match(patterns[style], name))


def format_docstring(description_ar, description_en=None, params=None, returns=None):
    """تنسيق docstring حسب المعايير"""
    docstring = f'"""{description_ar}'
    
    if description_en:
        docstring += f'\n{description_en}'
    
    if params:
        docstring += '\n\nArgs:'
        for param, desc in params.items():
            docstring += f'\n    {param}: {desc}'
    
    if returns:
        docstring += f'\n\nReturns:\n    {returns}'
    
    docstring += '"""'
    return docstring


# مثال على الاستخدام
if __name__ == "__main__":
    # اختبار التسمية
    print("اختبار التسمية:")
    print(f"my_variable (snake_case): {validate_naming('my_variable', 'snake_case')}")
    print(f"MyClass (PascalCase): {validate_naming('MyClass', 'PascalCase')}")
    print(f"MAX_SIZE (UPPER_CASE): {validate_naming('MAX_SIZE', 'UPPER_CASE')}")
    
    # مثال على docstring
    print("\nمثال على docstring:")
    docstring = format_docstring(
        "حساب مجموع قائمة من الأرقام",
        "Calculate the sum of a list of numbers",
        {"numbers": "قائمة الأرقام المراد جمعها"},
        "مجموع الأرقام"
    )
    print(docstring)
