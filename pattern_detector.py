# -*- coding: utf-8 -*-
"""
    from datetime import datetime
import json
import os
import re

            import astor
from collections import defaultdict, Counter
from difflib import SequenceMatcher
import ast
import hashlib

# -*- coding: utf-8 -*-
كاشف الأنماط والتكرار الذكي
Smart Pattern and Duplication Detector
"""


class SmartPatternDetector:

    def __init__(self):
        self.patterns = {
            'duplicate_functions': [],
            'similar_functions': [],
            'duplicate_code_blocks': [],
            'repeated_patterns': [],
            'naming_patterns': {},
            'structural_patterns': []
        }

    def detect_patterns(self, project_path="."):
        """اكتشاف جميع الأنماط في المشروع"""
        print("🧠 بدء اكتشاف الأنماط والتكرار...")

        python_files = self._find_python_files(project_path)
        all_functions = []
        all_code_blocks = []

        for file_path in python_files:
            if 'venv' in file_path or '__pycache__' in file_path:
                continue

            try:
                functions, code_blocks = self._extract_code_elements(file_path)
                all_functions.extend(functions)
                all_code_blocks.extend(code_blocks)
            except Exception as e:
                print(f"❌ خطأ في معالجة {file_path}: {e}")

        # اكتشاف الأنماط المختلفة
        self._detect_duplicate_functions(all_functions)
        self._detect_similar_functions(all_functions)
        self._detect_duplicate_code_blocks(all_code_blocks)
        self._detect_naming_patterns(all_functions)
        self._detect_structural_patterns(all_functions)

        print("✅ تم اكتشاف الأنماط بنجاح")
        return self.patterns

    def _find_python_files(self, path):
        python_files = []
        for root, dirs, files in os.walk(path):
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))
        return python_files

    def _extract_code_elements(self, file_path):
        """استخراج عناصر الكود من الملف"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        tree = ast.parse(content)
        functions = []
        code_blocks = []

        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                func_info = {
                    'name': node.name,
                    'file': file_path,
                    'line': node.lineno,
                    'code': ast.unparse(node) if hasattr(ast, 'unparse') else self._node_to_string(node),
                    'hash': self._calculate_code_hash(node),
                    'signature': self._get_function_signature(node),
                    'body_lines': len(node.body)
                }
                functions.append(func_info)

                # استخراج كتل الكود من داخل الدالة
                for child in node.body:
                    if isinstance(child, (ast.If, ast.For, ast.While, ast.With, ast.Try)):
                        block_info = {
                            'type': type(child).__name__,
                            'file': file_path,
                            'line': child.lineno,
                            'code': ast.unparse(child) if hasattr(ast, 'unparse') else self._node_to_string(child),
                            'hash': self._calculate_code_hash(child)
                        }
                        code_blocks.append(block_info)

        return functions, code_blocks

    def _node_to_string(self, node):
        try:
            return astor.to_source(node)
        except ImportError:
            return str(node)

    def _calculate_code_hash(self, node):
        """حساب hash للكود"""
        try:
            code_str = ast.unparse(node) if hasattr(ast, 'unparse') else self._node_to_string(node)
            # إزالة المسافات والتعليقات للمقارنة الدقيقة
            normalized = re.sub(r'\s+', ' ', code_str).strip()
            return hashlib.md5(normalized.encode()).hexdigest()
        except:
            return ""

    def _get_function_signature(self, node):
        args = [arg.arg for arg in node.args.args]
        return f"{node.name}({', '.join(args)})"

    def _detect_duplicate_functions(self, functions):
        """اكتشاف الدوال المكررة تماماً"""
        hash_groups = defaultdict(list)

        for func in functions:
            if func['hash']:
                hash_groups[func['hash']].append(func)

        for hash_val, func_group in hash_groups.items():
            if len(func_group) > 1:
                self.patterns['duplicate_functions'].append({
                    'hash': hash_val,
                    'count': len(func_group),
                    'functions': func_group,
                    'savings_potential': sum(f['body_lines'] for f in func_group[1:])  # السطور التي يمكن توفيرها
                })

    def _detect_similar_functions(self, functions):
        for i, func1 in enumerate(functions):
            for func2 in functions[i+1:]:
                if func1['file'] != func2['file'] or func1['name'] == func2['name']:
                    continue

                similarity = self._calculate_similarity(func1['code'], func2['code'])
                if similarity > 0.8:  # 80% تشابه
                    self.patterns['similar_functions'].append({
                        'function1': func1,
                        'function2': func2,
                        'similarity': similarity,
                        'suggestion': 'يمكن دمج هاتين الدالتين أو إنشاء دالة مشتركة'
                    })

    def _detect_duplicate_code_blocks(self, code_blocks):
        """اكتشاف كتل الكود المكررة"""
        hash_groups = defaultdict(list)

        for block in code_blocks:
            if block['hash']:
                hash_groups[block['hash']].append(block)

        for hash_val, block_group in hash_groups.items():
            if len(block_group) > 1:
                self.patterns['duplicate_code_blocks'].append({
                    'hash': hash_val,
                    'count': len(block_group),
                    'blocks': block_group,
                    'type': block_group[0]['type'],
                    'suggestion': 'يمكن استخراج هذا الكود إلى دالة منفصلة'
                })

    def _detect_naming_patterns(self, functions):
        # تحليل البادئات
        prefixes = defaultdict(list)
        suffixes = defaultdict(list)

        for func in functions:
            name = func['name']

            # البادئات
            if '_' in name:
                prefix = name.split('_')[0]
                prefixes[prefix].append(func)

            # اللواحق
            if name.endswith(('_data', '_info', '_result', '_value')):
                suffix = name.split('_')[-1]
                suffixes[suffix].append(func)

        # حفظ الأنماط المهمة فقط
        self.patterns['naming_patterns'] = {
            'common_prefixes': {k: len(v) for k, v in prefixes.items() if len(v) > 3},
            'common_suffixes': {k: len(v) for k, v in suffixes.items() if len(v) > 2}
        }

    def _detect_structural_patterns(self, functions):
        """اكتشاف الأنماط الهيكلية"""
        # تجميع الدوال حسب عدد المعاملات
        by_args = defaultdict(list)
        by_length = defaultdict(list)

        for func in functions:
            args_count = func['signature'].count(',') + 1 if ',' in func['signature'] else (1 if '(' in func['signature'] and ')' not in func['signature'].replace('()', '') else 0)
            by_args[args_count].append(func)
            by_length[func['body_lines']].append(func)

        # البحث عن أنماط مثيرة للاهتمام
        for args_count, func_list in by_args.items():
            if len(func_list) > 10 and args_count > 5:
                self.patterns['structural_patterns'].append({
                    'type': 'High Parameter Count',
                    'pattern': f'دوال بـ {args_count} معاملات',
                    'count': len(func_list),
                    'suggestion': 'فكر في تقليل عدد المعاملات أو استخدام كائنات'
                })

        for length, func_list in by_length.items():
            if len(func_list) > 5 and length > 50:
                self.patterns['structural_patterns'].append({
                    'type': 'Long Functions',
                    'pattern': f'دوال طويلة ({length} سطر)',
                    'count': len(func_list),
                    'suggestion': 'فكر في تقسيم هذه الدوال إلى دوال أصغر'
                })

    def _calculate_similarity(self, code1, code2):
        return SequenceMatcher(None, code1, code2).ratio()

    def generate_pattern_report(self, output_file='pattern_report.json'):
        """إنتاج تقرير الأنماط"""
        report = {
            'timestamp': str(datetime.now()),
            'summary': {
                'duplicate_functions': len(self.patterns['duplicate_functions']),
                'similar_functions': len(self.patterns['similar_functions']),
                'duplicate_code_blocks': len(self.patterns['duplicate_code_blocks']),
                'structural_patterns': len(self.patterns['structural_patterns'])
            },
            'patterns': self.patterns,
            'recommendations': self._generate_recommendations()
        }

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        print(f"📊 تم حفظ تقرير الأنماط في: {output_file}")
        return report

    def _generate_recommendations(self):
        recommendations = []

        # توصيات بناءً على الدوال المكررة
        total_duplicate_savings = sum(p['savings_potential'] for p in self.patterns['duplicate_functions'])
        if total_duplicate_savings > 100:
            recommendations.append({
                'priority': 'high',
                'type': 'Code Duplication',
                'message': f'يمكن توفير {total_duplicate_savings} سطر بإزالة الدوال المكررة',
                'action': 'دمج الدوال المكررة في دالة واحدة'
            })

        # توصيات بناءً على الدوال المتشابهة
        if len(self.patterns['similar_functions']) > 10:
            recommendations.append({
                'priority': 'medium',
                'type': 'Similar Code',
                'message': f'يوجد {len(self.patterns["similar_functions"])} زوج من الدوال المتشابهة',
                'action': 'فحص إمكانية دمج الدوال المتشابهة'
            })

        return recommendations

if __name__ == "__main__":

    detector = SmartPatternDetector()
    patterns = detector.detect_patterns()
    report = detector.generate_pattern_report()

    print("\n🧠 ملخص اكتشاف الأنماط:")
    print(f"🔄 الدوال المكررة: {len(patterns['duplicate_functions'])}")
    print(f"🔀 الدوال المتشابهة: {len(patterns['similar_functions'])}")
    print(f"📦 كتل الكود المكررة: {len(patterns['duplicate_code_blocks'])}")
    print(f"🏗️ الأنماط الهيكلية: {len(patterns['structural_patterns'])}")