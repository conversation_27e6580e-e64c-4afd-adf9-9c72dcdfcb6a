{"timestamp": "2025-06-27 15:18:48.112990", "summary": {"duplicate_functions": 1, "similar_functions": 0, "duplicate_code_blocks": 2, "structural_patterns": 0}, "patterns": {"duplicate_functions": [{"hash": "730bd534d6860f327baf2c6db04dc6d3", "count": 2, "functions": [{"name": "_find_python_files", "file": ".\\tools\\advanced_analyzer.py", "line": 54, "code": "def _find_python_files(self, path):\n    python_files = []\n    for root, dirs, files in os.walk(path):\n        for file in files:\n            if file.endswith('.py'):\n                python_files.append(os.path.join(root, file))\n    return python_files", "hash": "730bd534d6860f327baf2c6db04dc6d3", "signature": "_find_python_files(self, path)", "body_lines": 3}, {"name": "_find_python_files", "file": ".\\tools\\pattern_detector.py", "line": 64, "code": "def _find_python_files(self, path):\n    python_files = []\n    for root, dirs, files in os.walk(path):\n        for file in files:\n            if file.endswith('.py'):\n                python_files.append(os.path.join(root, file))\n    return python_files", "hash": "730bd534d6860f327baf2c6db04dc6d3", "signature": "_find_python_files(self, path)", "body_lines": 3}], "savings_potential": 3}], "similar_functions": [], "duplicate_code_blocks": [{"hash": "46b9c02fdf137a011e85c062c5f8e896", "count": 2, "blocks": [{"type": "For", "file": ".\\tools\\advanced_analyzer.py", "line": 56, "code": "for root, dirs, files in os.walk(path):\n    for file in files:\n        if file.endswith('.py'):\n            python_files.append(os.path.join(root, file))", "hash": "46b9c02fdf137a011e85c062c5f8e896"}, {"type": "For", "file": ".\\tools\\pattern_detector.py", "line": 66, "code": "for root, dirs, files in os.walk(path):\n    for file in files:\n        if file.endswith('.py'):\n            python_files.append(os.path.join(root, file))", "hash": "46b9c02fdf137a011e85c062c5f8e896"}], "type": "For", "suggestion": "يمكن استخراج هذا الكود إلى دالة منفصلة"}, {"hash": "d3cd2d425a4ac61e17341c3b35ef4128", "count": 2, "blocks": [{"type": "With", "file": ".\\tools\\advanced_analyzer.py", "line": 280, "code": "with open(output_file, 'w', encoding='utf-8') as f:\n    json.dump(report, f, ensure_ascii=False, indent=2)", "hash": "d3cd2d425a4ac61e17341c3b35ef4128"}, {"type": "With", "file": ".\\tools\\pattern_detector.py", "line": 249, "code": "with open(output_file, 'w', encoding='utf-8') as f:\n    json.dump(report, f, ensure_ascii=False, indent=2)", "hash": "d3cd2d425a4ac61e17341c3b35ef4128"}], "type": "With", "suggestion": "يمكن استخراج هذا الكود إلى دالة منفصلة"}], "repeated_patterns": [], "naming_patterns": {"common_prefixes": {"": 27}, "common_suffixes": {}}, "structural_patterns": []}, "recommendations": []}