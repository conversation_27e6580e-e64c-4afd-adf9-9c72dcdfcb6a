#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
            import os
        import datetime
from PyQt5.QtCore import QTimer, QObject, pyqtSignal
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
import os
import sys
import sys
import threading

                import psutil
        import ctypes
        import shutil
from PyQt5.QtWidgets import QApplication, QMessageBox
from performance_config import get_config, apply_performance_settings
import gc
import gc
import psutil
import sqlite3
import time
import time
import traceback

#!/usr/bin/env python
# -*- coding: utf-8 -*-
أداة تحسين الأداء للبرنامج
Performance Optimizer Tool
"""


class PerformanceMonitor(QObject):

    memory_warning = pyqtSignal(float)  # تحذير استهلاك الذاكرة
    performance_update = pyqtSignal(dict)  # تحديث معلومات الأداء

    def __init__(self):
        super().__init__()
        self.config = get_config('memory')
        self.monitoring = False
        self.timer = QTimer()
        self.timer.timeout.connect(self.check_performance)

    def start_monitoring(self):
        """بدء مراقبة الأداء"""
        if not self.monitoring:
            self.monitoring = True
            interval = self.config.get('cleanup_interval', 60000)
            self.timer.start(interval)
            print("🔍 بدء مراقبة الأداء...")

    def stop_monitoring(self):
        if self.monitoring:
            self.monitoring = False
            self.timer.stop()
            print("⏹️ إيقاف مراقبة الأداء")

    def check_performance(self):
        """فحص الأداء الحالي"""
        try:
            # معلومات الذاكرة (بدون psutil)
            memory_mb = 0
            try:
                process = psutil.Process()
                memory_info = process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                cpu_percent = process.cpu_percent()
                system_memory = psutil.virtual_memory()
                system_memory_percent = system_memory.percent
            except ImportError:
                # استخدام طرق بديلة
                memory_mb = 50  # تقدير
                cpu_percent = 0
                system_memory_percent = 50

            performance_data = {
                'memory_mb': memory_mb,
                'cpu_percent': cpu_percent,
                'system_memory_percent': system_memory_percent,
                'gc_count': len(gc.get_objects()),
                'timestamp': time.time()
            }

            # إرسال تحديث الأداء
            self.performance_update.emit(performance_data)

            # فحص تحذيرات الذاكرة
            max_memory = self.config.get('max_cache_size', 50 * 1024 * 1024) / 1024 / 1024
            if memory_mb > max_memory:
                self.memory_warning.emit(memory_mb)
                self.cleanup_memory()

        except Exception as e:
            print(f"❌ خطأ في مراقبة الأداء: {str(e)}")

    def cleanup_memory(self):
        try:
            print("🧹 تنظيف الذاكرة...")
            collected = gc.collect()
            print(f"✅ تم تنظيف {collected} كائن من الذاكرة")
        except Exception as e:
            print(f"❌ خطأ في تنظيف الذاكرة: {str(e)}")

def initialize_performance_optimization():
    """تهيئة تحسينات الأداء"""
    try:
        print("🚀 تهيئة تحسينات الأداء...")

        # تطبيق الإعدادات الأساسية
        apply_performance_settings()

        # تحسين Python
        if hasattr(sys, 'setswitchinterval'):
            sys.setswitchinterval(0.005)

        # تحسين جمع القمامة
        gc.set_threshold(100, 1000, 10000)

        print("✅ تم تهيئة تحسينات الأداء بنجاح")
        return True

    except Exception as e:
        print(f"❌ خطأ في تهيئة تحسينات الأداء: {str(e)}")
        return False

أداة لتحسين أداء البرنامج وحل مشاكل التجمد والتوقف
"""


def optimize_memory():
    # تشغيل جامع القمامة يدوياً
    collected = gc.collect()
    print(f"تم تحرير {collected} كائن من الذاكرة")

    # تحرير الذاكرة المخصصة للنظام
    if sys.platform == 'win32':
        ctypes.windll.kernel32.SetProcessWorkingSetSize(-1, -1)

    # إعادة الذاكرة غير المستخدمة إلى نظام التشغيل
    process = psutil.Process(os.getpid())
    memory_before = process.memory_info().rss / (1024 * 1024)

    # تشغيل جامع القمامة مرة أخرى بعد تحرير الذاكرة
    gc.collect()

    memory_after = process.memory_info().rss / (1024 * 1024)
    print(f"تم تقليل استخدام الذاكرة من {memory_before:.2f} ميجابايت إلى {memory_after:.2f} ميجابايت")

    return memory_before - memory_after

def optimize_database(db_path='accounting.db'):
    """تحسين أداء قاعدة البيانات"""
    try:
        # فتح اتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # تشغيل عمليات التحسين
        cursor.execute("PRAGMA optimize")
        cursor.execute("VACUUM")
        cursor.execute("ANALYZE")

        # إغلاق الاتصال
        conn.close()

        print(f"تم تحسين قاعدة البيانات: {db_path}")
        return True
    except Exception as e:
        print(f"خطأ في تحسين قاعدة البيانات: {str(e)}")
        return False

def check_database_integrity(db_path='accounting.db'):
    try:
        # فتح اتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # التحقق من سلامة قاعدة البيانات
        cursor.execute("PRAGMA integrity_check")
        result = cursor.fetchone()[0]

        # إغلاق الاتصال
        conn.close()

        if result == "ok":
            print("قاعدة البيانات سليمة")
            return True
        else:
            print(f"مشكلة في سلامة قاعدة البيانات: {result}")
            return False
    except Exception as e:
        print(f"خطأ في التحقق من سلامة قاعدة البيانات: {str(e)}")
        return False

def create_database_backup(db_path='accounting.db'):
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    try:

        # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        backup_dir = "backups"
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)

        # إنشاء اسم ملف النسخة الاحتياطية
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = os.path.join(backup_dir, f"accounting_backup_{timestamp}.db")

        # نسخ ملف قاعدة البيانات
        shutil.copy2(db_path, backup_path)

        print(f"تم إنشاء نسخة احتياطية من قاعدة البيانات: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"خطأ في إنشاء نسخة احتياطية من قاعدة البيانات: {str(e)}")
        return None

class DatabaseOptimizer(QThread):
    finished = pyqtSignal(bool)
    progress = pyqtSignal(str)

    def __init__(self, db_path='accounting.db'):
        super().__init__()
        self.db_path = db_path

    def run(self):
        try:
            self.progress.emit("جاري إنشاء نسخة احتياطية من قاعدة البيانات...")
            backup_path = create_database_backup(self.db_path)

            if not backup_path:
                self.progress.emit("فشل إنشاء نسخة احتياطية من قاعدة البيانات")
                self.finished.emit(False)
                return

            self.progress.emit("جاري التحقق من سلامة قاعدة البيانات...")
            if not check_database_integrity(self.db_path):
                self.progress.emit("تم اكتشاف مشاكل في قاعدة البيانات")
                self.finished.emit(False)
                return

            self.progress.emit("جاري تحسين أداء قاعدة البيانات...")
            if not optimize_database(self.db_path):
                self.progress.emit("فشل تحسين قاعدة البيانات")
                self.finished.emit(False)
                return

            self.progress.emit("تم تحسين قاعدة البيانات بنجاح")
            self.finished.emit(True)
        except Exception as e:
            self.progress.emit(f"خطأ: {str(e)}")
            self.finished.emit(False)

class MemoryOptimizer(QThread):
    """فئة لتحسين استخدام الذاكرة في خلفية البرنامج"""
    finished = pyqtSignal(float)

    def run(self):
        try:
            # تحسين استخدام الذاكرة
            memory_saved = optimize_memory()
            self.finished.emit(memory_saved)
        except Exception as e:
            print(f"خطأ في تحسين الذاكرة: {str(e)}")
            self.finished.emit(0)

def setup_performance_monitoring(main_window):
    # إنشاء مؤقت لتحسين الذاكرة دورياً - معطل
    # memory_timer = QTimer(main_window)
    # memory_timer.timeout.connect(lambda: MemoryOptimizer().start())
    # memory_timer.start(600000)  # تحسين الذاكرة كل 10 دقائق - معطل

    # إنشاء مؤقت لتحسين قاعدة البيانات دورياً - معطل
    # db_timer = QTimer(main_window)
    # db_timer.timeout.connect(lambda: optimize_database_on_idle(main_window))
    # db_timer.start(7200000)  # تحسين قاعدة البيانات كل ساعتين - معطل

    # تخزين المؤقتات في النافذة الرئيسية - معطل
    # main_window.memory_timer = memory_timer
    # main_window.db_timer = db_timer

    print("✅ تم تعطيل جميع مؤقتات مراقبة الأداء لتجنب التحديث التلقائي")

def optimize_database_on_idle(main_window):
    """تحسين قاعدة البيانات عندما يكون البرنامج خاملاً"""
    # التحقق من استخدام المعالج
    cpu_usage = psutil.cpu_percent(interval=1)

    # تحسين قاعدة البيانات فقط عندما يكون استخدام المعالج منخفضاً
    if cpu_usage < 30:
        optimizer = DatabaseOptimizer()
        optimizer.progress.connect(lambda msg: print(msg))
        optimizer.start()

def apply_performance_optimizations():
    # تحسين استخدام الذاكرة
    optimize_memory()

    # تحسين قاعدة البيانات
    optimize_database()

    # تعطيل جمع القمامة التلقائي وإدارته يدوياً
    gc.disable()

    print("تم تطبيق تحسينات الأداء بنجاح")

if __name__ == "__main__":
    # تطبيق تحسينات الأداء
    apply_performance_optimizations()