{"timestamp": "2025-06-27 15:30:44.613897", "database_path": "accounting.db", "analysis_results": {"tables": {"clients": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "name", "type": "VARCHAR(100)", "nullable": false}, {"name": "phone", "type": "VARCHAR(20)", "nullable": true}, {"name": "email", "type": "VARCHAR(100)", "nullable": true}, {"name": "address", "type": "VARCHAR(200)", "nullable": true}, {"name": "balance", "type": "FLOAT", "nullable": true}, {"name": "notes", "type": "TEXT", "nullable": true}, {"name": "created_at", "type": "DATETIME", "nullable": true}], "row_count": 6, "size_bytes": 600, "is_empty": false}, "suppliers": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "name", "type": "VARCHAR(100)", "nullable": false}, {"name": "phone", "type": "VARCHAR(20)", "nullable": true}, {"name": "email", "type": "VARCHAR(100)", "nullable": true}, {"name": "address", "type": "VARCHAR(200)", "nullable": true}, {"name": "balance", "type": "FLOAT", "nullable": true}, {"name": "notes", "type": "TEXT", "nullable": true}, {"name": "created_at", "type": "DATETIME", "nullable": true}, {"name": "status", "type": "TEXT", "nullable": true}, {"name": "updated_at", "type": "DATETIME", "nullable": true}], "row_count": 1, "size_bytes": 100, "is_empty": false}, "employees": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "name", "type": "VARCHAR(100)", "nullable": false}, {"name": "position", "type": "VARCHAR(100)", "nullable": true}, {"name": "phone", "type": "VARCHAR(20)", "nullable": true}, {"name": "email", "type": "VARCHAR(100)", "nullable": true}, {"name": "address", "type": "VARCHAR(200)", "nullable": true}, {"name": "hire_date", "type": "DATETIME", "nullable": true}, {"name": "salary", "type": "FLOAT", "nullable": true}, {"name": "notes", "type": "TEXT", "nullable": true}, {"name": "balance", "type": "REAL", "nullable": true}], "row_count": 1, "size_bytes": 100, "is_empty": false}, "notifications": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "title", "type": "VARCHAR(100)", "nullable": false}, {"name": "message", "type": "TEXT", "nullable": false}, {"name": "date", "type": "DATETIME", "nullable": true}, {"name": "is_read", "type": "BOOLEAN", "nullable": true}, {"name": "type", "type": "VARCHAR(50)", "nullable": true}, {"name": "related_id", "type": "INTEGER", "nullable": true}], "row_count": 2, "size_bytes": 200, "is_empty": false}, "settings": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "key", "type": "VARCHAR(100)", "nullable": false}, {"name": "value", "type": "VARCHAR(500)", "nullable": true}, {"name": "category", "type": "VARCHAR(100)", "nullable": true}, {"name": "description", "type": "VARCHAR(200)", "nullable": true}], "row_count": 19, "size_bytes": 1900, "is_empty": false}, "users": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "username", "type": "VARCHAR(50)", "nullable": false}, {"name": "password", "type": "VARCHAR(100)", "nullable": false}, {"name": "full_name", "type": "VARCHAR(100)", "nullable": true}, {"name": "email", "type": "VARCHAR(100)", "nullable": true}, {"name": "phone", "type": "VARCHAR(20)", "nullable": true}, {"name": "role", "type": "VARCHAR(20)", "nullable": true}, {"name": "is_active", "type": "BOOLEAN", "nullable": true}, {"name": "last_login", "type": "DATETIME", "nullable": true}, {"name": "created_at", "type": "DATETIME", "nullable": true}, {"name": "can_view_dashboard", "type": "BOOLEAN", "nullable": true}, {"name": "can_manage_clients", "type": "BOOLEAN", "nullable": true}, {"name": "can_manage_suppliers", "type": "BOOLEAN", "nullable": true}, {"name": "can_manage_employees", "type": "BOOLEAN", "nullable": true}, {"name": "can_manage_projects", "type": "BOOLEAN", "nullable": true}, {"name": "can_manage_inventory", "type": "BOOLEAN", "nullable": true}, {"name": "can_manage_expenses", "type": "BOOLEAN", "nullable": true}, {"name": "can_manage_revenues", "type": "BOOLEAN", "nullable": true}, {"name": "can_manage_invoices", "type": "BOOLEAN", "nullable": true}, {"name": "can_view_reports", "type": "BOOLEAN", "nullable": true}, {"name": "can_manage_settings", "type": "BOOLEAN", "nullable": true}, {"name": "can_manage_users", "type": "BOOLEAN", "nullable": true}], "row_count": 1, "size_bytes": 100, "is_empty": false}, "salaries": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "employee_id", "type": "INTEGER", "nullable": true}, {"name": "amount", "type": "FLOAT", "nullable": false}, {"name": "payment_date", "type": "DATETIME", "nullable": true}, {"name": "month", "type": "INTEGER", "nullable": true}, {"name": "year", "type": "INTEGER", "nullable": true}, {"name": "notes", "type": "TEXT", "nullable": true}], "row_count": 0, "size_bytes": 0, "is_empty": true}, "expenses": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "title", "type": "VARCHAR(100)", "nullable": false}, {"name": "amount", "type": "FLOAT", "nullable": false}, {"name": "date", "type": "DATETIME", "nullable": true}, {"name": "category", "type": "VARCHAR(100)", "nullable": true}, {"name": "supplier_id", "type": "INTEGER", "nullable": true}, {"name": "notes", "type": "TEXT", "nullable": true}], "row_count": 1, "size_bytes": 100, "is_empty": false}, "invoices": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "invoice_number", "type": "VARCHAR(50)", "nullable": true}, {"name": "client_id", "type": "INTEGER", "nullable": true}, {"name": "date", "type": "DATETIME", "nullable": true}, {"name": "due_date", "type": "DATETIME", "nullable": true}, {"name": "total_amount", "type": "FLOAT", "nullable": true}, {"name": "paid_amount", "type": "FLOAT", "nullable": true}, {"name": "status", "type": "VARCHAR(20)", "nullable": true}, {"name": "notes", "type": "TEXT", "nullable": true}], "row_count": 1, "size_bytes": 100, "is_empty": false}, "projects": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "name", "type": "VARCHAR(200)", "nullable": false}, {"name": "client_id", "type": "INTEGER", "nullable": true}, {"name": "location", "type": "VARCHAR(200)", "nullable": true}, {"name": "area", "type": "FLOAT", "nullable": true}, {"name": "start_date", "type": "DATETIME", "nullable": true}, {"name": "expected_end_date", "type": "DATETIME", "nullable": true}, {"name": "actual_end_date", "type": "DATETIME", "nullable": true}, {"name": "status", "type": "VARCHAR(50)", "nullable": true}, {"name": "budget", "type": "FLOAT", "nullable": true}, {"name": "total_cost", "type": "FLOAT", "nullable": true}, {"name": "description", "type": "TEXT", "nullable": true}, {"name": "notes", "type": "TEXT", "nullable": true}], "row_count": 1, "size_bytes": 100, "is_empty": false}, "inventory": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "name", "type": "VARCHAR(100)", "nullable": false}, {"name": "category", "type": "VARCHAR(100)", "nullable": true}, {"name": "unit", "type": "VARCHAR(50)", "nullable": true}, {"name": "quantity", "type": "FLOAT", "nullable": true}, {"name": "min_quantity", "type": "FLOAT", "nullable": true}, {"name": "cost_price", "type": "FLOAT", "nullable": true}, {"name": "selling_price", "type": "FLOAT", "nullable": true}, {"name": "supplier_id", "type": "INTEGER", "nullable": true}, {"name": "location", "type": "VARCHAR(100)", "nullable": true}, {"name": "notes", "type": "TEXT", "nullable": true}, {"name": "last_updated", "type": "DATETIME", "nullable": true}], "row_count": 1, "size_bytes": 100, "is_empty": false}, "revenues": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "title", "type": "VARCHAR(100)", "nullable": false}, {"name": "amount", "type": "FLOAT", "nullable": false}, {"name": "date", "type": "DATETIME", "nullable": true}, {"name": "category", "type": "VARCHAR(100)", "nullable": true}, {"name": "invoice_id", "type": "INTEGER", "nullable": true}, {"name": "notes", "type": "TEXT", "nullable": true}], "row_count": 1, "size_bytes": 100, "is_empty": false}, "invoice_items": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "invoice_id", "type": "INTEGER", "nullable": true}, {"name": "description", "type": "VARCHAR(200)", "nullable": false}, {"name": "quantity", "type": "FLOAT", "nullable": true}, {"name": "unit_price", "type": "FLOAT", "nullable": false}, {"name": "total_price", "type": "FLOAT", "nullable": false}], "row_count": 2, "size_bytes": 200, "is_empty": false}, "project_expenses": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "project_id", "type": "INTEGER", "nullable": true}, {"name": "expense_id", "type": "INTEGER", "nullable": true}, {"name": "amount", "type": "FLOAT", "nullable": false}, {"name": "date", "type": "DATETIME", "nullable": true}, {"name": "notes", "type": "TEXT", "nullable": true}], "row_count": 0, "size_bytes": 0, "is_empty": true}, "project_materials": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "project_id", "type": "INTEGER", "nullable": true}, {"name": "material_id", "type": "INTEGER", "nullable": true}, {"name": "quantity", "type": "FLOAT", "nullable": false}, {"name": "unit_price", "type": "FLOAT", "nullable": false}, {"name": "total_price", "type": "FLOAT", "nullable": false}, {"name": "date_used", "type": "DATETIME", "nullable": true}, {"name": "notes", "type": "TEXT", "nullable": true}], "row_count": 0, "size_bytes": 0, "is_empty": true}, "documents": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "title", "type": "VARCHAR(200)", "nullable": false}, {"name": "file_path", "type": "VARCHAR(500)", "nullable": false}, {"name": "file_type", "type": "VARCHAR(50)", "nullable": true}, {"name": "upload_date", "type": "DATETIME", "nullable": true}, {"name": "description", "type": "TEXT", "nullable": true}, {"name": "project_id", "type": "INTEGER", "nullable": true}, {"name": "client_id", "type": "INTEGER", "nullable": true}, {"name": "supplier_id", "type": "INTEGER", "nullable": true}], "row_count": 0, "size_bytes": 0, "is_empty": true}, "reminders": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "title", "type": "VARCHAR(100)", "nullable": false}, {"name": "description", "type": "TEXT", "nullable": true}, {"name": "reminder_date", "type": "DATETIME", "nullable": false}, {"name": "created_date", "type": "DATETIME", "nullable": true}, {"name": "is_completed", "type": "BOOLEAN", "nullable": true}, {"name": "priority", "type": "VARCHAR(20)", "nullable": true}, {"name": "user_id", "type": "INTEGER", "nullable": true}], "row_count": 1, "size_bytes": 100, "is_empty": false}, "events": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "title", "type": "VARCHAR(100)", "nullable": false}, {"name": "description", "type": "TEXT", "nullable": true}, {"name": "event_date", "type": "DATETIME", "nullable": false}, {"name": "created_date", "type": "DATETIME", "nullable": true}, {"name": "reminder_id", "type": "INTEGER", "nullable": false}, {"name": "status", "type": "VARCHAR(20)", "nullable": true}, {"name": "notes", "type": "TEXT", "nullable": true}], "row_count": 0, "size_bytes": 0, "is_empty": true}, "daily_wages": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "employee_id", "type": "INTEGER", "nullable": true}, {"name": "daily_amount", "type": "FLOAT", "nullable": false}, {"name": "work_days", "type": "FLOAT", "nullable": false}, {"name": "total_amount", "type": "FLOAT", "nullable": false}, {"name": "advance", "type": "FLOAT", "nullable": true}, {"name": "net_amount", "type": "FLOAT", "nullable": false}, {"name": "wage_date", "type": "DATETIME", "nullable": true}, {"name": "created_at", "type": "DATETIME", "nullable": true}], "row_count": 1, "size_bytes": 100, "is_empty": false}, "properties": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "title", "type": "VARCHAR(200)", "nullable": false}, {"name": "type", "type": "VARCHAR(50)", "nullable": false}, {"name": "project_id", "type": "INTEGER", "nullable": true}, {"name": "location", "type": "VARCHAR(200)", "nullable": true}, {"name": "area", "type": "FLOAT", "nullable": true}, {"name": "price", "type": "FLOAT", "nullable": true}, {"name": "status", "type": "VARCHAR(50)", "nullable": true}, {"name": "description", "type": "TEXT", "nullable": true}, {"name": "features", "type": "TEXT", "nullable": true}, {"name": "created_at", "type": "DATETIME", "nullable": true}, {"name": "updated_at", "type": "DATETIME", "nullable": true}], "row_count": 1, "size_bytes": 100, "is_empty": false}, "property_documents": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "property_id", "type": "INTEGER", "nullable": false}, {"name": "title", "type": "VARCHAR(200)", "nullable": false}, {"name": "file_path", "type": "VARCHAR(500)", "nullable": false}, {"name": "file_type", "type": "VARCHAR(50)", "nullable": true}, {"name": "is_main_image", "type": "BOOLEAN", "nullable": true}, {"name": "upload_date", "type": "DATETIME", "nullable": true}, {"name": "description", "type": "TEXT", "nullable": true}], "row_count": 1, "size_bytes": 100, "is_empty": false}, "client_phones": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "client_id", "type": "INTEGER", "nullable": false}, {"name": "phone_number", "type": "VARCHAR(20)", "nullable": false}, {"name": "label", "type": "VARCHAR(50)", "nullable": true}, {"name": "is_primary", "type": "BOOLEAN", "nullable": true}], "row_count": 2, "size_bytes": 200, "is_empty": false}, "supplier_phones": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "supplier_id", "type": "INTEGER", "nullable": false}, {"name": "phone_number", "type": "VARCHAR(20)", "nullable": false}, {"name": "label", "type": "VARCHAR(50)", "nullable": true}, {"name": "is_primary", "type": "BOOLEAN", "nullable": true}], "row_count": 0, "size_bytes": 0, "is_empty": true}, "employee_phones": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "employee_id", "type": "INTEGER", "nullable": false}, {"name": "phone_number", "type": "VARCHAR(20)", "nullable": false}, {"name": "label", "type": "VARCHAR(50)", "nullable": true}, {"name": "is_primary", "type": "BOOLEAN", "nullable": true}], "row_count": 0, "size_bytes": 0, "is_empty": true}, "purchases": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "purchase_number", "type": "VARCHAR(50)", "nullable": true}, {"name": "supplier_id", "type": "INTEGER", "nullable": true}, {"name": "date", "type": "DATETIME", "nullable": true}, {"name": "total_amount", "type": "FLOAT", "nullable": true}, {"name": "paid_amount", "type": "FLOAT", "nullable": true}, {"name": "status", "type": "VARCHAR(20)", "nullable": true}, {"name": "notes", "type": "TEXT", "nullable": true}, {"name": "created_at", "type": "DATETIME", "nullable": true}], "row_count": 1, "size_bytes": 100, "is_empty": false}, "purchase_items": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "purchase_id", "type": "INTEGER", "nullable": true}, {"name": "inventory_id", "type": "INTEGER", "nullable": true}, {"name": "quantity", "type": "FLOAT", "nullable": false}, {"name": "unit_price", "type": "FLOAT", "nullable": false}, {"name": "total_price", "type": "FLOAT", "nullable": false}, {"name": "received_quantity", "type": "FLOAT", "nullable": true}, {"name": "notes", "type": "TEXT", "nullable": true}], "row_count": 1, "size_bytes": 100, "is_empty": false}, "sales": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "sale_number", "type": "VARCHAR(50)", "nullable": true}, {"name": "client_id", "type": "INTEGER", "nullable": true}, {"name": "date", "type": "DATETIME", "nullable": true}, {"name": "total_amount", "type": "FLOAT", "nullable": true}, {"name": "paid_amount", "type": "FLOAT", "nullable": true}, {"name": "discount_amount", "type": "FLOAT", "nullable": true}, {"name": "tax_amount", "type": "FLOAT", "nullable": true}, {"name": "status", "type": "VARCHAR(20)", "nullable": true}, {"name": "payment_method", "type": "VARCHAR(50)", "nullable": true}, {"name": "notes", "type": "TEXT", "nullable": true}, {"name": "created_at", "type": "DATETIME", "nullable": true}], "row_count": 1, "size_bytes": 100, "is_empty": false}, "sale_items": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false}, {"name": "sale_id", "type": "INTEGER", "nullable": true}, {"name": "inventory_id", "type": "INTEGER", "nullable": true}, {"name": "quantity", "type": "FLOAT", "nullable": false}, {"name": "unit_price", "type": "FLOAT", "nullable": false}, {"name": "total_price", "type": "FLOAT", "nullable": false}, {"name": "discount_amount", "type": "FLOAT", "nullable": true}, {"name": "notes", "type": "TEXT", "nullable": true}], "row_count": 1, "size_bytes": 100, "is_empty": false}}, "unused_columns": [{"table": "users", "column": "email", "reason": "جميع القيم NULL"}, {"table": "users", "column": "phone", "reason": "جميع القيم NULL"}, {"table": "expenses", "column": "supplier_id", "reason": "جميع القيم NULL"}, {"table": "revenues", "column": "invoice_id", "reason": "جميع القيم NULL"}, {"table": "reminders", "column": "user_id", "reason": "جميع القيم NULL"}, {"table": "properties", "column": "project_id", "reason": "جميع القيم NULL"}], "empty_tables": ["salaries", "project_expenses", "project_materials", "documents", "events", "supplier_phones", "employee_phones"], "optimization_suggestions": [{"type": "drop_empty_table", "table": "salaries", "reason": "الجدول فارغ ولا يحتوي على بيانات", "sql": "DROP TABLE IF EXISTS salaries"}, {"type": "drop_empty_table", "table": "project_expenses", "reason": "الجدول فارغ ولا يحتوي على بيانات", "sql": "DROP TABLE IF EXISTS project_expenses"}, {"type": "drop_empty_table", "table": "project_materials", "reason": "الجدول فارغ ولا يحتوي على بيانات", "sql": "DROP TABLE IF EXISTS project_materials"}, {"type": "drop_empty_table", "table": "documents", "reason": "الجدول فارغ ولا يحتوي على بيانات", "sql": "DROP TABLE IF EXISTS documents"}, {"type": "drop_empty_table", "table": "events", "reason": "الجدول فارغ ولا يحتوي على بيانات", "sql": "DROP TABLE IF EXISTS events"}, {"type": "drop_empty_table", "table": "supplier_phones", "reason": "الجدول فارغ ولا يحتوي على بيانات", "sql": "DROP TABLE IF EXISTS supplier_phones"}, {"type": "drop_empty_table", "table": "employee_phones", "reason": "الجدول فارغ ولا يحتوي على بيانات", "sql": "DROP TABLE IF EXISTS employee_phones"}, {"type": "drop_unused_column", "table": "users", "column": "email", "reason": "جميع القيم NULL", "sql": "ALTER TABLE users DROP COLUMN email"}, {"type": "drop_unused_column", "table": "users", "column": "phone", "reason": "جميع القيم NULL", "sql": "ALTER TABLE users DROP COLUMN phone"}, {"type": "drop_unused_column", "table": "expenses", "column": "supplier_id", "reason": "جميع القيم NULL", "sql": "ALTER TABLE expenses DROP COLUMN supplier_id"}, {"type": "drop_unused_column", "table": "revenues", "column": "invoice_id", "reason": "جميع القيم NULL", "sql": "ALTER TABLE revenues DROP COLUMN invoice_id"}, {"type": "drop_unused_column", "table": "reminders", "column": "user_id", "reason": "جميع القيم NULL", "sql": "ALTER TABLE reminders DROP COLUMN user_id"}, {"type": "drop_unused_column", "table": "properties", "column": "project_id", "reason": "جميع القيم NULL", "sql": "ALTER TABLE properties DROP COLUMN project_id"}, {"type": "vacuum_database", "reason": "تنظيف وضغط قاعدة البيانات", "sql": "VACUUM"}, {"type": "analyze_database", "reason": "تحديث إحصائيات قاعدة البيانات", "sql": "ANALYZE"}], "statistics": {"indexes_count": 0, "common_query_patterns": 4}}, "optimizations_applied": [], "summary": {"total_tables": 28, "empty_tables": 7, "unused_columns": 6, "optimization_suggestions": 15, "optimizations_applied": 0}}