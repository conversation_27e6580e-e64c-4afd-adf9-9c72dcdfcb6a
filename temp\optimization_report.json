{"timestamp": "2025-06-27T15:09:51.808934", "total_optimizations": 1798, "optimizations_by_type": {"Import organization": 62, "Removed trailing whitespace": 496, "Duplicate comment detected": 944, "Removed redundant empty line": 296}, "optimizations_by_file": {".\\advanced_analyzer.py": 45, ".\\auto_optimizer.py": 60, ".\\database.py": 3, ".\\error_handler.py": 38, ".\\main.py": 1, ".\\pattern_detector.py": 44, ".\\performance_config.py": 3, ".\\performance_optimizer.py": 4, ".\\update_database.py": 8, ".\\update_suppliers_table.py": 30, ".\\utils.py": 15, ".\\backup_20250627_150947\\advanced_analyzer.py": 45, ".\\backup_20250627_150947\\auto_optimizer.py": 60, ".\\backup_20250627_150947\\database.py": 3, ".\\backup_20250627_150947\\error_handler.py": 38, ".\\backup_20250627_150947\\main.py": 1, ".\\backup_20250627_150947\\pattern_detector.py": 44, ".\\backup_20250627_150947\\performance_config.py": 3, ".\\backup_20250627_150947\\performance_optimizer.py": 4, ".\\backup_20250627_150947\\update_database.py": 8, ".\\backup_20250627_150947\\update_suppliers_table.py": 30, ".\\backup_20250627_150947\\utils.py": 15, ".\\backup_20250627_150947\\ui\\clean_dashboard.py": 4, ".\\backup_20250627_150947\\ui\\clients.py": 211, ".\\backup_20250627_150947\\ui\\dialogs.py": 9, ".\\backup_20250627_150947\\ui\\employees.py": 144, ".\\backup_20250627_150947\\ui\\expenses.py": 6, ".\\backup_20250627_150947\\ui\\inventory.py": 19, ".\\backup_20250627_150947\\ui\\invoices.py": 35, ".\\backup_20250627_150947\\ui\\main_window.py": 48, ".\\backup_20250627_150947\\ui\\notifications.py": 3, ".\\backup_20250627_150947\\ui\\projects.py": 5, ".\\backup_20250627_150947\\ui\\properties.py": 22, ".\\backup_20250627_150947\\ui\\purchases.py": 11, ".\\backup_20250627_150947\\ui\\reminders.py": 8, ".\\backup_20250627_150947\\ui\\reports.py": 38, ".\\backup_20250627_150947\\ui\\revenues.py": 7, ".\\backup_20250627_150947\\ui\\sales.py": 11, ".\\backup_20250627_150947\\ui\\saved_status_bar.py": 2, ".\\backup_20250627_150947\\ui\\suppliers.py": 61, ".\\backup_20250627_150947\\ui\\unified_dropdown_styles.py": 3, ".\\backup_20250627_150947\\ui\\unified_styles.py": 1, ".\\ui\\clean_dashboard.py": 4, ".\\ui\\clients.py": 211, ".\\ui\\dialogs.py": 9, ".\\ui\\employees.py": 144, ".\\ui\\expenses.py": 6, ".\\ui\\inventory.py": 19, ".\\ui\\invoices.py": 35, ".\\ui\\main_window.py": 48, ".\\ui\\notifications.py": 3, ".\\ui\\projects.py": 5, ".\\ui\\properties.py": 22, ".\\ui\\purchases.py": 11, ".\\ui\\reminders.py": 8, ".\\ui\\reports.py": 38, ".\\ui\\revenues.py": 7, ".\\ui\\sales.py": 11, ".\\ui\\saved_status_bar.py": 2, ".\\ui\\suppliers.py": 61, ".\\ui\\unified_dropdown_styles.py": 3, ".\\ui\\unified_styles.py": 1}, "details": [{"type": "Import organization", "file": ".\\advanced_analyzer.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 19, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 33, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 37, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 39, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 43, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 48, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 51, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 54, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 62, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 68, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 72, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 76, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 80, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 84, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 87, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 100, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 111, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 120, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 128, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 135, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 137, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 144, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 149, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 159, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 169, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 182, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 184, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 189, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 192, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 198, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 206, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 208, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 216, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 225, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 230, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 232, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 236, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 246, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 254, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 264, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 266, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 280, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 283, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\advanced_analyzer.py", "line": 291, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Import organization", "file": ".\\auto_optimizer.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 18, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 22, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 26, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 29, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 31, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 35, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 40, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 43, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 47, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 53, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 62, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 66, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 69, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 73, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 76, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 79, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 82, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 85, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 90, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 93, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 100, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 105, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 124, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 137, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 139, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 142, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 146, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 153, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 155, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 160, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 164, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 180, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 190, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 198, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 200, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 204, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 207, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 214, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 221, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 229, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 234, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 246, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 253, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 256, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 263, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 265, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 272, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 274, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 276, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 280, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 293, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 295, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 305, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 313, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 321, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 324, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 330, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 333, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\auto_optimizer.py", "line": 339, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Duplicate comment detected", "file": ".\\database.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\database.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\database.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Duplicate comment detected", "file": ".\\error_handler.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\error_handler.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 20, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 22, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 27, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 35, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 38, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 44, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 49, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 54, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 60, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 62, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 65, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 68, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 75, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 79, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 82, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 85, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 91, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 93, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 96, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 99, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 106, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 108, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 111, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 117, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 131, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 136, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 152, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 160, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 164, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 169, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 174, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 180, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 183, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 188, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 204, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\error_handler.py", "line": 209, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Import organization", "file": ".\\main.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Import organization", "file": ".\\pattern_detector.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 21, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 31, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 35, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 39, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 43, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 50, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 57, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 60, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 68, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 73, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 77, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 90, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 102, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 104, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 110, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 120, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 124, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 128, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 132, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 141, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 147, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 156, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 160, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 164, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 174, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 179, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 182, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 187, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 192, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 198, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 204, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 209, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 219, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 228, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 231, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 245, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 248, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 251, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 254, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 264, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 273, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 277, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\pattern_detector.py", "line": 281, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Import organization", "file": ".\\performance_config.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed trailing whitespace", "file": ".\\performance_config.py", "line": 72, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\performance_config.py", "line": 79, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Duplicate comment detected", "file": ".\\performance_optimizer.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\performance_optimizer.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\performance_optimizer.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\performance_optimizer.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Import organization", "file": ".\\update_database.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed trailing whitespace", "file": ".\\update_database.py", "line": 16, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_database.py", "line": 20, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_database.py", "line": 25, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_database.py", "line": 29, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_database.py", "line": 36, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_database.py", "line": 40, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_database.py", "line": 43, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Import organization", "file": ".\\update_suppliers_table.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 17, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 19, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 23, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 28, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 30, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 35, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 37, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 45, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 50, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 53, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 54, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 59, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 63, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 66, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 69, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 73, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 76, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 81, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 83, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 86, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 93, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 97, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 101, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 104, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 107, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 110, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 118, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 125, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\update_suppliers_table.py", "line": 131, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Duplicate comment detected", "file": ".\\utils.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\utils.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\utils.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\utils.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\utils.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\utils.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\utils.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\utils.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\utils.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\utils.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\utils.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\utils.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\utils.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\utils.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\utils.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 19, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 33, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 37, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 39, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 43, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 48, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 51, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 54, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 62, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 68, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 72, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 76, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 80, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 84, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 87, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 100, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 111, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 120, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 128, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 135, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 137, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 144, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 149, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 159, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 169, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 182, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 184, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 189, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 192, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 198, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 206, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 208, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 216, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 225, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 230, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 232, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 236, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 246, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 254, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 264, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 266, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 280, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 283, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\advanced_analyzer.py", "line": 291, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 18, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 22, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 26, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 29, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 31, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 35, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 40, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 43, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 47, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 53, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 62, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 66, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 69, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 73, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 76, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 79, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 82, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 85, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 90, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 93, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 100, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 105, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 124, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 137, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 139, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 142, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 146, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 153, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 155, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 160, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 164, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 180, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 190, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 198, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 200, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 204, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 207, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 214, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 221, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 229, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 234, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 246, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 253, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 256, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 263, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 265, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 272, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 274, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 276, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 280, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 293, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 295, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 305, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 313, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 321, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 324, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 330, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 333, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\auto_optimizer.py", "line": 339, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\database.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\database.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\database.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\error_handler.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\error_handler.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 20, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 22, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 27, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 35, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 38, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 44, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 49, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 54, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 60, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 62, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 65, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 68, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 75, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 79, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 82, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 85, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 91, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 93, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 96, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 99, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 106, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 108, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 111, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 117, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 131, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 136, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 152, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 160, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 164, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 169, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 174, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 180, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 183, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 188, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 204, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\error_handler.py", "line": 209, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\main.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\pattern_detector.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 21, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 31, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 35, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 39, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 43, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 50, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 57, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 60, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 68, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 73, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 77, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 90, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 102, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 104, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 110, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 120, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 124, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 128, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 132, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 141, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 147, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 156, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 160, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 164, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 174, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 179, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 182, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 187, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 192, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 198, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 204, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 209, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 219, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 228, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 231, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 245, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 248, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 251, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 254, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 264, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 273, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 277, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\pattern_detector.py", "line": 281, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\performance_config.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\performance_config.py", "line": 72, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\performance_config.py", "line": 79, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\performance_optimizer.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\performance_optimizer.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\performance_optimizer.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\performance_optimizer.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\update_database.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_database.py", "line": 16, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_database.py", "line": 20, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_database.py", "line": 25, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_database.py", "line": 29, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_database.py", "line": 36, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_database.py", "line": 40, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_database.py", "line": 43, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 17, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 19, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 23, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 28, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 30, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 35, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 37, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 45, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 50, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 53, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 54, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 59, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 63, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 66, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 69, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 73, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 76, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 81, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 83, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 86, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 93, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 97, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 101, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 104, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 107, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 110, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 118, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 125, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\update_suppliers_table.py", "line": 131, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\utils.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\utils.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\utils.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\utils.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\utils.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\utils.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\utils.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\utils.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\utils.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\utils.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\utils.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\utils.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\utils.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\utils.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\utils.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clean_dashboard.py", "line": 12, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clean_dashboard.py", "line": 449, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clean_dashboard.py", "line": 450, "description": "إزالة سطر فارغ مكرر"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\ui\\clean_dashboard.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 3499, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 3500, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 3525, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 3526, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 4739, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 4740, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 5299, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 5300, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 5301, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 5302, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 5303, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 5304, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 5714, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 5715, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 5716, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 5717, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 5823, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 5824, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 5825, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 5826, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 5998, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 5999, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 6474, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 6475, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 6507, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 6508, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 7294, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 7295, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 7325, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 7326, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 7838, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 7839, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 8189, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 8190, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 8191, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 8192, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 8244, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 8245, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 8246, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 8247, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 8248, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 8249, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 8250, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 8251, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 8252, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 8253, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 8254, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 8255, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 8256, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 8257, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 8258, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 8259, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 8346, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 8347, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 9412, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 9413, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 9414, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 9415, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 10415, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 10416, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 12096, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 12097, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 12771, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 12772, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\clients.py", "line": 13202, "description": "إزالة سطر فارغ مكرر"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 6 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 8 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\ui\\clients.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\ui\\dialogs.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\dialogs.py", "line": 71, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\dialogs.py", "line": 75, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\dialogs.py", "line": 195, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\dialogs.py", "line": 208, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\dialogs.py", "line": 210, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\dialogs.py", "line": 215, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\dialogs.py", "line": 219, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\dialogs.py", "line": 226, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 296, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 297, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 488, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 489, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 551, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 552, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 563, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 564, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 628, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 629, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 694, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 695, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 1159, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 1160, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 1292, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 1293, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 2018, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 2019, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 2020, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 2021, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 4570, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 4571, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 6098, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 6099, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 8536, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 8763, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\employees.py", "line": 8940, "description": "إزالة سطر فارغ مكرر"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 7 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 6 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 8 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 10 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 8 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\ui\\employees.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\expenses.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\expenses.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\expenses.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\expenses.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\expenses.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\ui\\expenses.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\inventory.py", "line": 1861, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\inventory.py", "line": 1862, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\inventory.py", "line": 1987, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\inventory.py", "line": 1988, "description": "إزالة سطر فارغ مكرر"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\inventory.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\inventory.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\inventory.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\inventory.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\inventory.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\inventory.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\inventory.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\inventory.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\inventory.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\inventory.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\inventory.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\inventory.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\inventory.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\inventory.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\ui\\inventory.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\ui\\invoices.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 12, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 24, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 427, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 428, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 1076, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 1077, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 1263, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 1264, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 1917, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 1918, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 2037, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 2038, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 2196, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 2197, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 2276, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 2277, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 2278, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 2279, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 2280, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 2281, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 2282, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 2283, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 2763, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 2764, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 2765, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 2766, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 3137, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 3138, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 3139, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 3140, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 3150, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "line": 3151, "description": "إزالة سطر فارغ مكرر"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "description": "تعليق مكرر في 6 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "description": "تعليق مكرر في 8 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\ui\\main_window.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\notifications.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\notifications.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\ui\\notifications.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\projects.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\projects.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\projects.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\projects.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\ui\\projects.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\properties.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\properties.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\ui\\properties.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\ui\\purchases.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\purchases.py", "line": 162, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\purchases.py", "line": 310, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\purchases.py", "line": 313, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\purchases.py", "line": 321, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\purchases.py", "line": 324, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\purchases.py", "line": 330, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\purchases.py", "line": 332, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\purchases.py", "line": 399, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\purchases.py", "line": 406, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\purchases.py", "line": 408, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reminders.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reminders.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reminders.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reminders.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reminders.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reminders.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reminders.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\ui\\reminders.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\reports.py", "line": 4780, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\reports.py", "line": 4781, "description": "إزالة سطر فارغ مكرر"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 9 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 9 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 9 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 8 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 9 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 6 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 6 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 7 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 6 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 6 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\ui\\reports.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\revenues.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\revenues.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\revenues.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\revenues.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\revenues.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\revenues.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\ui\\revenues.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\ui\\sales.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\sales.py", "line": 162, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\sales.py", "line": 310, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\sales.py", "line": 313, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\sales.py", "line": 321, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\sales.py", "line": 324, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\sales.py", "line": 330, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\sales.py", "line": 332, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\sales.py", "line": 399, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\sales.py", "line": 406, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\backup_20250627_150947\\ui\\sales.py", "line": 408, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\saved_status_bar.py", "line": 9, "description": "إزالة سطر فارغ مكرر"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\ui\\saved_status_bar.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "line": 29, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "line": 30, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "line": 1414, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "line": 1415, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "line": 1456, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "line": 1457, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "line": 1458, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "line": 1459, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "line": 4494, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "line": 4495, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "line": 6882, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "line": 6883, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "line": 7108, "description": "إزالة سطر فارغ مكرر"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 8 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 8 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 6 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\ui\\suppliers.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed redundant empty line", "file": ".\\backup_20250627_150947\\ui\\unified_dropdown_styles.py", "line": 10, "description": "إزالة سطر فارغ مكرر"}, {"type": "Duplicate comment detected", "file": ".\\backup_20250627_150947\\ui\\unified_dropdown_styles.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\ui\\unified_dropdown_styles.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Import organization", "file": ".\\backup_20250627_150947\\ui\\unified_styles.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clean_dashboard.py", "line": 12, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clean_dashboard.py", "line": 449, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clean_dashboard.py", "line": 450, "description": "إزالة سطر فارغ مكرر"}, {"type": "Import organization", "file": ".\\ui\\clean_dashboard.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 3499, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 3500, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 3525, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 3526, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 4739, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 4740, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 5299, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 5300, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 5301, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 5302, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 5303, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 5304, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 5714, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 5715, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 5716, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 5717, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 5823, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 5824, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 5825, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 5826, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 5998, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 5999, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 6474, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 6475, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 6507, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 6508, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 7294, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 7295, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 7325, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 7326, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 7838, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 7839, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 8189, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 8190, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 8191, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 8192, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 8244, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 8245, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 8246, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 8247, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 8248, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 8249, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 8250, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 8251, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 8252, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 8253, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 8254, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 8255, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 8256, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 8257, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 8258, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 8259, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 8346, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 8347, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 9412, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 9413, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 9414, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 9415, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 10415, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 10416, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 12096, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 12097, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 12771, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 12772, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\clients.py", "line": 13202, "description": "إزالة سطر فارغ مكرر"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 6 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 8 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\clients.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\ui\\clients.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Import organization", "file": ".\\ui\\dialogs.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\dialogs.py", "line": 71, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\dialogs.py", "line": 75, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\dialogs.py", "line": 195, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\dialogs.py", "line": 208, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\dialogs.py", "line": 210, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\dialogs.py", "line": 215, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\dialogs.py", "line": 219, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\dialogs.py", "line": 226, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 296, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 297, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 488, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 489, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 551, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 552, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 563, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 564, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 628, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 629, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 694, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 695, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 1159, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 1160, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 1292, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 1293, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 2018, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 2019, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 2020, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 2021, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 4570, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 4571, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 6098, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 6099, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 8536, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 8763, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\employees.py", "line": 8940, "description": "إزالة سطر فارغ مكرر"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 7 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 6 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 8 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 10 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 8 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\employees.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\ui\\employees.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Duplicate comment detected", "file": ".\\ui\\expenses.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\expenses.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\expenses.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\expenses.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\expenses.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\ui\\expenses.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed redundant empty line", "file": ".\\ui\\inventory.py", "line": 1861, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\inventory.py", "line": 1862, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\inventory.py", "line": 1987, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\inventory.py", "line": 1988, "description": "إزالة سطر فارغ مكرر"}, {"type": "Duplicate comment detected", "file": ".\\ui\\inventory.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\inventory.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\inventory.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\inventory.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\inventory.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\inventory.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\inventory.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\inventory.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\inventory.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\inventory.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\inventory.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\inventory.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\inventory.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\inventory.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\ui\\inventory.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\invoices.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\ui\\invoices.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 12, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 24, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 427, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 428, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 1076, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 1077, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 1263, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 1264, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 1917, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 1918, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 2037, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 2038, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 2196, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 2197, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 2276, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 2277, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 2278, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 2279, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 2280, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 2281, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 2282, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 2283, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 2763, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 2764, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 2765, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 2766, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 3137, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 3138, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 3139, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 3140, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 3150, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\main_window.py", "line": 3151, "description": "إزالة سطر فارغ مكرر"}, {"type": "Duplicate comment detected", "file": ".\\ui\\main_window.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\main_window.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\main_window.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\main_window.py", "description": "تعليق مكرر في 6 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\main_window.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\main_window.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\main_window.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\main_window.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\main_window.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\main_window.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\main_window.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\main_window.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\main_window.py", "description": "تعليق مكرر في 8 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\main_window.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\main_window.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\ui\\main_window.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Duplicate comment detected", "file": ".\\ui\\notifications.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\notifications.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\ui\\notifications.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Duplicate comment detected", "file": ".\\ui\\projects.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\projects.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\projects.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\projects.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\ui\\projects.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Duplicate comment detected", "file": ".\\ui\\properties.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\properties.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\properties.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\ui\\properties.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Import organization", "file": ".\\ui\\purchases.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\purchases.py", "line": 162, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\purchases.py", "line": 310, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\purchases.py", "line": 313, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\purchases.py", "line": 321, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\purchases.py", "line": 324, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\purchases.py", "line": 330, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\purchases.py", "line": 332, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\purchases.py", "line": 399, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\purchases.py", "line": 406, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\purchases.py", "line": 408, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reminders.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reminders.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reminders.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reminders.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reminders.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reminders.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reminders.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\ui\\reminders.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed redundant empty line", "file": ".\\ui\\reports.py", "line": 4780, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\reports.py", "line": 4781, "description": "إزالة سطر فارغ مكرر"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 9 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 9 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 9 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 8 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 9 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 6 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 6 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 7 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 6 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 6 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\reports.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\ui\\reports.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Duplicate comment detected", "file": ".\\ui\\revenues.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\revenues.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\revenues.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\revenues.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\revenues.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\revenues.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\ui\\revenues.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Import organization", "file": ".\\ui\\sales.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\sales.py", "line": 162, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\sales.py", "line": 310, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\sales.py", "line": 313, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\sales.py", "line": 321, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\sales.py", "line": 324, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\sales.py", "line": 330, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\sales.py", "line": 332, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\sales.py", "line": 399, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\sales.py", "line": 406, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed trailing whitespace", "file": ".\\ui\\sales.py", "line": 408, "description": "إزالة مسافات في نهاية السطر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\saved_status_bar.py", "line": 9, "description": "إزالة سطر فارغ مكرر"}, {"type": "Import organization", "file": ".\\ui\\saved_status_bar.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed redundant empty line", "file": ".\\ui\\suppliers.py", "line": 29, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\suppliers.py", "line": 30, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\suppliers.py", "line": 1414, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\suppliers.py", "line": 1415, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\suppliers.py", "line": 1456, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\suppliers.py", "line": 1457, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\suppliers.py", "line": 1458, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\suppliers.py", "line": 1459, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\suppliers.py", "line": 4494, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\suppliers.py", "line": 4495, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\suppliers.py", "line": 6882, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\suppliers.py", "line": 6883, "description": "إزالة سطر فارغ مكرر"}, {"type": "Removed redundant empty line", "file": ".\\ui\\suppliers.py", "line": 7108, "description": "إزالة سطر فارغ مكرر"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 8 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 8 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 5 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 4 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 6 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 3 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Duplicate comment detected", "file": ".\\ui\\suppliers.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\ui\\suppliers.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Removed redundant empty line", "file": ".\\ui\\unified_dropdown_styles.py", "line": 10, "description": "إزالة سطر فارغ مكرر"}, {"type": "Duplicate comment detected", "file": ".\\ui\\unified_dropdown_styles.py", "description": "تعليق مكرر في 2 مواضع"}, {"type": "Import organization", "file": ".\\ui\\unified_dropdown_styles.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}, {"type": "Import organization", "file": ".\\ui\\unified_styles.py", "description": "تم ترتيب الاستيرادات حسب المعايير"}]}