# -*- coding: utf-8 -*-
"""
أداة تنسيق الكود التلقائي
Automatic Code Formatter Tool
"""

import ast
import os
import re
import sys
from datetime import datetime
from typing import List, Dict, Tuple

# إضافة المسار للوصول للمعايير
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from config.coding_standards import get_standard, validate_naming
    from .common_utils import find_python_files, save_json_report
except ImportError:
    # في حالة التشغيل المباشر
    sys.path.append('..')
    from config.coding_standards import get_standard, validate_naming
    from tools.common_utils import find_python_files, save_json_report


class CodeFormatter:
    """أداة تنسيق الكود حسب المعايير الموحدة"""
    
    def __init__(self):
        self.formatting_standards = get_standard('formatting')
        self.naming_standards = get_standard('naming')
        self.import_standards = get_standard('imports')
        self.issues_found = []
        self.fixes_applied = []
    
    def format_project(self, project_path="."):
        """تنسيق جميع ملفات المشروع"""
        print("🎨 بدء تنسيق المشروع...")
        
        python_files = find_python_files(project_path)
        
        for file_path in python_files:
            if self._should_skip_file(file_path):
                continue
            
            try:
                self._format_file(file_path)
            except Exception as e:
                print(f"❌ خطأ في تنسيق {file_path}: {e}")
        
        self._generate_report()
        print(f"✅ تم تنسيق {len(python_files)} ملف")
    
    def _should_skip_file(self, file_path):
        """تحديد الملفات التي يجب تجاهلها"""
        skip_patterns = ['venv', '__pycache__', '.git', 'migrations']
        return any(pattern in file_path for pattern in skip_patterns)
    
    def _format_file(self, file_path):
        """تنسيق ملف واحد"""
        print(f"🔧 تنسيق: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        # تطبيق التنسيقات المختلفة
        formatted_content = original_content
        formatted_content = self._fix_imports(formatted_content, file_path)
        formatted_content = self._fix_spacing(formatted_content, file_path)
        formatted_content = self._fix_line_length(formatted_content, file_path)
        formatted_content = self._fix_blank_lines(formatted_content, file_path)
        
        # حفظ الملف إذا تم تغييره
        if formatted_content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(formatted_content)
            self.fixes_applied.append({
                'file': file_path,
                'type': 'formatting',
                'description': 'تم تطبيق تنسيق موحد'
            })
    
    def _fix_imports(self, content, file_path):
        """تنظيم الاستيرادات"""
        lines = content.split('\n')
        import_lines = []
        other_lines = []
        
        # فصل الاستيرادات عن باقي الكود
        in_imports = True
        for line in lines:
            stripped = line.strip()
            if stripped.startswith(('import ', 'from ')) and in_imports:
                import_lines.append(line)
            elif stripped == '' and in_imports:
                continue  # تجاهل الأسطر الفارغة في منطقة الاستيرادات
            else:
                if in_imports and stripped:
                    in_imports = False
                other_lines.append(line)
        
        # تنظيم الاستيرادات
        if import_lines:
            organized_imports = self._organize_imports(import_lines)
            # إضافة سطر فارغ بعد الاستيرادات
            return '\n'.join(organized_imports + [''] + other_lines)
        
        return content
    
    def _organize_imports(self, import_lines):
        """تنظيم ترتيب الاستيرادات"""
        standard_lib = []
        third_party = []
        local_imports = []
        
        for line in import_lines:
            stripped = line.strip()
            if self._is_standard_library_import(stripped):
                standard_lib.append(line)
            elif self._is_local_import(stripped):
                local_imports.append(line)
            else:
                third_party.append(line)
        
        # ترتيب كل مجموعة أبجدياً
        standard_lib.sort()
        third_party.sort()
        local_imports.sort()
        
        # دمج المجموعات مع أسطر فارغة بينها
        organized = []
        if standard_lib:
            organized.extend(standard_lib)
            if third_party or local_imports:
                organized.append('')
        
        if third_party:
            organized.extend(third_party)
            if local_imports:
                organized.append('')
        
        if local_imports:
            organized.extend(local_imports)
        
        return organized
    
    def _is_standard_library_import(self, import_line):
        """تحديد ما إذا كان الاستيراد من المكتبة القياسية"""
        standard_modules = {
            'os', 'sys', 'json', 'time', 'datetime', 'collections', 
            'itertools', 'functools', 're', 'math', 'random', 'hashlib',
            'sqlite3', 'threading', 'multiprocessing', 'subprocess',
            'pathlib', 'typing', 'ast', 'gc', 'traceback'
        }
        
        # استخراج اسم الوحدة
        if import_line.startswith('import '):
            module = import_line.replace('import ', '').split('.')[0].split(' as ')[0]
        elif import_line.startswith('from '):
            module = import_line.replace('from ', '').split('.')[0].split(' import')[0]
        else:
            return False
        
        return module in standard_modules
    
    def _is_local_import(self, import_line):
        """تحديد ما إذا كان الاستيراد محلي"""
        local_patterns = ['from .', 'from ui.', 'from database', 'from utils', 'from tools.', 'from config.']
        return any(import_line.startswith(pattern) for pattern in local_patterns)
    
    def _fix_spacing(self, content, file_path):
        """إصلاح المسافات"""
        lines = content.split('\n')
        fixed_lines = []
        
        for line in lines:
            # إزالة المسافات الزائدة في نهاية السطر
            line = line.rstrip()
            
            # إصلاح المسافات حول العمليات
            if '=' in line and not line.strip().startswith('#'):
                line = re.sub(r'\s*=\s*', ' = ', line)
            
            # إصلاح المسافات بعد الفواصل
            line = re.sub(r',(?!\s)', ', ', line)
            
            fixed_lines.append(line)
        
        return '\n'.join(fixed_lines)
    
    def _fix_line_length(self, content, file_path):
        """إصلاح طول الأسطر الطويلة"""
        max_length = self.formatting_standards['max_line_length']
        lines = content.split('\n')
        fixed_lines = []
        
        for line in lines:
            if len(line) > max_length and not line.strip().startswith('#'):
                # تسجيل المشكلة
                self.issues_found.append({
                    'file': file_path,
                    'type': 'long_line',
                    'line': line,
                    'length': len(line),
                    'max_length': max_length
                })
            fixed_lines.append(line)
        
        return '\n'.join(fixed_lines)
    
    def _fix_blank_lines(self, content, file_path):
        """إصلاح الأسطر الفارغة"""
        lines = content.split('\n')
        fixed_lines = []
        prev_line_empty = False
        consecutive_empty = 0
        
        for line in lines:
            is_empty = line.strip() == ''
            
            if is_empty:
                consecutive_empty += 1
                # السماح بحد أقصى سطرين فارغين متتاليين
                if consecutive_empty <= 2:
                    fixed_lines.append(line)
            else:
                consecutive_empty = 0
                fixed_lines.append(line)
            
            prev_line_empty = is_empty
        
        return '\n'.join(fixed_lines)
    
    def _generate_report(self):
        """إنتاج تقرير التنسيق"""
        report = {
            'timestamp': str(datetime.now()),
            'summary': {
                'files_processed': len(self.fixes_applied),
                'issues_found': len(self.issues_found),
                'fixes_applied': len(self.fixes_applied)
            },
            'issues': self.issues_found,
            'fixes': self.fixes_applied
        }
        
        save_json_report(report, 'temp/formatting_report.json', 'تقرير التنسيق')
        return report


if __name__ == "__main__":
    formatter = CodeFormatter()
    formatter.format_project()
    
    print("\n🎨 ملخص التنسيق:")
    print(f"📁 الملفات المعالجة: {len(formatter.fixes_applied)}")
    print(f"⚠️ المشاكل المكتشفة: {len(formatter.issues_found)}")
    print(f"✅ الإصلاحات المطبقة: {len(formatter.fixes_applied)}")
