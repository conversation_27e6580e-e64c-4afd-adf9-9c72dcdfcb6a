# -*- coding: utf-8 -*-
"""
أدوات مشتركة للأدوات المساعدة
Common utilities for tools
"""

import json
import os


def find_python_files(path="."):
    """البحث عن جميع ملفات Python في المسار المحدد"""
    python_files = []
    for root, dirs, files in os.walk(path):
        # تجاهل مجلدات معينة
        dirs[:] = [d for d in dirs if d not in ['venv', '__pycache__', '.git']]
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    return python_files


def save_json_report(report, output_file, description="التقرير"):
    """حفظ تقرير JSON مع معالجة الأخطاء"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        print(f"📊 تم حفظ {description} في: {output_file}")
        return True
    except Exception as e:
        print(f"❌ خطأ في حفظ {description}: {str(e)}")
        return False


def load_json_file(file_path):
    """تحميل ملف JSON مع معالجة الأخطاء"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ خطأ في تحميل الملف {file_path}: {str(e)}")
        return None


def ensure_directory_exists(directory_path):
    """التأكد من وجود المجلد وإنشاؤه إذا لم يكن موجوداً"""
    try:
        os.makedirs(directory_path, exist_ok=True)
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء المجلد {directory_path}: {str(e)}")
        return False
