# -*- coding: utf-8 -*-
"""
أداة تنظيف وتحسين قاعدة البيانات
Database Cleaning and Optimization Tool
"""

import sqlite3
import os
from datetime import datetime
from typing import Dict, List, Tuple

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

try:
    from tools.common_utils import save_json_report
except ImportError:
    # تعريف دالة بديلة بسيطة
    import json
    def save_json_report(report, output_file, description="التقرير"):
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print(f"📊 تم حفظ {description} في: {output_file}")
            return True
        except Exception as e:
            print(f"❌ خطأ في حفظ {description}: {str(e)}")
            return False


class DatabaseCleaner:
    """أداة تنظيف وتحسين قاعدة البيانات"""
    
    def __init__(self, db_path='accounting.db'):
        self.db_path = db_path
        self.analysis_results = {
            'tables': {},
            'unused_columns': [],
            'empty_tables': [],
            'optimization_suggestions': [],
            'statistics': {}
        }
        self.optimizations_applied = []
    
    def analyze_database(self):
        """تحليل شامل لقاعدة البيانات"""
        print("🔍 بدء تحليل قاعدة البيانات...")
        
        if not os.path.exists(self.db_path):
            print(f"❌ قاعدة البيانات غير موجودة: {self.db_path}")
            return
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # تحليل الجداول
                self._analyze_tables(cursor)
                
                # تحليل الفهارس
                self._analyze_indexes(cursor)
                
                # تحليل الاستعلامات الشائعة
                self._analyze_common_queries(cursor)
                
                # إنتاج اقتراحات التحسين
                self._generate_optimization_suggestions()
                
            print("✅ تم تحليل قاعدة البيانات بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في تحليل قاعدة البيانات: {str(e)}")
    
    def _analyze_tables(self, cursor):
        """تحليل الجداول وأعمدتها"""
        # الحصول على قائمة الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        for (table_name,) in tables:
            if table_name.startswith('sqlite_'):
                continue
            
            # تحليل بنية الجدول
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            # عدد الصفوف
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            row_count = cursor.fetchone()[0]
            
            # حجم الجدول (تقريبي) - استعلام مبسط
            try:
                cursor.execute(f"SELECT COUNT(*) * 100 FROM {table_name}")  # تقدير تقريبي
                size_result = cursor.fetchone()[0]
                table_size = size_result if size_result else 0
            except:
                table_size = 0
            
            self.analysis_results['tables'][table_name] = {
                'columns': [{'name': col[1], 'type': col[2], 'nullable': not col[3]} for col in columns],
                'row_count': row_count,
                'size_bytes': table_size,
                'is_empty': row_count == 0
            }
            
            # تحديد الجداول الفارغة
            if row_count == 0:
                self.analysis_results['empty_tables'].append(table_name)
            
            # تحليل استخدام الأعمدة
            self._analyze_column_usage(cursor, table_name, columns)
    
    def _analyze_column_usage(self, cursor, table_name, columns):
        """تحليل استخدام الأعمدة"""
        for col_info in columns:
            col_name = col_info[1]
            
            # فحص الأعمدة التي تحتوي على قيم NULL فقط
            cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE {col_name} IS NOT NULL")
            non_null_count = cursor.fetchone()[0]
            
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            total_count = cursor.fetchone()[0]
            
            if total_count > 0 and non_null_count == 0:
                self.analysis_results['unused_columns'].append({
                    'table': table_name,
                    'column': col_name,
                    'reason': 'جميع القيم NULL'
                })
            
            # فحص الأعمدة التي تحتوي على قيمة واحدة فقط
            if non_null_count > 0:
                cursor.execute(f"SELECT COUNT(DISTINCT {col_name}) FROM {table_name} WHERE {col_name} IS NOT NULL")
                distinct_count = cursor.fetchone()[0]
                
                if distinct_count == 1 and non_null_count > 10:  # إذا كان هناك أكثر من 10 صفوف
                    self.analysis_results['unused_columns'].append({
                        'table': table_name,
                        'column': col_name,
                        'reason': 'قيمة واحدة فقط في جميع الصفوف'
                    })
    
    def _analyze_indexes(self, cursor):
        """تحليل الفهارس"""
        cursor.execute("SELECT name, tbl_name, sql FROM sqlite_master WHERE type='index' AND sql IS NOT NULL")
        indexes = cursor.fetchall()
        
        self.analysis_results['statistics']['indexes_count'] = len(indexes)
        
        # اقتراح فهارس جديدة للأعمدة المستخدمة كثيراً
        common_search_columns = ['name', 'email', 'phone', 'created_at', 'client_id', 'supplier_id']
        
        for table_name, table_info in self.analysis_results['tables'].items():
            for column in table_info['columns']:
                col_name = column['name']
                if col_name in common_search_columns:
                    # فحص وجود فهرس لهذا العمود
                    index_exists = any(table_name in idx[1] and col_name in (idx[2] or '') for idx in indexes)
                    
                    if not index_exists and table_info['row_count'] > 100:
                        self.analysis_results['optimization_suggestions'].append({
                            'type': 'create_index',
                            'table': table_name,
                            'column': col_name,
                            'reason': f'العمود {col_name} يُستخدم كثيراً في البحث',
                            'sql': f'CREATE INDEX idx_{table_name}_{col_name} ON {table_name}({col_name})'
                        })
    
    def _analyze_common_queries(self, cursor):
        """تحليل الاستعلامات الشائعة"""
        # هذه دالة تحليلية - في التطبيق الحقيقي يمكن تسجيل الاستعلامات وتحليلها
        common_patterns = [
            "SELECT * FROM clients WHERE name LIKE '%?%'",
            "SELECT * FROM invoices WHERE client_id = ?",
            "SELECT * FROM expenses WHERE supplier_id = ?",
            "SELECT * FROM projects WHERE client_id = ?",
        ]
        
        self.analysis_results['statistics']['common_query_patterns'] = len(common_patterns)
    
    def _generate_optimization_suggestions(self):
        """إنتاج اقتراحات التحسين"""
        suggestions = self.analysis_results['optimization_suggestions']
        
        # اقتراح حذف الجداول الفارغة
        for table in self.analysis_results['empty_tables']:
            suggestions.append({
                'type': 'drop_empty_table',
                'table': table,
                'reason': 'الجدول فارغ ولا يحتوي على بيانات',
                'sql': f'DROP TABLE IF EXISTS {table}'
            })
        
        # اقتراح حذف الأعمدة غير المستخدمة
        for unused_col in self.analysis_results['unused_columns']:
            suggestions.append({
                'type': 'drop_unused_column',
                'table': unused_col['table'],
                'column': unused_col['column'],
                'reason': unused_col['reason'],
                'sql': f'ALTER TABLE {unused_col["table"]} DROP COLUMN {unused_col["column"]}'
            })
        
        # اقتراح تحسين أداء قاعدة البيانات
        suggestions.append({
            'type': 'vacuum_database',
            'reason': 'تنظيف وضغط قاعدة البيانات',
            'sql': 'VACUUM'
        })
        
        suggestions.append({
            'type': 'analyze_database',
            'reason': 'تحديث إحصائيات قاعدة البيانات',
            'sql': 'ANALYZE'
        })
    
    def optimize_database(self, apply_suggestions=False):
        """تحسين قاعدة البيانات"""
        print("⚡ بدء تحسين قاعدة البيانات...")
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if apply_suggestions:
                    # تطبيق اقتراحات التحسين الآمنة
                    safe_suggestions = [s for s in self.analysis_results['optimization_suggestions'] 
                                      if s['type'] in ['create_index', 'vacuum_database', 'analyze_database']]
                    
                    for suggestion in safe_suggestions:
                        try:
                            print(f"🔧 تطبيق: {suggestion['reason']}")
                            cursor.execute(suggestion['sql'])
                            
                            self.optimizations_applied.append({
                                'type': suggestion['type'],
                                'description': suggestion['reason'],
                                'sql': suggestion['sql'],
                                'status': 'success'
                            })
                            
                        except Exception as e:
                            print(f"❌ فشل في تطبيق: {suggestion['reason']} - {str(e)}")
                            self.optimizations_applied.append({
                                'type': suggestion['type'],
                                'description': suggestion['reason'],
                                'sql': suggestion['sql'],
                                'status': 'failed',
                                'error': str(e)
                            })
                
                # تحسينات أساسية آمنة
                cursor.execute("PRAGMA optimize")
                cursor.execute("VACUUM")
                cursor.execute("ANALYZE")
                
                conn.commit()
                
            print("✅ تم تحسين قاعدة البيانات بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في تحسين قاعدة البيانات: {str(e)}")
    
    def generate_report(self):
        """إنتاج تقرير التحليل والتحسين"""
        report = {
            'timestamp': str(datetime.now()),
            'database_path': self.db_path,
            'analysis_results': self.analysis_results,
            'optimizations_applied': self.optimizations_applied,
            'summary': {
                'total_tables': len(self.analysis_results['tables']),
                'empty_tables': len(self.analysis_results['empty_tables']),
                'unused_columns': len(self.analysis_results['unused_columns']),
                'optimization_suggestions': len(self.analysis_results['optimization_suggestions']),
                'optimizations_applied': len(self.optimizations_applied)
            }
        }
        
        save_json_report(report, 'temp/database_analysis_report.json', 'تقرير تحليل قاعدة البيانات')
        return report


if __name__ == "__main__":
    cleaner = DatabaseCleaner()
    
    # تحليل قاعدة البيانات
    cleaner.analyze_database()
    
    # تحسين قاعدة البيانات (بدون تطبيق الاقتراحات الخطيرة)
    cleaner.optimize_database(apply_suggestions=False)
    
    # إنتاج التقرير
    report = cleaner.generate_report()
    
    print("\n🗄️ ملخص تحليل قاعدة البيانات:")
    print(f"📊 إجمالي الجداول: {report['summary']['total_tables']}")
    print(f"🗑️ الجداول الفارغة: {report['summary']['empty_tables']}")
    print(f"❌ الأعمدة غير المستخدمة: {report['summary']['unused_columns']}")
    print(f"💡 اقتراحات التحسين: {report['summary']['optimization_suggestions']}")
    print(f"✅ التحسينات المطبقة: {report['summary']['optimizations_applied']}")
