# -*- coding: utf-8 -*-
"""
أداة تحسين التوثيق والتعليقات
Documentation Enhancement Tool
"""

import ast
import os
import re
from datetime import datetime
from typing import Dict, List, Optional

try:
    from .common_utils import find_python_files, save_json_report
except ImportError:
    from tools.common_utils import find_python_files, save_json_report


class DocumentationEnhancer:
    """أداة تحسين التوثيق والتعليقات في الكود"""
    
    def __init__(self):
        self.documentation_issues = []
        self.improvements_made = []
        self.statistics = {
            'files_processed': 0,
            'functions_documented': 0,
            'classes_documented': 0,
            'missing_docstrings': 0,
            'comments_added': 0
        }
    
    def enhance_project_documentation(self, project_path="."):
        """تحسين توثيق المشروع بالكامل"""
        print("📚 بدء تحسين التوثيق...")
        
        python_files = find_python_files(project_path)
        
        for file_path in python_files:
            if self._should_skip_file(file_path):
                continue
            
            try:
                self._enhance_file_documentation(file_path)
                self.statistics['files_processed'] += 1
            except Exception as e:
                print(f"❌ خطأ في تحسين توثيق {file_path}: {e}")
        
        self._generate_documentation_report()
        print(f"✅ تم تحسين توثيق {self.statistics['files_processed']} ملف")
    
    def _should_skip_file(self, file_path):
        """تحديد الملفات التي يجب تجاهلها"""
        skip_patterns = ['venv', '__pycache__', '.git', 'migrations', 'test_']
        return any(pattern in file_path for pattern in skip_patterns)
    
    def _enhance_file_documentation(self, file_path):
        """تحسين توثيق ملف واحد"""
        print(f"📖 تحسين توثيق: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        try:
            tree = ast.parse(content)
            enhanced_content = self._add_missing_docstrings(content, tree, file_path)
            enhanced_content = self._improve_comments(enhanced_content, file_path)
            enhanced_content = self._add_file_header(enhanced_content, file_path)
            
            # حفظ الملف إذا تم تحسينه
            if enhanced_content != content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(enhanced_content)
                
                self.improvements_made.append({
                    'file': file_path,
                    'type': 'documentation_enhancement',
                    'description': 'تم تحسين التوثيق والتعليقات'
                })
        
        except SyntaxError as e:
            self.documentation_issues.append({
                'file': file_path,
                'type': 'syntax_error',
                'message': f'خطأ في بناء الجملة: {str(e)}'
            })
    
    def _add_missing_docstrings(self, content, tree, file_path):
        """إضافة docstrings مفقودة للدوال والفئات"""
        lines = content.split('\n')
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.ClassDef)):
                if not ast.get_docstring(node):
                    # إضافة docstring أساسي
                    docstring = self._generate_docstring(node, file_path)
                    if docstring:
                        # العثور على السطر المناسب لإدراج docstring
                        insert_line = node.lineno  # السطر بعد تعريف الدالة/الفئة
                        
                        # البحث عن أول سطر غير فارغ بعد التعريف
                        while insert_line < len(lines) and lines[insert_line].strip() == '':
                            insert_line += 1
                        
                        # إدراج docstring
                        indent = self._get_indentation(lines[node.lineno - 1]) + '    '
                        docstring_lines = [
                            f'{indent}"""',
                            f'{indent}{docstring}',
                            f'{indent}"""'
                        ]
                        
                        # إدراج في المكان المناسب
                        lines[insert_line:insert_line] = docstring_lines
                        
                        if isinstance(node, ast.FunctionDef):
                            self.statistics['functions_documented'] += 1
                        else:
                            self.statistics['classes_documented'] += 1
                else:
                    self.statistics['missing_docstrings'] += 1
        
        return '\n'.join(lines)
    
    def _generate_docstring(self, node, file_path):
        """إنتاج docstring تلقائي للدالة أو الفئة"""
        if isinstance(node, ast.FunctionDef):
            return self._generate_function_docstring(node)
        elif isinstance(node, ast.ClassDef):
            return self._generate_class_docstring(node)
        return None
    
    def _generate_function_docstring(self, func_node):
        """إنتاج docstring للدالة"""
        func_name = func_node.name
        
        # تحليل اسم الدالة لتوليد وصف
        if func_name.startswith('get_'):
            description = f"الحصول على {func_name[4:].replace('_', ' ')}"
        elif func_name.startswith('set_'):
            description = f"تعيين {func_name[4:].replace('_', ' ')}"
        elif func_name.startswith('create_'):
            description = f"إنشاء {func_name[7:].replace('_', ' ')}"
        elif func_name.startswith('delete_'):
            description = f"حذف {func_name[7:].replace('_', ' ')}"
        elif func_name.startswith('update_'):
            description = f"تحديث {func_name[7:].replace('_', ' ')}"
        elif func_name.startswith('load_'):
            description = f"تحميل {func_name[5:].replace('_', ' ')}"
        elif func_name.startswith('save_'):
            description = f"حفظ {func_name[5:].replace('_', ' ')}"
        elif func_name.startswith('init_'):
            description = f"تهيئة {func_name[5:].replace('_', ' ')}"
        elif func_name.startswith('setup_'):
            description = f"إعداد {func_name[6:].replace('_', ' ')}"
        else:
            description = f"دالة {func_name.replace('_', ' ')}"
        
        return description
    
    def _generate_class_docstring(self, class_node):
        """إنتاج docstring للفئة"""
        class_name = class_node.name
        
        # تحليل اسم الفئة لتوليد وصف
        if class_name.endswith('Widget'):
            description = f"واجهة {class_name[:-6]}"
        elif class_name.endswith('Dialog'):
            description = f"نافذة حوار {class_name[:-6]}"
        elif class_name.endswith('Window'):
            description = f"نافذة {class_name[:-6]}"
        elif class_name.endswith('Manager'):
            description = f"مدير {class_name[:-7]}"
        elif class_name.endswith('Handler'):
            description = f"معالج {class_name[:-7]}"
        elif class_name.endswith('Optimizer'):
            description = f"محسن {class_name[:-9]}"
        else:
            description = f"فئة {class_name}"
        
        return description
    
    def _improve_comments(self, content, file_path):
        """تحسين التعليقات الموجودة"""
        lines = content.split('\n')
        improved_lines = []
        
        for line in lines:
            # تحسين التعليقات المضمنة
            if '#' in line and not line.strip().startswith('#'):
                # التأكد من وجود مسافتين قبل التعليق
                parts = line.split('#', 1)
                if len(parts) == 2:
                    code_part = parts[0].rstrip()
                    comment_part = parts[1].strip()
                    if comment_part:
                        line = f"{code_part}  # {comment_part}"
            
            improved_lines.append(line)
        
        return '\n'.join(improved_lines)
    
    def _add_file_header(self, content, file_path):
        """إضافة رأس الملف إذا لم يكن موجوداً"""
        lines = content.split('\n')
        
        # التحقق من وجود رأس الملف
        has_header = False
        for i, line in enumerate(lines[:10]):  # فحص أول 10 أسطر
            if '# -*- coding: utf-8 -*-' in line or '"""' in line:
                has_header = True
                break
        
        if not has_header:
            # إضافة رأس أساسي
            filename = os.path.basename(file_path)
            header = [
                '# -*- coding: utf-8 -*-',
                '"""',
                f'{filename} - وصف الملف',
                f'تم إنشاؤه في: {datetime.now().strftime("%Y-%m-%d")}',
                '"""',
                ''
            ]
            
            # إدراج الرأس في بداية الملف
            lines = header + lines
            
            self.improvements_made.append({
                'file': file_path,
                'type': 'file_header',
                'description': 'تم إضافة رأس الملف'
            })
        
        return '\n'.join(lines)
    
    def _get_indentation(self, line):
        """الحصول على المسافة البادئة للسطر"""
        return line[:len(line) - len(line.lstrip())]
    
    def _generate_documentation_report(self):
        """إنتاج تقرير التوثيق"""
        report = {
            'timestamp': str(datetime.now()),
            'statistics': self.statistics,
            'improvements_made': self.improvements_made,
            'issues_found': self.documentation_issues,
            'summary': {
                'total_improvements': len(self.improvements_made),
                'documentation_coverage': self._calculate_coverage()
            }
        }
        
        save_json_report(report, 'temp/documentation_report.json', 'تقرير التوثيق')
        return report
    
    def _calculate_coverage(self):
        """حساب نسبة تغطية التوثيق"""
        total_items = (self.statistics['functions_documented'] + 
                      self.statistics['classes_documented'] + 
                      self.statistics['missing_docstrings'])
        
        if total_items == 0:
            return 100.0
        
        documented_items = (self.statistics['functions_documented'] + 
                           self.statistics['classes_documented'])
        
        return round((documented_items / total_items) * 100, 2)


if __name__ == "__main__":
    enhancer = DocumentationEnhancer()
    enhancer.enhance_project_documentation()
    
    print("\n📚 ملخص تحسين التوثيق:")
    print(f"📁 الملفات المعالجة: {enhancer.statistics['files_processed']}")
    print(f"🔧 الدوال الموثقة: {enhancer.statistics['functions_documented']}")
    print(f"🏗️ الفئات الموثقة: {enhancer.statistics['classes_documented']}")
    print(f"📊 تغطية التوثيق: {enhancer._calculate_coverage()}%")
