"""
from PyQt5.QtCore import Qt, QTimer
import datetime

        import random
from PyQt5.QtGui import QFont
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,

from ui.unified_styles import UnifiedStyles

لوحة المعلومات المتطورة النظيفة - بدون أكواد غير مستخدمة
"""

                             Q<PERSON>rame, QProgressBar, QScrollArea)

class CleanAdvancedDashboard(QWidget):

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        # التخطيط الرئيسي - استغلال كامل المساحة
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: rgba(0, 0, 0, 0.1);
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: rgba(0, 0, 0, 0.3);
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: rgba(0, 0, 0, 0.5);
            }
        """)

        # الحاوية الرئيسية للمحتوى - استغلال كامل مع حفظ المكونات
        content_widget = QWidget()
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش لاستغلال الفراغ
        content_layout.setSpacing(15)  # الحفاظ على المسافات بين المكونات

        # تطبيق التصميم الأنيق
        self.apply_elegant_styling()

        # إنشاء الرسم البياني المتطور الوحيد الشامل
        self.create_ultra_advanced_single_chart(content_layout)

        content_widget.setLayout(content_layout)
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)
        self.setLayout(main_layout)

        # بدء تحديث التاريخ وآخر تحديث
        self.update_datetime()
        self.update_last_update_time()

        # مؤقت التاريخ (تحديث أبطأ لتجنب الوميض)
        self.date_timer = QTimer()
        self.date_timer.timeout.connect(self.update_datetime)
        self.date_timer.start(300000)  # تحديث كل 5 دقائق

        # مؤقت آخر تحديث (تحديث أبطأ لتجنب الوميض)
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_last_update_time)
        self.update_timer.start(120000)  # تحديث كل دقيقتين

        print("✅ تم إنشاء الواجهة المتطورة النظيفة بنجاح!")

    def update_datetime(self):
        now = datetime.datetime.now()

        # تنسيق التاريخ: سنة/شهر/يوم
        date_str = now.strftime("%Y/%m/%d")

        # أسماء الأيام بالعربية
        arabic_days = ["الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت", "الأحد"]
        day_name = arabic_days[now.weekday()]

        # تحديد الأيقونة حسب اليوم
        day_icons = {
            "الاثنين": "🌟",
            "الثلاثاء": "💫",
            "الأربعاء": "⭐",
            "الخميس": "✨",
            "الجمعة": "🌙",
            "السبت": "☀️",
            "الأحد": "🌅"
        }
        day_icon = day_icons.get(day_name, "📅")

        # عرض التاريخ واليوم فقط (بدون وقت)
        datetime_str = f"{day_icon} {day_name} | 📅 {date_str}"
        self.datetime_label.setText(datetime_str)

    def update_last_update_time(self):
        """تحديث مؤشر آخر تحديث المتطور بدون وقت"""
        now = datetime.datetime.now()

        # تحديد الأيقونة والرسالة حسب الوقت
        hour = now.hour
        if 6 <= hour < 12:
            update_icon = "🌅"
            update_message = "تحديث صباحي"
            status_color = "rgba(255, 193, 7, 0.8)"
        elif 12 <= hour < 18:
            update_icon = "☀️"
            update_message = "تحديث نهاري"
            status_color = "rgba(255, 152, 0, 0.8)"
        elif 18 <= hour < 22:
            update_icon = "🌆"
            update_message = "تحديث مسائي"
            status_color = "rgba(156, 39, 176, 0.8)"
        else:
            update_icon = "🌙"
            update_message = "تحديث ليلي"
            status_color = "rgba(63, 81, 181, 0.8)"

        # رسائل متنوعة للتحديث بدون وقت
        update_messages = [
            f"{update_icon} {update_message} - نشط",
            f"🔄 {update_message} - محدث",
            f"⚡ {update_message} - مباشر",
            f"📊 {update_message} - فعال",
            f"🎯 {update_message} - متاح",
            f"✨ {update_message} - جاهز",
            f"🚀 {update_message} - متطور",
            f"💫 {update_message} - ذكي"
        ]

        # اختيار رسالة عشوائية
        selected_message = random.choice(update_messages)
        self.last_update_label.setText(selected_message)

        # تحديث لون الخلفية حسب الوقت بدون إطارات
            color: white;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {status_color},
                stop:1 rgba(255, 255, 255, 0.1));
            border-radius: 15px;
            border: none;
            padding: 8px 16px;
        """)

    def create_ultra_advanced_single_chart(self, layout):
        # الحاوية الرئيسية للرسم البياني المتطور
        main_chart_frame = QFrame()
        main_chart_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(102, 126, 234, 0.95),
                    stop:0.2 rgba(118, 75, 162, 0.95),
                    stop:0.4 rgba(240, 147, 251, 0.95),
                    stop:0.6 rgba(245, 87, 108, 0.95),
                    stop:0.8 rgba(79, 172, 254, 0.95),
                    stop:1 rgba(0, 242, 254, 0.95));
                border-radius: 20px;
                border: 2px solid rgba(255, 255, 255, 0.4);
                margin: 0px;
            }

        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش لاستغلال الفراغ
        main_layout.setSpacing(20)  # الحفاظ على المسافات بين الأقسام

        # العنوان الرئيسي المتطور مع ألوان متدرجة بدون إطارات
        title_container = QWidget()
        title_container.setFixedHeight(80)  # تحديد ارتفاع 80 للمساحة الرئيسية
        title_container.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(138, 43, 226, 0.85),
                    stop:0.15 rgba(75, 0, 130, 0.85),
                    stop:0.3 rgba(72, 61, 139, 0.85),
                    stop:0.45 rgba(106, 90, 205, 0.85),
                    stop:0.6 rgba(147, 112, 219, 0.85),
                    stop:0.75 rgba(186, 85, 211, 0.85),
                    stop:0.9 rgba(219, 112, 147, 0.85),
                    stop:1 rgba(255, 105, 180, 0.85));
                border-radius: 20px;
                border: none;
                margin: 5px;
            }

        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(8, 6, 8, 6)  # هوامش مناسبة للارتفاع 80
        title_layout.setSpacing(8)  # مسافات مناسبة بين العناصر

        # أيقونة العنوان المتطورة (حجم مناسب)
        title_icon = QLabel("📊")
        title_icon.setFont(QFont("Arial", 28))
        title_icon.setAlignment(Qt.AlignCenter)
        title_icon.setFixedSize(35, 35)
        title_icon.setStyleSheet("""
            color: white;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(255, 255, 255, 0.2),
                stop:1 rgba(255, 255, 255, 0.1));
            border-radius: 17px;
            border: none;
        title_layout.addWidget(title_icon)

        # نص العنوان المتطور بدون إطارات
        main_title = QLabel("لوحة التحكم الذكية المتطورة - تحليلات شاملة")
        main_title.setFont(QFont("Arial", 24, QFont.Bold))
        main_title.setStyleSheet("""
            color: white;
            background: transparent;
            border: none;
            padding: 4px 8px;
        title_layout.addWidget(main_title)

        title_layout.addStretch()

        # التاريخ المتطور بدون إطارات
        self.datetime_label = QLabel()
        self.datetime_label.setFont(QFont("Arial", 24, QFont.Bold))
        self.datetime_label.setStyleSheet("""
            color: white;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(255, 255, 255, 0.2),
                stop:1 rgba(255, 255, 255, 0.1));
            border-radius: 15px;
            border: none;
            padding: 8px 16px;
        self.datetime_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(self.datetime_label)

        title_layout.addStretch()

        # مؤشر آخر تحديث المتطور بدون إطارات
        self.last_update_label = QLabel()
        self.last_update_label.setFont(QFont("Arial", 24, QFont.Bold))
        self.last_update_label.setStyleSheet("""
            color: white;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(39, 174, 96, 0.8),
                stop:1 rgba(46, 204, 113, 0.8));
            border-radius: 15px;
            border: none;
            padding: 8px 16px;
        self.update_last_update_time()
        title_layout.addWidget(self.last_update_label)

        title_container.setLayout(title_layout)
        main_layout.addWidget(title_container)

        # إضافة مساحة مرنة في الأعلى لتوسيط المحتوى
        main_layout.addStretch(1)

        # القسم الأول: الإحصائيات الرئيسية المتطورة (في المنتصف)
        stats_section = self.create_ultra_stats_section()
        main_layout.addWidget(stats_section)

        # إضافة مساحة صغيرة بين الإحصائيات والرسوم البيانية
        main_layout.addSpacing(5)

        # القسم الثاني: الرسوم البيانية التفاعلية المتطورة (في المنتصف)
        charts_section = self.create_ultra_charts_section()
        main_layout.addWidget(charts_section)

        # إضافة مساحة مرنة في الأسفل لتوسيط المحتوى
        main_layout.addStretch(1)

        main_chart_frame.setLayout(main_layout)
        layout.addWidget(main_chart_frame)

    def create_ultra_stats_section(self):
        """إنشاء قسم الإحصائيات المتطور بدون إطارات"""
        stats_frame = QFrame()
            QFrame {
                background: transparent;
                border: none;
            }
        """)

        stats_layout = QVBoxLayout()
        stats_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش لاستغلال الفراغ
        stats_layout.setSpacing(15)  # الحفاظ على المسافات بين المكونات

        # عنوان القسم المرفوع والمكبر
        section_title = QLabel("📈 الإحصائيات الرئيسية المتطورة")
        section_title.setFont(QFont("Arial", 20, QFont.Bold))
            color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #8B5CF6,
                stop:0.5 #EC4899,
                stop:1 #F59E0B);
            background: transparent;
            padding: 10px;
        """)
        section_title.setAlignment(Qt.AlignCenter)
        stats_layout.addWidget(section_title)

        # شبكة الإحصائيات - مسافات مضبوطة
        stats_grid = QHBoxLayout()
        stats_grid.setSpacing(10)  # مسافات مضبوطة بين البطاقات

        # إحصائيات متطورة
        ultra_stats = [
            ("💰", "إجمالي الإيرادات", "2,450,000 ريال", "#27ae60", "+15.2%"),
            ("🤝", "إجمالي العملاء", "1,247 عميل", "#3498db", "+8.7%"),
            ("🏗️", "المشاريع النشطة", "89 مشروع", "#e74c3c", "+12.4%"),
            ("📊", "معدل النجاح", "94.2%", "#9b59b6", "+2.1%"),
            ("⚡", "الأداء العام", "ممتاز", "#f39c12", "+5.8%")
        ]

        for icon, title, value, color, growth in ultra_stats:
            stat_card = self.create_ultra_stat_card(icon, title, value, color, growth)
            stats_grid.addWidget(stat_card)

        stats_layout.addLayout(stats_grid)
        stats_frame.setLayout(stats_layout)
        return stats_frame

    def create_ultra_stat_card(self, icon, title, value, color, growth):
        card = QFrame()
        card.setFixedHeight(200)  # زيادة الارتفاع أكثر لتكبير الطول
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color}25,
                    stop:0.5 {color}20,
                    stop:1 {color}15);
                border: none;
                border-radius: 10px;
                margin: 2px;
            }}
            QFrame:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color}35,
                    stop:0.5 {color}30,
                    stop:1 {color}25);
            }}

        card_layout = QVBoxLayout()
        card_layout.setContentsMargins(12, 12, 12, 12)  # هوامش أكبر للارتفاع الجديد
        card_layout.setSpacing(10)  # مسافات أكبر بين العناصر

        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setFont(QFont("Arial", 38))
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet(f"color: {color}; background: transparent;")
        card_layout.addWidget(icon_label)

        # القيمة الرئيسية
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 22, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"color: {color}; background: transparent;")
        card_layout.addWidget(value_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; background: transparent;")
        title_label.setWordWrap(True)
        card_layout.addWidget(title_label)

        # مؤشر النمو
        growth_label = QLabel(f"📈 {growth}")
        growth_label.setFont(QFont("Arial", 13, QFont.Bold))
        growth_label.setAlignment(Qt.AlignCenter)
        growth_label.setStyleSheet("color: #27ae60; background: transparent;")
        card_layout.addWidget(growth_label)

        card.setLayout(card_layout)
        return card

    def create_ultra_charts_section(self):
        """إنشاء قسم الرسوم البيانية المتطور بدون إطارات"""
        charts_frame = QFrame()
            QFrame {
                background: transparent;
                border: none;
            }
        """)

        charts_layout = QVBoxLayout()
        charts_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش لاستغلال الفراغ
        charts_layout.setSpacing(8)  # مسافات مقللة بين المكونات

        # عنوان القسم في المنتصف ومطور
        section_title = QLabel("📊 الرسوم البيانية التفاعلية المتطورة")
        section_title.setFont(QFont("Arial", 20, QFont.Bold))
            color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3B82F6,
                stop:0.5 #8B5CF6,
                stop:1 #EC4899);
            background: transparent;
            padding: 10px;
        """)
        section_title.setAlignment(Qt.AlignCenter)
        charts_layout.addWidget(section_title)

        # رسم بياني شامل متطور
        mega_chart = self.create_mega_chart()
        charts_layout.addWidget(mega_chart)

        charts_frame.setLayout(charts_layout)
        return charts_frame

    def create_mega_chart(self):
        chart_frame = QFrame()
        chart_frame.setStyleSheet("""
            QFrame {
                background: transparent;
                border: none;
            }

        chart_layout = QVBoxLayout()
        chart_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش لاستغلال الفراغ
        chart_layout.setSpacing(6)  # مسافات مقللة بين الصفوف

        # الصف الأول: الإيرادات والمصروفات - مسافات مضبوطة
        financial_row = QHBoxLayout()
        financial_row.setSpacing(10)  # مسافات مضبوطة بين البطاقات

        # بيانات مالية متطورة
        financial_data = [
            ("💰 الإيرادات", "2,450,000", "#27ae60", 85),
            ("💸 المصروفات", "1,680,000", "#e74c3c", 65),
            ("📈 الأرباح", "770,000", "#3498db", 75),
            ("💎 الاستثمارات", "320,000", "#9b59b6", 45)
        ]

        for title, value, color, percentage in financial_data:
            financial_card = self.create_advanced_data_card(title, value, color, percentage)
            financial_row.addWidget(financial_card)

        chart_layout.addLayout(financial_row)

        # الصف الثاني: العملاء والمشاريع - مسافات مضبوطة
        business_row = QHBoxLayout()
        business_row.setSpacing(10)  # مسافات مضبوطة بين البطاقات

        business_data = [
            ("🤝 العملاء النشطين", "1,247", "#3498db", 92),
            ("🏗️ المشاريع الجارية", "89", "#f39c12", 78),
            ("✅ المشاريع المكتملة", "156", "#27ae60", 95),
            ("⏰ المشاريع المتأخرة", "12", "#e74c3c", 15)
        ]

        for title, value, color, percentage in business_data:
            business_card = self.create_advanced_data_card(title, value, color, percentage)
            business_row.addWidget(business_card)

        chart_layout.addLayout(business_row)

        # الصف الثالث: بيانات إضافية - مسافات مضبوطة
        additional_row = QHBoxLayout()
        additional_row.setSpacing(10)  # مسافات مضبوطة بين البطاقات

        additional_data = [
            ("📊 التقارير المنجزة", "245", "#9b59b6", 88),
            ("💼 العقود النشطة", "67", "#e67e22", 72),
            ("🎯 الأهداف المحققة", "34", "#2ecc71", 95),
            ("⚙️ المهام المعلقة", "18", "#e74c3c", 25)
        ]

        for title, value, color, percentage in additional_data:
            additional_card = self.create_advanced_data_card(title, value, color, percentage)
            additional_row.addWidget(additional_card)

        chart_layout.addLayout(additional_row)

        chart_frame.setLayout(chart_layout)
        return chart_frame

    def create_advanced_data_card(self, title, value, color, percentage):
        """إنشاء بطاقة بيانات متطورة بدون إطارات مع ارتفاع موحد"""
        card = QFrame()
        card.setFixedHeight(118)  # إنزال بدرجتين (120 - 2 = 118)
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color}15,
                    stop:1 {color}25);
                border: none;
                border-radius: 8px;
                margin: 1px;
            }}
            QFrame:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color}25,
                    stop:1 {color}35);
            }}
        """)

        card_layout = QVBoxLayout()
        card_layout.setContentsMargins(1, 1, 1, 1)  # أقل هوامش ممكنة لاستغلال المساحة
        card_layout.setSpacing(1)  # أقل مسافات ممكنة لاستغلال المساحة العمودية

        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"color: {color}; background: transparent;")
        card_layout.addWidget(title_label)

        # القيمة
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 20, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet("color: #2c3e50; background: transparent;")
        card_layout.addWidget(value_label)

        # شريط التقدم المتطور
        progress = QProgressBar()
        progress.setRange(0, 100)
        progress.setValue(percentage)
        progress.setFixedHeight(12)
        progress.setTextVisible(False)
            QProgressBar {{
                background: #f0f0f0;
                border: none;
                border-radius: 6px;
            }}
            QProgressBar::chunk {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {color}, stop:1 {color}80);
                border-radius: 6px;
            }}
        """)
        card_layout.addWidget(progress)

        # النسبة المئوية
        percentage_label = QLabel(f"{percentage}%")
        percentage_label.setFont(QFont("Arial", 10, QFont.Bold))
        percentage_label.setAlignment(Qt.AlignCenter)
        percentage_label.setStyleSheet(f"color: {color}; background: transparent;")
        card_layout.addWidget(percentage_label)

        card.setLayout(card_layout)
        return card

    def apply_elegant_styling(self):
        self.setStyleSheet(f"""
            QWidget {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {UnifiedStyles.COLORS['background']},
                    stop:1 {UnifiedStyles.COLORS['surface']});
                color: {UnifiedStyles.COLORS.get('text', '#333')};
                font-family: 'Segoe UI', Arial, sans-serif;
            }}