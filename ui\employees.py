"""
                        import re
                    from datetime import datetime
                    from reportlab.lib import colors
                    from reportlab.lib.pagesizes import letter, A4
                    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
                    from reportlab.lib.units import inch
                    from reportlab.pdfbase import pdfmetrics
                    from reportlab.pdfbase.ttfonts import TTFont
                    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
                from datetime import datetime
            from PyQt5.QtCore import QDate
            from PyQt5.QtCore import Qt, QDate
            from PyQt5.QtCore import Qt, QTimer
            from PyQt5.QtCore import Qt, QTimer, QRect, pyqtSignal, QEasingCurve
            from datetime import datetime
            from datetime import datetime
            from datetime import datetime
            from datetime import datetime
            from datetime import datetime
            from datetime import datetime
            from datetime import datetime
            from datetime import datetime
            from datetime import datetime
            from datetime import datetime
            from datetime import datetime, date
            from datetime import datetime, timedelta
            from datetime import datetime, timedelta
            from datetime import datetime, timedelta
            from datetime import datetime, timedelta
            from datetime import datetime, timedelta
            from datetime import datetime, timedelta
            from datetime import datetime, timedelta
            import datetime
            import json
            import json
            import json
            import json
            import json
            import json
            import json
            import os
            import os
            import re
        from PyQt5.QtCore import QTimer
        from PyQt5.QtCore import QTimer
        from PyQt5.QtCore import Qt
        from PyQt5.QtCore import Qt
        from PyQt5.QtCore import Qt
        from PyQt5.QtCore import Qt
        from PyQt5.QtCore import Qt
        import re
        import re
        import re
        import re
        import re
from PyQt5.QtCore import QDate, Qt, QTimer

                    from PyQt5.QtWidgets import QApplication
                from openpyxl import Workbook
                from openpyxl.styles import Font, PatternFill, Alignment
                import csv
            from PyQt5.QtGui import QColor
            from PyQt5.QtGui import QColor
            from PyQt5.QtGui import QFont, QPalette, QColor, QPainter, QLinearGradient
            from PyQt5.QtGui import QTextDocument
            from PyQt5.QtGui import QTextDocument
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QCheckBox, QDialogButtonBox,
            from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
            from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QPushButton, QLabel,
            from PyQt5.QtWidgets import QApplication
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtWidgets import QInputDialog
            from PyQt5.QtWidgets import QMessageBox
            from PyQt5.QtWidgets import QMessageBox
            from PyQt5.QtWidgets import QMessageBox
            from PyQt5.QtWidgets import QMessageBox
            from PyQt5.QtWidgets import QMessageBox
            from PyQt5.QtWidgets import QMessageBox
            from PyQt5.QtWidgets import QMessageBox
            from PyQt5.QtWidgets import QMessageBox
            from sqlalchemy import func
            from sqlalchemy import func
            from sqlalchemy import func
            from sqlalchemy import func
            from sqlalchemy import func
            from sqlalchemy import func
            from sqlalchemy import func
            from sqlalchemy import func
            from sqlalchemy import func
            from sqlalchemy import func
            from sqlalchemy import or_, and_, func
            import csv
            import csv
            import pandas as pd
            import platform
            import random
            import subprocess
        from PyQt5.QtGui import QPainter, QFont, QColor
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtWidgets import QFileDialog
        from PyQt5.QtWidgets import QGroupBox, QRadioButton, QButtonGroup, QFormLayout
        from PyQt5.QtWidgets import QGroupBox, QRadioButton, QButtonGroup, QFormLayout
        from PyQt5.QtWidgets import QMenu, QAction
        from PyQt5.QtWidgets import QTableWidget
from PyQt5.QtGui import QFont, QColor, QPainter
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout,
from utils import show_error_message, show_info_message, show_confirmation_message, is_valid_phone, is_valid_email

            from ui.unified_styles import UnifiedStyles
        from ui.unified_styles import StyledTabWidget
        from ui.unified_styles import UnifiedStyles
        from ui.unified_styles import UnifiedStyles
from database import Employee, DailyWage, EmployeePhone
from ui.unified_styles import UnifiedStyles, StyledButton, StyledTable, StyledLabel, StyledGroupBox, BaseDialog

واجهة إدارة العمال المتطورة - نسخة متقدمة ومطورة
تطابق تماماً مع تطوير الموردين والعملاء
"""

                            QLineEdit, QTableWidget, QTableWidgetItem,
                            QComboBox, QTabWidget, QDialog, QFormLayout,
                            QTextEdit, QDateEdit, QDoubleSpinBox, QSpinBox,
                            QListWidget, QListWidgetItem, QSplitter, QCheckBox,
                            QLabel, QPushButton, QMenu, QAction, QHeaderView,
                            QAbstractItemView, QFrame, QSizePolicy, QFileDialog,
                            QMessageBox)

class EmployeesWidget(QWidget):

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مع هوامش صفر واستغلال كامل للمساحة
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش
        main_layout.setSpacing(0)  # إزالة المسافات

        # ضبط النافذة لاستغلال كل المساحة
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # إنشاء التبويبات المتطورة بعرض القائمة
        styled_tabs = StyledTabWidget()
        self.tabs = styled_tabs.tab_widget

        # تطوير تصميم التبويبات لتملأ العرض الكامل مع ارتفاع أقل
        self.tabs.setStyleSheet("""
            QTabWidget {
                background: #ecf0f1;
                border: none;
                margin: 0px;
                padding: 0px;
            }
            QTabWidget::pane {
                border: 3px solid #000000;
                border-radius: 8px;
                background: #ffffff;
                margin: 0px;
                padding: 0px;
                top: -2px;
            }
            QTabBar {
                background: transparent;
                border: none;
                margin: 0px;
                padding: 0px;
                qproperty-expanding: false;
                alignment: center;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                color: white;
                border: 2px solid #000000;
                border-bottom: none;
                padding: 8px 25px;
                margin-right: 5px;
                border-radius: 8px 8px 0px 0px;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                min-width: 900px;
                max-height: 35px;
                text-align: center;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4285f4, stop:0.5 #1a73e8, stop:1 #1557b0);
                color: white;
                border: 3px solid #000000;
                border-bottom: none;
                margin-top: -1px;
                padding: 9px 16px;
                font-size: 15px;
                max-height: 36px;
            }
            QTabBar::tab:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5a67d8, stop:0.5 #667eea, stop:1 #e0c3fc);
                border: 2px solid #000000;
            }

        # إنشاء تبويب بيانات العمال
        self.employees_data_widget = EmployeesDataWidget(self.session)
        self.tabs.addTab(self.employees_data_widget, "👥 بيانات العمال")

        # إنشاء تبويب الأجور اليومية
        self.daily_wages_widget = DailyWagesWidget(self.session)
        self.tabs.addTab(self.daily_wages_widget, "💰 الأجور اليومية")

        # إضافة التبويبات إلى التخطيط الرئيسي
        main_layout.addWidget(self.tabs)
        self.setLayout(main_layout)

    def save_daily_wages_data(self):
        """حفظ بيانات الأجور اليومية"""
        if hasattr(self, 'daily_wages_widget'):
            self.daily_wages_widget.refresh_data()

class EmployeesDataWidget(QWidget):

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()
        self.refresh_data()

    def init_ui(self):
        """إنشاء واجهة المستخدم المتطورة"""
        # إنشاء التخطيط الرئيسي مطابق للموردين
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(8)

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للموردين
        title_label = QLabel("👷 إدارة العمال المتطورة - نظام شامل ومتقدم لإدارة بيانات العمال مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e40af,
                    stop:0.2 #3b82f6,
                    stop:0.4 #6366f1,
                    stop:0.6 #8b5cf6,
                    stop:0.8 #a855f7,
                    stop:1 #c084fc);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;

            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للموردين
        top_frame = QFrame()
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مع ألوان موحدة - مطابقة للعملاء والموردين
        search_label = QLabel("🔍 بحث:")
            QLabel {
                color: #ffffff;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dc2626,
                    stop:0.5 #b91c1c,
                    stop:1 #991b1b);
                border: 3px solid #7f1d1d;
                border-radius: 12px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ef4444,
                    stop:0.5 #dc2626,
                    stop:1 #b91c1c);
                border: 3px solid #ef4444;
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث بالاسم، الهاتف، الإيميل أو العنوان...")
        self.search_edit.textChanged.connect(self.filter_employees)
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff,
                    stop:0.5 #f8fafc,
                    stop:1 #e2e8f0);
                border: 3px solid #4f46e5;
                border-radius: 12px;
                padding: 8px 15px;
                font-size: 14px;
                font-weight: bold;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: #4f46e5;
            }
            QLineEdit:focus {
                border: 3px solid #3730a3;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f0f9ff,
                    stop:1 #e0f2fe);
            }
            QLineEdit:hover {
                border: 3px solid #5b52f0;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fafbff,
                    stop:1 #f1f5f9);
            }
        """)

        search_button = QPushButton("🔍")
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0891b2,
                    stop:0.5 #0e7490,
                    stop:1 #155e75);
                color: #ffffff;
                border: 3px solid #164e63;
                border-radius: 12px;
                padding: 8px;
                font-size: 20px;
                font-weight: bold;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0ea5e9,
                    stop:0.5 #0891b2,
                    stop:1 #0e7490);
                border: 3px solid #0ea5e9;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #155e75,
                    stop:0.5 #164e63,
                    stop:1 #1e3a8a);
                border: 3px solid #1e3a8a;
            }
        """)
        search_button.clicked.connect(self.filter_employees)
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية التصفية موحدة مع العملاء والموردين
        filter_label = QLabel("🎯 تصفية:")
            QLabel {
                color: #ffffff;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f59e0b,
                    stop:0.5 #d97706,
                    stop:1 #b45309);
                border: 3px solid #92400e;
                border-radius: 12px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fbbf24,
                    stop:0.5 #f59e0b,
                    stop:1 #d97706);
                border: 3px solid #fbbf24;
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # أزرار التصفية السريعة بألوان مختلفة ومميزة - موحدة مع العملاء والموردين
        filter_all_button = QPushButton("📊 الكل")
        self.style_advanced_button(filter_all_button, 'slate')  # رمادي داكن للكل
        filter_all_button.clicked.connect(lambda: self.filter_by_balance('all'))
        filter_all_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        filter_positive_button = QPushButton("💰 لهم مبلغ")
        self.style_advanced_button(filter_positive_button, 'emerald')  # أخضر زمردي مختلف
        filter_positive_button.clicked.connect(lambda: self.filter_by_balance('positive'))
        filter_positive_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        filter_negative_button = QPushButton("⚠️ عليهم مبلغ")
        self.style_advanced_button(filter_negative_button, 'danger')  # أحمر للسالب
        filter_negative_button.clicked.connect(lambda: self.filter_by_balance('negative'))
        filter_negative_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        filter_zero_button = QPushButton("⚪ بدون رصيد")
        self.style_advanced_button(filter_zero_button, 'secondary')  # رمادي فاتح مختلف
        filter_zero_button.clicked.connect(lambda: self.filter_by_balance('zero'))
        filter_zero_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        # زر الأعمدة موحد مع العملاء والموردين
        columns_button = QPushButton("👁️ الأعمدة")
        self.style_advanced_button(columns_button, 'purple')  # بنفسجي للأعمدة
        columns_button.clicked.connect(self.show_columns_dialog)
        columns_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        # أزرار العمليات المتقدمة موحدة مع العملاء والموردين
        bulk_operations_button = QPushButton("📋 عمليات مجمعة")
        self.style_advanced_button(bulk_operations_button, 'indigo')  # نيلي للعمليات
        bulk_operations_button.clicked.connect(self.show_bulk_operations_dialog)
        bulk_operations_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        advanced_filters_button = QPushButton("🎯 فلاتر متقدمة")
        self.style_advanced_button(advanced_filters_button, 'rose')  # وردي للفلاتر
        advanced_filters_button.clicked.connect(self.show_advanced_filters_dialog)
        advanced_filters_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        # تطبيق نفس مقاسات الإطار السفلي على أزرار الإطار العلوي
        top_buttons = [filter_all_button, filter_positive_button, filter_negative_button,
                      filter_zero_button, columns_button, bulk_operations_button, advanced_filters_button]

        for button in top_buttons:
            button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)  # تغيير سياسة الحجم
            button.setMinimumWidth(95)  # نفس العرض الأدنى للإطار السفلي
            button.setMaximumHeight(38)  # ارتفاع أكبر ليناسب الإطار الأكبر (75px)
            button.setMinimumHeight(34)  # ارتفاع أدنى أكبر
            button.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش الإضافية

            # حجم خط مناسب للارتفاع الجديد
            font = button.font()
            font.setPointSize(12)  # حجم خط أكبر ليناسب الارتفاع الأكبر
            font.setBold(True)
            button.setFont(font)

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار موحد مع العملاء والموردين
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_all_button, 1, Qt.AlignVCenter)  # توسيع الأزرار
        search_layout.addWidget(filter_positive_button, 1, Qt.AlignVCenter)
        search_layout.addWidget(filter_negative_button, 1, Qt.AlignVCenter)
        search_layout.addWidget(filter_zero_button, 1, Qt.AlignVCenter)
        search_layout.addWidget(columns_button, 1, Qt.AlignVCenter)
        search_layout.addWidget(bulk_operations_button, 1, Qt.AlignVCenter)
        search_layout.addWidget(advanced_filters_button, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إنشاء جدول العمال المتطور
        self.create_employees_table()

        main_layout.addWidget(top_frame)
        main_layout.addWidget(self.employees_table, 1)  # إعطاء الجدول أولوية في التمدد

        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للموردين
        bottom_frame = QFrame()
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        actions_layout.setSpacing(4)  # مسافة أكبر بين الأزرار لتوزيع أفضل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار بأحجام متناسقة حسب الترتيب الجديد مطابق للموردين

        # المجموعة الأولى - العمليات الأساسية مع ألوان متنوعة
        self.add_button = QPushButton("➕ إضافة عامل ▼")
        self.style_advanced_button(self.add_button, 'emerald', has_menu=True)  # أخضر زمردي مميز مع قائمة
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة لإضافة العامل مع النمط الموحد الجديد
        add_menu = QMenu(self)
        add_menu.setStyleSheet(UnifiedStyles.get_menu_style('success', 'normal'))

        # إضافة خيارات الإضافة المتقدمة
        add_normal_action = QAction("👤 إضافة عامل عادي", self)
        add_normal_action.triggered.connect(self.add_employee)
        add_menu.addAction(add_normal_action)

        add_bulk_action = QAction("👥 إضافة عدة عمال", self)
        add_bulk_action.triggered.connect(lambda: show_info_message("قريباً", "ستتم إضافة ميزة إضافة عدة عمال قريباً"))
        add_menu.addAction(add_bulk_action)

        import_action = QAction("📥 استيراد من ملف", self)
        import_action.triggered.connect(lambda: show_info_message("قريباً", "ستتم إضافة ميزة الاستيراد من ملف قريباً"))
        add_menu.addAction(import_action)

        # ربط القائمة بالزر
        self.add_button.setMenu(add_menu)

        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'primary')  # أزرق كلاسيكي
        self.edit_button.clicked.connect(self.edit_employee)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')  # أحمر تحذيري
        self.delete_button.clicked.connect(self.delete_employee)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')  # تصميم حديث ومتطور - مطابق للعملاء والموردين
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # المجموعة الثانية - العمليات المالية والملفات مع ألوان متنوعة
        self.balance_button = QPushButton("💰 تعديل الرصيد")
        self.style_advanced_button(self.balance_button, 'orange')  # برتقالي مالي
        self.balance_button.clicked.connect(self.adjust_balance)
        self.balance_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.attachments_button = QPushButton("📎 إضافة المرفقات")
        self.style_advanced_button(self.attachments_button, 'purple')  # بنفسجي مميز
        self.attachments_button.clicked.connect(self.manage_attachments)
        self.attachments_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.details_button = QPushButton("👁️ عرض التفاصيل ▼")
        self.style_advanced_button(self.details_button, 'indigo', has_menu=True)  # نيلي عميق
        self.details_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.call_button = QPushButton("📞 اتصال سريع ▼")
        self.style_advanced_button(self.call_button, 'lime', has_menu=True)  # أخضر ليموني مختلف للاتصال
        self.call_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة لعرض التفاصيل مع النمط الموحد الجديد
        details_menu = QMenu(self)
        details_menu.setStyleSheet(UnifiedStyles.get_menu_style('primary', 'normal'))

        # إضافة خيارات عرض التفاصيل
        basic_details_action = QAction("👤 التفاصيل الأساسية", self)
        basic_details_action.triggered.connect(self.show_employee_details)
        details_menu.addAction(basic_details_action)

        advanced_details_action = QAction("📊 التفاصيل المتقدمة", self)
        advanced_details_action.triggered.connect(self.show_advanced_employee_details)
        details_menu.addAction(advanced_details_action)

        history_details_action = QAction("📋 سجل العمل", self)
        history_details_action.triggered.connect(self.show_work_history)
        details_menu.addAction(history_details_action)

        # ربط القائمة بالزر
        self.details_button.setMenu(details_menu)

        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'info', has_menu=True)  # لون متسق مع نظام الألوان
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة للتصدير مع النمط الموحد الجديد
        export_menu = QMenu(self)
        export_menu.setStyleSheet(UnifiedStyles.get_menu_style('warning', 'normal'))

        # إضافة خيارات التصدير
        export_excel_action = QAction("📊 تصدير إلى Excel", self)
        export_excel_action.triggered.connect(self.export_data)
        export_menu.addAction(export_excel_action)

        export_pdf_action = QAction("📄 تصدير إلى PDF", self)
        export_pdf_action.triggered.connect(self.export_to_pdf)
        export_menu.addAction(export_pdf_action)

        export_csv_action = QAction("📋 تصدير إلى CSV", self)
        export_csv_action.triggered.connect(self.export_to_csv)
        export_menu.addAction(export_csv_action)

        export_json_action = QAction("🔗 تصدير إلى JSON", self)
        export_json_action.triggered.connect(self.export_to_json)
        export_menu.addAction(export_json_action)

        # ربط القائمة بالزر
        self.export_button.setMenu(export_menu)

        # المجموعة الثالثة - التقارير والإحصائيات مع ألوان متنوعة
        self.report_button = QPushButton("📋 التقارير")
        self.style_advanced_button(self.report_button, 'cyan')  # سيان مميز للتقارير
        self.report_button.clicked.connect(self.generate_employees_report)
        self.report_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.statistics_button = QPushButton("📊 الإحصائيات ▼")
        self.style_advanced_button(self.statistics_button, 'rose', has_menu=True)  # وردي للإحصائيات
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # المجموعة الرابعة - النظام مع ألوان متنوعة
        self.backup_button = QPushButton("💾 نسخ احتياطي")
        self.style_advanced_button(self.backup_button, 'warning')  # كهرماني للنسخ الاحتياطي
        self.backup_button.clicked.connect(self.backup_employees_data)
        self.backup_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.restore_button = QPushButton("📥 استعادة")
        self.style_advanced_button(self.restore_button, 'slate')  # رمادي داكن مميز للاستعادة
        self.restore_button.clicked.connect(self.restore_employees_data)
        self.restore_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة للاتصال السريع مع النمط الموحد الجديد
        call_menu = QMenu(self)
        call_menu.setStyleSheet(UnifiedStyles.get_menu_style('success', 'normal'))

        # إضافة خيارات الاتصال المتقدمة
        direct_call_action = QAction("📞 اتصال مباشر", self)
        direct_call_action.triggered.connect(self.make_direct_call)
        call_menu.addAction(direct_call_action)

        whatsapp_action = QAction("💬 رسالة واتساب", self)
        whatsapp_action.triggered.connect(self.send_whatsapp)
        call_menu.addAction(whatsapp_action)

        email_action = QAction("📧 إرسال إيميل", self)
        email_action.triggered.connect(self.send_email)
        call_menu.addAction(email_action)

        sms_action = QAction("📱 رسالة نصية", self)
        sms_action.triggered.connect(self.send_sms)
        call_menu.addAction(sms_action)

        call_history_action = QAction("📋 سجل المكالمات", self)
        call_history_action.triggered.connect(self.show_call_history)
        call_menu.addAction(call_history_action)

        # ربط القائمة بالزر
        self.call_button.setMenu(call_menu)

        # إنشاء قائمة منسدلة للإحصائيات مع النمط الموحد الجديد
        statistics_menu = QMenu(self)
        statistics_menu.setStyleSheet(UnifiedStyles.get_menu_style('danger', 'normal'))

        # إضافة خيارات الإحصائيات
        basic_stats_action = QAction("📈 إحصائيات أساسية", self)
        basic_stats_action.triggered.connect(self.show_statistics)
        statistics_menu.addAction(basic_stats_action)

        detailed_stats_action = QAction("📊 إحصائيات مفصلة", self)
        detailed_stats_action.triggered.connect(self.show_detailed_statistics)
        statistics_menu.addAction(detailed_stats_action)

        performance_stats_action = QAction("⚡ إحصائيات الأداء", self)
        performance_stats_action.triggered.connect(self.show_performance_statistics)
        statistics_menu.addAction(performance_stats_action)

        monthly_report_action = QAction("📅 تقرير شهري", self)
        monthly_report_action.triggered.connect(self.show_monthly_report)
        statistics_menu.addAction(monthly_report_action)

        # ربط القائمة بالزر
        self.statistics_button.setMenu(statistics_menu)

        # إضافة الأزرار بالترتيب المطلوب مع ألوان متنوعة ومميزة مطابق للموردين

        # المجموعة الأولى - العمليات الأساسية مع التوسيط العمودي
        actions_layout.addWidget(self.add_button, 0, Qt.AlignVCenter)           # 1. ➕ إضافة عامل (أخضر)
        actions_layout.addWidget(self.edit_button, 0, Qt.AlignVCenter)          # 2. ✏️ تعديل (أزرق)
        actions_layout.addWidget(self.delete_button, 0, Qt.AlignVCenter)        # 3. 🗑️ حذف (أحمر)
        actions_layout.addWidget(self.refresh_button, 0, Qt.AlignVCenter)       # 4. 🔄 تحديث (تيل)

        # فاصل راقي 1
        actions_layout.addWidget(self.create_elegant_separator(), 0, Qt.AlignVCenter)

        # المجموعة الثانية - العمليات المالية والملفات مع التوسيط العمودي - موحدة مع العملاء
        actions_layout.addWidget(self.balance_button, 0, Qt.AlignVCenter)       # 5. 💰 تعديل الرصيد (برتقالي)
        actions_layout.addWidget(self.attachments_button, 0, Qt.AlignVCenter)   # 6. 📎 المرفقات (بنفسجي)
        actions_layout.addWidget(self.details_button, 0, Qt.AlignVCenter)       # 7. 👁️ التفاصيل (نيلي)

        # فاصل راقي 2
        actions_layout.addWidget(self.create_elegant_separator(), 0, Qt.AlignVCenter)

        # المجموعة الثالثة - الاتصال مع التوسيط العمودي - موحدة مع العملاء
        actions_layout.addWidget(self.call_button, 0, Qt.AlignVCenter)          # 8. 📞 اتصال سريع (ليموني)

        # فاصل راقي 3
        actions_layout.addWidget(self.create_elegant_separator(), 0, Qt.AlignVCenter)

        # المجموعة الرابعة - التقارير والإحصائيات مع التوسيط العمودي - موحدة مع العملاء
        actions_layout.addWidget(self.statistics_button, 0, Qt.AlignVCenter)    # 9. 📊 الإحصائيات (وردي)
        actions_layout.addWidget(self.report_button, 0, Qt.AlignVCenter)        # 10. 📋 التقارير (سيان)

        # فاصل راقي 4
        actions_layout.addWidget(self.create_elegant_separator(), 0, Qt.AlignVCenter)

        # المجموعة الخامسة - النظام مع التوسيط العمودي - موحدة مع العملاء
        actions_layout.addWidget(self.backup_button, 0, Qt.AlignVCenter)        # 11. 💾 نسخ احتياطي (كهرماني)
        actions_layout.addWidget(self.export_button, 0, Qt.AlignVCenter)        # 12. 📤 تصدير (تيل فاتح)
        actions_layout.addWidget(self.restore_button, 0, Qt.AlignVCenter)       # 13. 📥 استعادة (رمادي داكن)

        # تطبيق الأحجام الجديدة على جميع الأزرار - موحد مع العملاء والموردين
        all_buttons = [
            self.add_button, self.edit_button, self.delete_button, self.refresh_button,
            self.balance_button, self.attachments_button, self.details_button, self.call_button,
            self.statistics_button, self.report_button, self.backup_button, self.export_button, self.restore_button
        ]

        for button in all_buttons:
            # تطبيق الأحجام الجديدة المناسبة للإطار الأكبر (75px)
            button.setMaximumHeight(38)  # ارتفاع أكبر ليناسب الإطار الأكبر
            button.setMinimumHeight(34)  # ارتفاع أدنى أكبر
            button.setMinimumWidth(95)   # عرض أدنى ثابت
            button.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش

            # تطبيق خط أكبر ليناسب الارتفاع الجديد
            font = button.font()
            font.setPointSize(12)  # حجم خط أكبر ليناسب الارتفاع الأكبر
            font.setBold(True)
            button.setFont(font)

        # تعيين التخطيط للإطار السفلي - استخدام الحاوي العمودي للتوسيط
        bottom_frame.setLayout(bottom_container)

        main_layout.addWidget(bottom_frame)

        self.setLayout(main_layout)

    def create_employees_table(self):
        print("🚀 بدء إنشاء جدول العمال المتطور...")

        # إنشاء الجدول الأساسي مع تحسينات مرئية
        self.employees_table = QTableWidget()

        # إضافة العلامة المائية للجدول
        self.add_watermark_to_employees_table()

        # تطبيق نمط متطور جداً ومبتكر مع تأثيرات بصرية متقدمة
        self.employees_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.3);
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
                selection-background-color: rgba(66, 133, 244, 0.2);
                selection-color: #1a73e8;
                border: 3px solid #000000;
                border-radius: 12px;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: 12px;
                color: #2c3e50;
            }
            QTableWidget::item {
                padding: 12px 8px;
                border-bottom: 1px solid rgba(0, 0, 0, 0.1);
                border-right: 1px solid rgba(0, 0, 0, 0.05);
            }
            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(66, 133, 244, 0.3),
                    stop:1 rgba(66, 133, 244, 0.1));
                color: #1a73e8;
                font-weight: bold;
            }
            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(66, 133, 244, 0.1),
                    stop:1 rgba(66, 133, 244, 0.05));
            }
            QScrollBar:vertical {
                background: #f1f3f4;
                width: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4285f4, stop:1 #1a73e8);
                border-radius: 6px;
                min-height: 20px;
                margin: 2px;
            }
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1a73e8, stop:1 #1557b0);
            }
            QScrollBar:horizontal {
                background: #f1f3f4;
                height: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            QScrollBar::handle:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4285f4, stop:1 #1a73e8);
                border-radius: 6px;
                min-width: 20px;
                margin: 2px;
            }
            QScrollBar::handle:horizontal:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1a73e8, stop:1 #1557b0);
            }
            QScrollBar::add-line, QScrollBar::sub-line {
                border: none;
                background: none;
            }

        # إعداد خصائص الجدول المتقدمة مطابق للعملاء والموردين
        self.employees_table.setAlternatingRowColors(True)
        self.employees_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.employees_table.setSelectionMode(QAbstractItemView.ExtendedSelection)  # تحديد متعدد مثل العملاء
        self.employees_table.setSortingEnabled(True)
        self.employees_table.setShowGrid(True)
        self.employees_table.setWordWrap(False)

        # ضبط الجدول ليملأ العرض والارتفاع الكامل مع استغلال أقصى مساحة
        header = self.employees_table.horizontalHeader()
        header.setStretchLastSection(True)  # جعل العمود الأخير يمتد
        self.employees_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # ضبط الجدول ليستغل كل المساحة المتاحة مع إطار أسود
        self.employees_table.setMinimumHeight(400)  # ارتفاع أدنى أكبر
        self.employees_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)  # توزيع الأعمدة

        # إضافة إطار أسود للجدول
        self.employees_table.setStyleSheet(self.employees_table.styleSheet() + """
            QTableWidget {
                border: 3px solid #000000 !important;
            }

        # إعداد هيكل الجدول
        self.setup_employees_table_structure()

        # إعداد تنسيق الجدول
        self.setup_employees_table_styling()

        # تطبيق التحسينات النهائية مرة واحدة
        print("🎨 تطبيق التحسينات النهائية...")
        self.apply_employees_final_enhancements()

        # فرض تطبيق الأعراض الصحيحة في النهاية
        self.apply_employees_correct_widths()

    def add_watermark_to_employees_table(self):
        """إضافة العلامة المائية لجدول العمال"""

        # إنشاء فئة مخصصة للجدول مع العلامة المائية
        class WatermarkTableWidget(QTableWidget):
            def paintEvent(self, event):
                super().paintEvent(event)

                # رسم العلامة المائية
                painter = QPainter(self.viewport())
                painter.setRenderHint(QPainter.Antialiasing)

                # إعداد الخط والنص - حجم أكبر يناسب عرض وارتفاع الجدول
                viewport_rect = self.viewport().rect()
                # حساب حجم الخط بناءً على أبعاد الجدول
                font_size = min(viewport_rect.width() // 8, viewport_rect.height() // 4)
                font_size = max(font_size, 60)  # حد أدنى 60
                font_size = min(font_size, 150)  # حد أقصى 150

                font = QFont("Arial", font_size, QFont.Bold)
                painter.setFont(font)
                painter.setPen(QColor(200, 200, 200, 60))  # شفافية أكثر للحجم الأكبر

                # النص والموضع
                text = "Smart Finish"
                rect = self.viewport().rect()
                painter.drawText(rect, Qt.AlignCenter, text)
                painter.end()

        # استبدال الجدول العادي بالجدول مع العلامة المائية
        if hasattr(self, 'employees_table'):
            # نسخ الخصائص الحالية
            parent = self.employees_table.parent()
            geometry = self.employees_table.geometry()

            # إنشاء جدول جديد مع العلامة المائية
            new_table = WatermarkTableWidget()
            new_table.setParent(parent)
            new_table.setGeometry(geometry)

            # استبدال المرجع
            self.employees_table = new_table

        print("✅ تم إضافة العلامة المائية لجدول العمال")

    def setup_employees_table_structure(self):
        # تحديد عدد الأعمدة والعناوين المحسنة - إضافة عمود الرصيد
        self.employees_table.setColumnCount(10)  # زيادة عدد الأعمدة لإضافة الرصيد

        # عناوين محسنة ومختصرة مع أيقونات مختلفة - مطابقة للعملاء
        headers = [
            "🔢 ID",          # رقم مسلسل
            "👤 اسم العامل",   # الاسم كاملاً - نفس أيقونة العملاء
            "📱 الهاتف",      # رقم الهاتف - موحد مع العملاء والموردين
            "📧 الإيميل",     # البريد الإلكتروني - نفس أيقونة العملاء
            "📍 العنوان",     # العنوان - نفس أيقونة العملاء
            "💰 الرصيد",      # الرصيد المالي - مثل العملاء
            "💼 المنصب",      # المنصب/الوظيفة
            "📝 ملاحظات",     # الملاحظات - نفس أيقونة العملاء
            "🔄 الحالة",      # حالة العامل - نفس أيقونة العملاء
            "📅 التاريخ"      # تاريخ التوظيف - نفس أيقونة العملاء
        ]

        self.employees_table.setHorizontalHeaderLabels(headers)

    def setup_employees_table_styling(self):
        """إعداد تنسيق وأسلوب الجدول"""
        header = self.employees_table.horizontalHeader()

        # تطبيق خط متطور للعناوين - موحد مع العملاء والموردين
        header_font = QFont("Segoe UI", 16, QFont.Bold)  # خط موحد مع العملاء والموردين
        header_font.setLetterSpacing(QFont.AbsoluteSpacing, 1.5)  # تباعد الأحرف موحد
        header.setFont(header_font)

        # تطبيق ارتفاع مناسب للعناوين - موحد مع العملاء
        header.setFixedHeight(65)  # ارتفاع موحد مع العملاء

        # تطبيق ألوان متدرجة جميلة للعناوين
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                color: white;
                border: 3px solid #5a67d8;
                border-bottom: 5px solid #4c51bf;
                padding: 15px 8px;
                font-weight: bold;
                font-size: 14px;
                text-align: center;
                min-height: 35px;
                max-height: 55px;
                letter-spacing: 1.0px;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5a67d8, stop:0.5 #667eea, stop:1 #e0c3fc);
                border: 3px solid #4c51bf;
                border-bottom: 5px solid #3c366b;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4c51bf, stop:0.5 #553c9a, stop:1 #c084fc);
                border: 3px solid #3c366b;
                border-bottom: 5px solid #2d3748;
            }
        """)

        # إعداد أعراض الأعمدة المحسنة مع عرض كامل للقائمة - مطابقة للعملاء
        # تحديد الأعراض المحسنة مطابقة للعملاء مع إضافة عمود الرصيد
        fixed_widths = {
            0: 120,  # الرقم - مطابق للعملاء
            1: 150,  # الاسم - مطابق للعملاء
            2: 220,  # الهاتف - مطابق للعملاء
            3: 180,  # الإيميل - مطابق للعملاء
            4: 150,  # العنوان - مطابق للعملاء
            5: 200,  # الرصيد - مطابق للعملاء
            6: 155,  # المنصب - مطابق لعمود الرصيد في الموردين
            7: 105,  # الملاحظات - مطابق للعملاء
            8: 120,  # الحالة - مطابق للعملاء
            9: 154   # التاريخ - مطابق للعملاء
        }

        # تطبيق الأعراض
        for col, width in fixed_widths.items():
            self.employees_table.setColumnWidth(col, width)
            # تعيين بعض الأعمدة كثابتة
            if col in [0, 2, 7, 8]:  # الرقم، الهاتف، الحالة، التاريخ
                header.setSectionResizeMode(col, header.Fixed)

        # تحسين ارتفاع الصفوف - موحد مع الموردين
        vertical_header = self.employees_table.verticalHeader()
        vertical_header.setDefaultSectionSize(40)  # ارتفاع موحد مع الموردين
        vertical_header.setMinimumSectionSize(35)
        vertical_header.setMaximumSectionSize(45)
        vertical_header.hide()  # إخفاء أرقام الصفوف لتوفير مساحة

        print("✅ تم إعداد تنسيق جدول العمال بنجاح!")

    def apply_employees_final_enhancements(self):
        try:
            print("🎨 تطبيق التحسينات النهائية على جدول العمال...")

            # تطبيق الأعراض الصحيحة
            self.apply_employees_correct_widths()

            # تطبيق الإطار الأسود
            self.force_employees_black_border_style()

            print("✅ تم تطبيق التحسينات النهائية على جدول العمال بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في تطبيق التحسينات النهائية: {str(e)}")

    def apply_employees_correct_widths(self):
        """تطبيق الأعراض الصحيحة لجدول العمال"""
        try:
            print("🔧 تطبيق الأعراض الصحيحة لجدول العمال...")

            # تطبيق الأعراض المحسنة مطابقة للعملاء مع إضافة عمود الرصيد
            self.employees_table.setColumnWidth(0, 120)  # الرقم - مطابق للعملاء
            self.employees_table.setColumnWidth(1, 150)  # الاسم - مطابق للعملاء
            self.employees_table.setColumnWidth(2, 220)  # الهاتف - مطابق للعملاء
            self.employees_table.setColumnWidth(3, 180)  # الإيميل - مطابق للعملاء
            self.employees_table.setColumnWidth(4, 150)  # العنوان - مطابق للعملاء
            self.employees_table.setColumnWidth(5, 200)  # الرصيد - مطابق للعملاء
            self.employees_table.setColumnWidth(6, 155)  # المنصب - مطابق لعمود الرصيد في الموردين
            self.employees_table.setColumnWidth(7, 105)  # الملاحظات - مطابق للعملاء
            self.employees_table.setColumnWidth(8, 120)  # الحالة - مطابق للعملاء
            self.employees_table.setColumnWidth(9, 154)  # التاريخ - مطابق للعملاء

            # فرض التحديث
            self.employees_table.update()
            self.employees_table.repaint()

            # تطبيق ارتفاع الصفوف المحسن - مطابق للموردين
            for row in range(self.employees_table.rowCount()):
                self.employees_table.setRowHeight(row, 40)  # ارتفاع مطابق للموردين

            print("✅ تم تطبيق الأعراض الصحيحة لجدول العمال بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في تطبيق الأعراض: {str(e)}")

    def force_employees_black_border_style(self):
        try:
            # الحصول على التنسيق الحالي وتعديل الإطار فقط
            current_style = self.employees_table.styleSheet()

            # إضافة الإطار الأسود فقط
            black_border_addition = """
                QTableWidget {
                    border: 3px solid #000000 !important;
                }

            # دمج التنسيق الحالي مع الإطار الأسود
            combined_style = current_style + black_border_addition
            self.employees_table.setStyleSheet(combined_style)

            print("✅ تم تطبيق الإطار الأسود البسيط للعمال")

        except Exception as e:
            print(f"❌ خطأ في تطبيق الإطار الأسود للعمال: {str(e)}")

    def apply_employees_advanced_visual_enhancements(self):
        """تطبيق التحسينات المرئية المتقدمة على جدول العمال مثل العملاء والموردين"""
        try:
            print("🎨 تطبيق التحسينات المرئية المتقدمة على جدول العمال...")

            # تطبيق التحسينات على كل صف
            for row in range(self.employees_table.rowCount()):
                # تحسين عرض البيانات
                self.enhance_employees_row_display(row)

                # تطبيق الألوان المتقدمة
                self.apply_employees_row_colors(row)

                # إضافة التلميحات المتقدمة
                self.add_employees_advanced_tooltips(row)

            print("✅ تم تطبيق التحسينات المرئية المتقدمة على جدول العمال بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في تطبيق التحسينات المرئية المتقدمة: {str(e)}")

    def enhance_employees_row_display(self, row):
        try:
            # تحسين عرض الاسم
            name_item = self.employees_table.item(row, 1)
            if name_item:
                name_text = name_item.text()
                if len(name_text) > 15:
                    name_item.setText(name_text[:12] + "...")
                    name_item.setToolTip(f"👤 الاسم الكامل: {name_text}")

            # تحسين عرض المنصب
            position_item = self.employees_table.item(row, 5)
            if position_item:
                position_text = position_item.text()
                if len(position_text) > 12:
                    position_item.setText(position_text[:9] + "...")
                    position_item.setToolTip(f"💼 المنصب الكامل: {position_text}")

            # تحسين عرض الملاحظات
            notes_item = self.employees_table.item(row, 6)
            if notes_item:
                notes_text = notes_item.text()
                if len(notes_text) > 15:
                    notes_item.setText(notes_text[:12] + "...")
                    notes_item.setToolTip(f"📝 الملاحظات الكاملة: {notes_text}")

        except Exception as e:
            print(f"❌ خطأ في تحسين عرض الصف {row}: {str(e)}")

    def apply_employees_row_colors(self, row):
        """تطبيق الألوان المتقدمة على صف العامل"""
        try:
            # تطبيق ألوان متدرجة حسب نوع البيانات
            for col in range(self.employees_table.columnCount()):
                item = self.employees_table.item(row, col)
                if item:
                    # ألوان خاصة للبيانات المفقودة
                    if "غير متوفر" in item.text() or "غير محدد" in item.text():
                        item.setForeground(QColor("#DC2626"))  # أحمر
                        item.setBackground(QColor("#FEE2E2"))  # خلفية حمراء فاتحة
                    # ألوان خاصة للحالة
                    elif col == 7:  # عمود الحالة
                        self.apply_employees_status_color(item)
                    # ألوان عادية للبيانات الصحيحة
                    else:
                        item.setForeground(QColor("#000000"))  # أسود
                        item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة

        except Exception as e:
            print(f"❌ خطأ في تطبيق ألوان الصف {row}: {str(e)}")

    def apply_employees_status_color(self, status_item):
        try:
            status_text = status_item.text()

            if "🟢 نشط" in status_text:
                status_item.setForeground(QColor("#38a169"))  # أخضر
                status_item.setBackground(QColor("#c6f6d5"))  # خلفية خضراء فاتحة
            elif "🆕 جديد" in status_text:
                status_item.setForeground(QColor("#17a2b8"))  # أزرق
                status_item.setBackground(QColor("#d1ecf1"))  # خلفية زرقاء فاتحة
            elif "⚫ خبير" in status_text:
                status_item.setForeground(QColor("#000000"))  # أسود
                status_item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة
            else:
                status_item.setForeground(QColor("#6c757d"))  # رمادي
                status_item.setBackground(QColor("#e9ecef"))  # خلفية رمادية

        except Exception as e:
            print(f"❌ خطأ في تطبيق لون الحالة: {str(e)}")

    def add_employees_advanced_tooltips(self, row):
        """إضافة تلميحات متقدمة للعامل"""
        try:
            # تلميح متقدم للاسم
            name_item = self.employees_table.item(row, 1)
            if name_item:
                name_item.setToolTip(f"👤 العامل: {name_item.text()}\n💡 انقر نقرتين لعرض التفاصيل")

            # تلميح متقدم للهاتف
            phone_item = self.employees_table.item(row, 2)
            if phone_item and "غير متوفر" not in phone_item.text():
                phone_item.setToolTip(f"📞 الهاتف: {phone_item.text()}\n💡 انقر نقرتين للاتصال\n📱 واتساب متاح")

            # تلميح متقدم للإيميل
            email_item = self.employees_table.item(row, 3)
            if email_item and "غير متوفر" not in email_item.text():
                email_item.setToolTip(f"📧 الإيميل: {email_item.text()}\n💡 انقر نقرتين لإرسال إيميل")

            # تلميح متقدم للمنصب
            position_item = self.employees_table.item(row, 5)
            if position_item:
                position_item.setToolTip(f"💼 المنصب: {position_item.text()}\n💡 انقر للتصفية حسب المنصب")

        except Exception as e:
            print(f"❌ خطأ في إضافة التلميحات للصف {row}: {str(e)}")

    def apply_employees_status_colors(self):
        try:
            print("🎨 تطبيق ألوان الحالة على جدول العمال...")

            for row in range(self.employees_table.rowCount()):
                status_item = self.employees_table.item(row, 7)  # عمود الحالة
                if status_item:
                    self.apply_employees_status_color(status_item)

            print("✅ تم تطبيق ألوان الحالة على جدول العمال بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في تطبيق ألوان الحالة: {str(e)}")

    def update_employees_header_colors(self):
        """تحديث ألوان عناوين جدول العمال"""
        try:
            print("🎨 تحديث ألوان عناوين جدول العمال...")

            header = self.employees_table.horizontalHeader()
                QHeaderView::section {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                    color: white;
                    border: 3px solid #5a67d8;
                    border-bottom: 5px solid #4c51bf;
                    padding: 15px 8px;
                    font-weight: bold;
                    font-size: 14px;
                    text-align: center;
                    min-height: 35px;
                    max-height: 55px;
                    letter-spacing: 1.0px;
                }
                QHeaderView::section:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #5a67d8, stop:0.5 #667eea, stop:1 #e0c3fc);
                    border: 3px solid #4c51bf;
                    border-bottom: 5px solid #3c366b;
                }
                QHeaderView::section:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #4c51bf, stop:0.5 #553c9a, stop:1 #c084fc);
                    border: 3px solid #3c366b;
                    border-bottom: 5px solid #2d3748;
                }
            """)

            print("✅ تم تحديث ألوان عناوين جدول العمال بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في تحديث ألوان العناوين: {str(e)}")

    def apply_employees_balance_colors(self):
        try:
            print("🎨 تطبيق ألوان الرصيد على جدول العمال...")

            for row in range(self.employees_table.rowCount()):
                balance_item = self.employees_table.item(row, 5)  # عمود الرصيد
                if balance_item:
                    balance_text = balance_item.text().replace(",", "")
                    try:
                        balance_value = float(balance_text)

                        if balance_value > 0:
                            # رصيد موجب - أخضر
                            balance_item.setForeground(QColor("#059669"))
                            balance_item.setBackground(QColor("#d1fae5"))
                            print(f"🟢 أخضر للصف {row} (رصيد: {balance_value})")
                        elif balance_value < 0:
                            # رصيد سالب - أحمر
                            balance_item.setForeground(QColor("#dc2626"))
                            balance_item.setBackground(QColor("#fee2e2"))
                            print(f"🔴 أحمر للصف {row} (رصيد: {balance_value})")
                        else:
                            # رصيد صفر - رمادي
                            balance_item.setForeground(QColor("#6b7280"))
                            balance_item.setBackground(QColor("#f3f4f6"))
                            print(f"⚪ رمادي للصف {row} (رصيد: {balance_value})")

                    except (ValueError, TypeError):
                        # في حالة عدم القدرة على تحويل النص إلى رقم
                        balance_item.setForeground(QColor("#000000"))
                        balance_item.setBackground(QColor("#f8f9fa"))

            print("✅ تم تطبيق ألوان الرصيد على جدول العمال بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في تطبيق ألوان الرصيد: {str(e)}")

    def force_employees_visual_updates(self):
        """فرض تطبيق التحديثات المرئية على جدول العمال"""
        try:
            print("🔄 فرض تطبيق التحديثات المرئية على جدول العمال...")

            # تطبيق ألوان الرصيد
            self.apply_employees_balance_colors()

            # تطبيق ألوان الحالة
            self.apply_employees_status_colors()

            # تطبيق التحسينات المرئية المتقدمة
            self.apply_employees_advanced_visual_enhancements()

            # فرض تحديث الجدول
            self.employees_table.update()
            self.employees_table.repaint()

            print("✅ تم فرض تطبيق التحديثات المرئية على جدول العمال بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في فرض التحديثات المرئية: {str(e)}")

    def refresh_data(self):
        try:
            print("🔄 بدء التحديث مع الحفاظ الكامل على التنسيق...")

            # الحصول على جميع العمال من قاعدة البيانات
            employees = self.session.query(Employee).all()

            # إذا لم توجد بيانات في قاعدة البيانات، استخدم البيانات التجريبية
            if not employees:
                print("🧪 لا توجد بيانات في قاعدة البيانات، استخدام البيانات التجريبية...")
                # إعادة إنشاء البيانات التجريبية مع الأيقونات
                self.create_simple_test_data()
                return

            # تحديث البيانات مع الحفاظ الكامل على التنسيق - استخدام التحديث الذكي
            self.smart_update_table_data(employees)

            # تطبيق التحسينات المرئية بعد التحديث مطابق للعملاء
            # self.apply_simple_enhancements()  # محذوف
            self.apply_employees_balance_colors()
            self.apply_employees_advanced_visual_enhancements()
            self.apply_employees_status_colors()
            # self.apply_employees_final_advanced_enhancements()  # محذوف

            print("✅ تم تحديث بيانات العمال مع الحفاظ على التحسينات بنجاح")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تحميل بيانات العمال: {str(e)}")

    def populate_table(self, employees):
        """ملء جدول العمال بالبيانات المحسنة والمطورة"""
        print("📊 بدء ملء جدول العمال بالبيانات...")

        # مسح الجدول
        self.employees_table.setRowCount(0)

        if not employees:
            print("🧪 لا توجد بيانات، إنشاء بيانات تجريبية...")
            self.create_simple_test_data()
            return

        # إعداد الخطوط الموحدة
        unified_font = QFont("Segoe UI", 11, QFont.Bold)  # خط مناسب مع Bold
        unified_font.setLetterSpacing(QFont.AbsoluteSpacing, 0.5)
        unified_font.setStyleHint(QFont.SansSerif)
        unified_font.setWeight(QFont.Bold)

        unified_bold_font = QFont("Segoe UI", 11, QFont.Bold)  # نفس الحجم للعريض
        unified_bold_font.setLetterSpacing(QFont.AbsoluteSpacing, 0.8)
        unified_bold_font.setStyleHint(QFont.SansSerif)
        unified_bold_font.setWeight(QFont.ExtraBold)

        # ملء البيانات مع تنسيق متطور
        for row, employee in enumerate(employees):
            self.employees_table.insertRow(row)

            # حساب الرصيد أولاً لتحديد الأيقونة (سيتم استخدامه في عدة أماكن)
            balance = random.choice([-5000, -2000, 0, 1500, 3000, 8000, 15000])  # رصيد تجريبي

            # 1. الرقم المسلسل مع تصميم متطور ومميز وأيقونة حسب الحالة

            # اختيار الأيقونة حسب الرصيد والحالة مثل العملاء
            if balance > 10000:
                icon = "💎"  # VIP - مبلغ كبير جداً
            elif balance > 1000:
                icon = "⭐"  # ممتاز - مبلغ كبير
            elif balance > 0:
                icon = "🟢"  # نشط - مبلغ موجب
            elif balance == 0:
                icon = "✅"  # عادي - بدون رصيد
            elif balance > -1000:
                icon = "🟡"  # مدين بسيط
            elif balance > -5000:
                icon = "⚠️"  # تحذير - مدين متوسط
            else:
                icon = "🚨"  # خطر - مدين كبير

            id_display = f"{employee.id} {icon}"
            id_item = QTableWidgetItem(id_display)
            id_item.setTextAlignment(Qt.AlignCenter)
            id_item.setFont(unified_bold_font)

            # ألوان متدرجة حسب رقم العامل مثل العملاء
            if employee.id <= 10:
                # عمال أوائل - ذهبي
                id_item.setForeground(QColor("#B8860B"))  # ذهبي داكن
                id_item.setBackground(QColor("#FFF8DC"))  # كريمي فاتح
                id_item.setToolTip(f"👑 عامل مؤسس رقم: {employee.id}")
            elif employee.id <= 50:
                # عمال مبكرين - فضي
                id_item.setForeground(QColor("#4682B4"))  # أزرق فولاذي
                id_item.setBackground(QColor("#F0F8FF"))  # أزرق فاتح جداً
                id_item.setToolTip(f"⭐ عامل مبكر رقم: {employee.id}")
            else:
                # عمال عاديين - رمادي أنيق
                id_item.setForeground(QColor("#2C3E50"))  # رمادي داكن
                id_item.setBackground(QColor("#ECF0F1"))  # رمادي فاتح
                id_item.setToolTip(f"🆔 رقم العامل: {employee.id}")

            self.employees_table.setItem(row, 0, id_item)

            # 2. اسم العامل مع تصميم جذاب ومتطور مثل العملاء
            # تحسين عرض الاسم
            display_name = employee.name.title() if employee.name else "غير محدد"
            if len(display_name) > 20:
                display_name = display_name[:17] + "..."

            name_item = QTableWidgetItem(display_name)
            name_item.setTextAlignment(Qt.AlignCenter)
            name_item.setFont(unified_bold_font)

            # ألوان متدرجة حسب الرصيد وحالة العامل مثل العملاء
            if balance > 10000:
                # عمال VIP - ذهبي فاخر
                name_item.setForeground(QColor("#8B4513"))  # بني ذهبي
                name_item.setBackground(QColor("#FFF8DC"))  # كريمي ذهبي
                name_item.setToolTip(f"👑 عامل VIP: {employee.name}\n💰 رصيد عالي: {balance:,.0f} ر.س")
            elif balance > 0:
                # عمال نشطين - أخضر أنيق
                name_item.setForeground(QColor("#2F4F4F"))  # أخضر داكن
                name_item.setBackground(QColor("#F0FFF0"))  # أخضر فاتح جداً
                name_item.setToolTip(f"✅ عامل نشط: {employee.name}\n💰 رصيد موجب: {balance:,.0f} ر.س")
            elif balance < 0:
                # عمال مدينين - أحمر تحذيري
                name_item.setForeground(QColor("#8B0000"))  # أحمر داكن
                name_item.setBackground(QColor("#FFE4E1"))  # أحمر فاتح جداً
                name_item.setToolTip(f"⚠️ عامل مدين: {employee.name}\n💸 مبلغ مستحق: {abs(balance):,.0f} ر.س")
            else:
                # عمال عاديين - رمادي أنيق
                name_item.setForeground(QColor("#2C3E50"))  # رمادي داكن
                name_item.setBackground(QColor("#F8F9FA"))  # رمادي فاتح جداً
                name_item.setToolTip(f"👤 عامل عادي: {employee.name}\n💰 رصيد صفر")

            self.employees_table.setItem(row, 1, name_item)

            # 3. الهاتف مع تصميم احترافي ومتطور مثل العملاء
            if employee.phone:
                # تنسيق رقم الهاتف بشكل جميل
                phone_clean = employee.phone.replace(" ", "").replace("-", "")
                if phone_clean.startswith("966"):
                    phone_display = f"+966 {phone_clean[3:6]} {phone_clean[6:9]} {phone_clean[9:]}"
                elif phone_clean.startswith("05"):
                    phone_display = f"0{phone_clean[1:3]} {phone_clean[3:6]} {phone_clean[6:]}"
                else:
                    phone_display = employee.phone

                phone_item = QTableWidgetItem(phone_display)
                phone_item.setFont(unified_bold_font)

                # ألوان متدرجة حسب نوع الرقم مثل العملاء
                if phone_clean.startswith("966") or phone_clean.startswith("00966"):
                    # رقم دولي - أزرق فاخر
                    phone_item.setForeground(QColor("#1E3A8A"))  # أزرق داكن
                    phone_item.setBackground(QColor("#DBEAFE"))  # أزرق فاتح
                    phone_item.setToolTip(f"🌍 رقم دولي: {phone_display}\n📞 انقر نقرتين للاتصال\n📱 واتساب متاح")
                elif phone_clean.startswith("05"):
                    # رقم جوال سعودي - أخضر مميز
                    phone_item.setForeground(QColor("#059669"))  # أخضر داكن
                    phone_item.setBackground(QColor("#D1FAE5"))  # أخضر فاتح
                    phone_item.setToolTip(f"📱 جوال سعودي: {phone_display}\n📞 انقر نقرتين للاتصال\n💬 واتساب متاح")
                else:
                    # رقم عادي - بنفسجي أنيق
                    phone_item.setForeground(QColor("#7C3AED"))  # بنفسجي داكن
                    phone_item.setBackground(QColor("#EDE9FE"))  # بنفسجي فاتح
                    phone_item.setToolTip(f"☎️ هاتف: {phone_display}\n📞 انقر نقرتين للاتصال")
            else:
                phone_item = QTableWidgetItem("📵 غير متوفر")
                phone_item.setFont(unified_font)
                phone_item.setForeground(QColor("#DC2626"))  # أحمر للغير متوفر
                phone_item.setBackground(QColor("#FEE2E2"))  # أحمر فاتح جداً
                phone_item.setToolTip("📵 لا يوجد رقم هاتف مسجل\n💡 يمكن إضافة رقم من خلال التعديل")

            phone_item.setTextAlignment(Qt.AlignCenter)
            self.employees_table.setItem(row, 2, phone_item)

            # 4. البريد الإلكتروني مع تصميم متطور مثل العملاء
            if hasattr(employee, 'email') and employee.email:
                email_text = employee.email
                if len(email_text) > 25:
                    email_display = email_text[:22] + "..."
                else:
                    email_display = email_text

                email_item = QTableWidgetItem(email_display)
                email_item.setFont(unified_font)

                # تحديد نوع البريد الإلكتروني - جميع الإيميلات بلون أسود موحد مثل العملاء
                # تم توحيد لون جميع الإيميلات إلى الأسود لتجنب الالتباس
                email_item.setForeground(QColor("#000000"))  # أسود موحد لجميع الإيميلات
                email_item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة موحدة

                # تحديد نوع الإيميل في التلميح فقط
                if "@gmail.com" in employee.email.lower():
                    email_item.setToolTip(f"📧 Gmail: {employee.email}\n💡 انقر نقرتين لإرسال إيميل")
                elif "@outlook.com" in employee.email.lower() or "@hotmail.com" in employee.email.lower():
                    email_item.setToolTip(f"📧 Outlook: {employee.email}\n💡 انقر نقرتين لإرسال إيميل")
                elif "@yahoo.com" in employee.email.lower():
                    email_item.setToolTip(f"📧 Yahoo: {employee.email}\n💡 انقر نقرتين لإرسال إيميل")
                else:
                    email_item.setToolTip(f"📧 إيميل: {employee.email}\n💡 انقر نقرتين لإرسال إيميل")
            else:
                email_item = QTableWidgetItem("📧 غير متوفر")
                email_item.setFont(unified_font)
                email_item.setForeground(QColor("#DC2626"))  # أحمر للغير متوفر
                email_item.setBackground(QColor("#FEE2E2"))  # أحمر فاتح
                email_item.setToolTip("📧 لا يوجد بريد إلكتروني\n💡 يمكن إضافة إيميل من خلال التعديل")

            email_item.setTextAlignment(Qt.AlignCenter)
            self.employees_table.setItem(row, 3, email_item)

            # 5. العنوان
            address_text = employee.address if hasattr(employee, 'address') and employee.address else "غير متوفر"
            if len(address_text) > 20:  # تقصير العناوين الطويلة
                address_display = address_text[:17] + "..."
            else:
                address_display = address_text

            address_item = QTableWidgetItem(address_display)
            address_item.setTextAlignment(Qt.AlignCenter)  # توسيط النص
            if hasattr(employee, 'address') and employee.address:
                address_item.setFont(unified_bold_font)  # خط عريض أسود
                address_item.setForeground(QColor("#000000"))  # أسود عريض
                address_item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة
                address_item.setToolTip(f"📍 العنوان: {employee.address}\n💡 انقر نقرتين لفتح الخريطة")
            else:
                address_item.setFont(unified_bold_font)  # خط عريض
                address_item.setForeground(QColor("#DC2626"))  # أحمر للغير متوفر
                address_item.setBackground(QColor("#FEE2E2"))  # أحمر فاتح
                address_item.setToolTip("📍 لا يوجد عنوان مسجل\n💡 يمكن إضافة عنوان من خلال التعديل")
            self.employees_table.setItem(row, 4, address_item)

            # 6. الرصيد مع تصميم متطور وجذاب مثل العملاء تماماً (بدون كسور وبدون ر.س)
            balance_text = f"{balance:,.0f}"
            balance_item = QTableWidgetItem(balance_text)
            balance_item.setTextAlignment(Qt.AlignCenter)
            # تطبيق خط موحد ثابت للرصيد مثل العملاء
            balance_font = QFont("Segoe UI", 11, QFont.Bold)  # حجم ثابت 11
            balance_item.setFont(balance_font)

            # تلوين الرصيد حسب القيمة مع ألوان واضحة ومميزة مثل العملاء
            if balance > 10000:
                # رصيد عالي جداً - أخضر ذهبي مميز
                balance_item.setForeground(QColor("#1a5f3f"))  # أخضر داكن
                balance_item.setBackground(QColor("#d4edda"))  # أخضر فاتح
                balance_item.setToolTip(f"💰 رصيد ممتاز: {balance_text}\n⭐ عامل VIP")
            elif balance > 0:
                # رصيد موجب - أخضر واضح
                balance_item.setForeground(QColor("#155724"))  # أخضر داكن
                balance_item.setBackground(QColor("#d1ecf1"))  # أخضر فاتح جداً
                balance_item.setToolTip(f"💰 رصيد موجب: {balance_text}\n✅ عامل نشط")
            elif balance < -5000:
                # رصيد سالب عالي - أحمر قوي
                balance_item.setForeground(QColor("#721c24"))  # أحمر داكن جداً
                balance_item.setBackground(QColor("#f8d7da"))  # أحمر فاتح
                balance_item.setToolTip(f"💰 رصيد مدين عالي: {balance_text}\n🚨 يحتاج متابعة")
            elif balance < 0:
                # رصيد سالب - أحمر واضح
                balance_item.setForeground(QColor("#856404"))  # برتقالي داكن
                balance_item.setBackground(QColor("#fff3cd"))  # أصفر فاتح
                balance_item.setToolTip(f"💰 رصيد مدين: {balance_text}\n⚠️ يحتاج متابعة")
            else:
                # رصيد صفر - رمادي
                balance_item.setForeground(QColor("#495057"))  # رمادي داكن
                balance_item.setBackground(QColor("#e9ecef"))  # رمادي فاتح
                balance_item.setToolTip(f"💰 رصيد صفر: {balance_text}\n➖ لا يوجد رصيد")

            self.employees_table.setItem(row, 5, balance_item)

            # 7. المنصب/الوظيفة
            position_text = employee.position or "غير محدد"
            if len(position_text) > 15:  # تقصير المناصب الطويلة
                position_display = position_text[:12] + "..."
            else:
                position_display = position_text

            position_item = QTableWidgetItem(position_display)
            position_item.setTextAlignment(Qt.AlignCenter)  # توسيط النص
            if employee.position:
                position_item.setFont(unified_bold_font)  # خط عريض أسود
                position_item.setForeground(QColor("#000000"))  # أسود عريض
                position_item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة
                position_item.setToolTip(f"💼 المنصب: {employee.position}")
            else:
                position_item.setFont(unified_bold_font)  # خط عريض
                position_item.setForeground(QColor("#DC2626"))  # أحمر للغير متوفر
                position_item.setBackground(QColor("#FEE2E2"))  # أحمر فاتح
                position_item.setToolTip("💼 لا يوجد منصب محدد\n💡 يمكن إضافة منصب من خلال التعديل")
            self.employees_table.setItem(row, 6, position_item)

            # 8. الملاحظات مع عرض أفضل
            notes_text = employee.notes or "غير متوفر"
            if len(notes_text) > 25:  # زيادة الحد الأقصى للنص
                notes_display = notes_text[:22] + "..."
            else:
                notes_display = notes_text

            notes_item = QTableWidgetItem(notes_display)
            notes_item.setTextAlignment(Qt.AlignCenter)  # توسيط النص
            if employee.notes:
                notes_item.setFont(unified_bold_font)  # خط عريض أسود
                notes_item.setForeground(QColor("#000000"))  # أسود عريض
                notes_item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة
                notes_item.setToolTip(employee.notes)  # النص الكامل في التلميح
            else:
                notes_item.setFont(unified_bold_font)  # خط عريض
                notes_item.setForeground(QColor("#DC2626"))  # أحمر للغير متوفر
                notes_item.setBackground(QColor("#FEE2E2"))  # أحمر فاتح
                notes_item.setToolTip("📝 لا توجد ملاحظات\n💡 يمكن إضافة ملاحظات من خلال التعديل")
            self.employees_table.setItem(row, 7, notes_item)

            # 8. الحالة مع تصميم احترافي محسن مثل العملاء
            # تحديد الحالة بناءً على الرصيد مع إضافة حالة "عادي" مثل العملاء
            if balance > 0:
                status_text = "🟢 نشط"
                status_color = QColor("#38a169")  # أخضر للنشط
                status_bg = QColor("#c6f6d5")     # خلفية خضراء فاتحة
            elif balance == 0:
                status_text = "⚫ عادى"  # النص مع علامة سوداء
                status_color = QColor("#000000")  # أسود للعادى
                status_bg = QColor("#f8f9fa")     # خلفية رمادية فاتحة
            else:
                status_text = "🔴 مدين"
                status_color = QColor("#e53e3e")  # أحمر للمدين
                status_bg = QColor("#fed7d7")     # خلفية حمراء فاتحة

            # إنشاء عنصر الحالة مع تنسيق موحد
            status_item = QTableWidgetItem(status_text)
            status_item.setTextAlignment(Qt.AlignCenter)

            # تطبيق خط موحد للحالة (حجم ثابت 12 عريض) مثل العملاء
            status_font = QFont("Segoe UI", 12, QFont.Bold)
            status_item.setFont(status_font)

            # تطبيق الألوان
            status_item.setForeground(status_color)
            status_item.setBackground(status_bg)

            # إضافة تلميح للحالة مثل العملاء
            if balance > 0:
                status_item.setToolTip(f"🟢 عامل نشط\n💰 الرصيد: {balance:,.0f}")
            elif balance == 0:
                status_item.setToolTip(f"⚫ عامل عادى\n💰 الرصيد: صفر")
            else:
                status_item.setToolTip(f"🔴 عامل مدين\n💰 المبلغ المستحق: {abs(balance):,.0f}")

            self.employees_table.setItem(row, 8, status_item)

            # 9. التاريخ مثل العملاء
            if hasattr(employee, 'hire_date') and employee.hire_date:
                try:
                    # التعامل مع أنواع التاريخ المختلفة
                    if isinstance(employee.hire_date, datetime):
                        date_text = employee.hire_date.strftime('%Y-%m-%d')
                    elif isinstance(employee.hire_date, date):
                        date_text = employee.hire_date.strftime('%Y-%m-%d')
                    else:
                        # محاولة تحويل النص إلى تاريخ
                        parsed_date = datetime.strptime(str(employee.hire_date), '%Y-%m-%d')
                        date_text = parsed_date.strftime('%Y-%m-%d')

                    date_item = QTableWidgetItem(date_text)
                    date_item.setForeground(QColor("#4a5568"))  # رمادي للتاريخ الموجود
                    date_item.setBackground(QColor("#f7fafc"))  # خلفية رمادية فاتحة
                    date_item.setToolTip(f"📅 تاريخ التوظيف: {date_text}")
                except (ValueError, TypeError, AttributeError):
                    date_text = "غير متوفر"
                    date_item = QTableWidgetItem(date_text)
                    date_item.setForeground(QColor("#DC2626"))  # أحمر للغير متوفر
                    date_item.setBackground(QColor("#FEE2E2"))  # أحمر فاتح
                    date_item.setToolTip("📅 تاريخ التوظيف غير صحيح")
            else:
                date_text = "غير متوفر"
                date_item = QTableWidgetItem(date_text)
                date_item.setForeground(QColor("#DC2626"))  # أحمر للغير متوفر
                date_item.setBackground(QColor("#FEE2E2"))  # أحمر فاتح
                date_item.setToolTip("📅 لا يوجد تاريخ توظيف مسجل")

            date_item.setTextAlignment(Qt.AlignCenter)
            date_item.setFont(unified_font)  # خط موحد مثل العملاء
            self.employees_table.setItem(row, 9, date_item)

        # تطبيق الأعراض الجديدة مباشرة مع فرض التطبيق
        print("📏 فرض تطبيق الأعراض الجديدة...")

        # أولاً: تعيين الأعمدة الثابتة
        header = self.employees_table.horizontalHeader()
        header.setSectionResizeMode(9, header.Fixed)  # التاريخ ثابت

        # ثانياً: تطبيق الأعراض المحسنة مطابقة للعملاء مع إضافة عمود الرصيد
        self.employees_table.setColumnWidth(0, 120)  # الرقم - مطابق للعملاء
        self.employees_table.setColumnWidth(1, 150)  # الاسم - مطابق للعملاء
        self.employees_table.setColumnWidth(2, 220)  # الهاتف - مطابق للعملاء
        self.employees_table.setColumnWidth(3, 180)  # الإيميل - مطابق للعملاء
        self.employees_table.setColumnWidth(4, 150)  # العنوان - مطابق للعملاء
        self.employees_table.setColumnWidth(5, 200)  # الرصيد - مطابق للعملاء
        self.employees_table.setColumnWidth(6, 155)  # المنصب - مطابق لعمود الرصيد في الموردين
        self.employees_table.setColumnWidth(7, 105)  # الملاحظات - مطابق للعملاء
        self.employees_table.setColumnWidth(8, 120)  # الحالة - مطابق للعملاء
        self.employees_table.setColumnWidth(9, 154)  # التاريخ - مطابق للعملاء

        print("✅ تم فرض تطبيق الأعراض الجديدة")

        print("✅ تم ملء الجدول بالبيانات المحسنة والمطورة")

        # تطبيق التحسينات النهائية بعد ملء البيانات
        self.apply_employees_final_enhancements()

        # تطبيق التحسينات المرئية المتقدمة مثل العملاء والموردين
        self.apply_employees_advanced_visual_enhancements()

        # تطبيق ألوان الرصيد مثل العملاء
        self.apply_employees_balance_colors()

        # تطبيق ألوان الحالة المتطورة
        self.apply_employees_status_colors()

        # تطبيق التحسينات النهائية المتقدمة
        # self.apply_employees_final_advanced_enhancements()  # محذوف

        # فرض تطبيق جميع التحديثات المرئية
        self.force_employees_visual_updates()

    def create_simple_test_data(self):
        try:
            print("🧪 إنشاء بيانات تجريبية بسيطة للعمال...")

            # بيانات تجريبية متنوعة مطابقة للعملاء
            test_employees = [
                {"id": "🆔 1", "name": "أحمد محمد علي", "phone": "0501234567", "email": "<EMAIL>", "address": "الرياض", "balance": "15000", "position": "مهندس برمجيات", "notes": "عامل متميز", "status": "🟢 نشط"},
                {"id": "🆔 2", "name": "فاطمة أحمد السالم", "phone": "0509876543", "email": "<EMAIL>", "address": "جدة", "balance": "-8000", "position": "مديرة مشاريع", "notes": "قائدة فريق", "status": "🔴 مدين"},
                {"id": "🆔 3", "name": "محمد عبدالله الغامدي", "phone": "0551122334", "email": "<EMAIL>", "address": "الدمام", "balance": "2500", "position": "محاسب مالي", "notes": "دقيق في العمل", "status": "🟢 نشط"},
                {"id": "🆔 4", "name": "نورا سعد الحربي", "phone": "0556677889", "email": "<EMAIL>", "address": "مكة", "balance": "-12000", "position": "مصممة جرافيك", "notes": "مبدعة في التصميم", "status": "🔴 مدين"},
                {"id": "🆔 5", "name": "خالد يوسف المطيري", "phone": "0544455667", "email": "<EMAIL>", "address": "الطائف", "balance": "25000", "position": "مطور تطبيقات", "notes": "متخصص في الجوال", "status": "🟢 نشط"}
            ]

            # إضافة البيانات للجدول مع تطوير متقدم مثل العملاء
            for row, employee_data in enumerate(test_employees):
                self.employees_table.insertRow(row)

                # إعداد الخطوط الموحدة مثل العملاء
                unified_font = QFont("Segoe UI", 11, QFont.Bold)
                unified_font.setLetterSpacing(QFont.AbsoluteSpacing, 0.5)
                unified_font.setStyleHint(QFont.SansSerif)
                unified_font.setWeight(QFont.Bold)

                unified_bold_font = QFont("Segoe UI", 11, QFont.Bold)
                unified_bold_font.setLetterSpacing(QFont.AbsoluteSpacing, 0.8)
                unified_bold_font.setStyleHint(QFont.SansSerif)
                unified_bold_font.setWeight(QFont.ExtraBold)

                # حساب الرصيد أولاً لتحديد الأيقونة
                balance = float(employee_data["balance"].replace(',', ''))

                # 1. الرقم مع أيقونة ديناميكية حسب الرصيد مثل العملاء
                if balance > 10000:
                    icon = "💎"  # VIP - مبلغ كبير جداً
                elif balance > 1000:
                    icon = "⭐"  # ممتاز - مبلغ كبير
                elif balance > 0:
                    icon = "🟢"  # نشط - مبلغ موجب
                elif balance == 0:
                    icon = "✅"  # عادي - بدون رصيد
                elif balance > -1000:
                    icon = "🟡"  # مدين بسيط
                elif balance > -5000:
                    icon = "⚠️"  # تحذير - مدين متوسط
                else:
                    icon = "🚨"  # خطر - مدين كبير

                employee_id = row + 1
                id_display = f"{employee_id} {icon}"
                id_item = QTableWidgetItem(id_display)
                id_item.setTextAlignment(Qt.AlignCenter)
                id_item.setFont(unified_bold_font)

                # ألوان متدرجة حسب رقم العامل مثل العملاء
                if employee_id <= 10:
                    # عمال أوائل - ذهبي
                    id_item.setForeground(QColor("#B8860B"))  # ذهبي داكن
                    id_item.setBackground(QColor("#FFF8DC"))  # كريمي فاتح
                    id_item.setToolTip(f"👑 عامل مؤسس رقم: {employee_id}")
                elif employee_id <= 50:
                    # عمال مبكرين - فضي
                    id_item.setForeground(QColor("#4682B4"))  # أزرق فولاذي
                    id_item.setBackground(QColor("#F0F8FF"))  # أزرق فاتح جداً
                    id_item.setToolTip(f"⭐ عامل مبكر رقم: {employee_id}")
                else:
                    # عمال عاديين - رمادي أنيق
                    id_item.setForeground(QColor("#2C3E50"))  # رمادي داكن
                    id_item.setBackground(QColor("#ECF0F1"))  # رمادي فاتح
                    id_item.setToolTip(f"🆔 رقم العامل: {employee_id}")

                self.employees_table.setItem(row, 0, id_item)

                # 2. اسم العامل مع ألوان متدرجة حسب الرصيد مثل العملاء
                display_name = employee_data["name"].title()
                if len(display_name) > 20:
                    display_name = display_name[:17] + "..."

                name_item = QTableWidgetItem(display_name)
                name_item.setTextAlignment(Qt.AlignCenter)
                name_item.setFont(unified_bold_font)

                # ألوان متدرجة حسب الرصيد وحالة العامل مثل العملاء
                if balance > 10000:
                    # عمال VIP - ذهبي فاخر
                    name_item.setForeground(QColor("#8B4513"))  # بني ذهبي
                    name_item.setBackground(QColor("#FFF8DC"))  # كريمي ذهبي
                    name_item.setToolTip(f"👑 عامل VIP: {employee_data['name']}\n💰 رصيد عالي: {balance:,.0f} ر.س")
                elif balance > 0:
                    # عمال نشطين - أخضر أنيق
                    name_item.setForeground(QColor("#2F4F4F"))  # أخضر داكن
                    name_item.setBackground(QColor("#F0FFF0"))  # أخضر فاتح جداً
                    name_item.setToolTip(f"✅ عامل نشط: {employee_data['name']}\n💰 رصيد موجب: {balance:,.0f} ر.س")
                elif balance < 0:
                    # عمال مدينين - أحمر تحذيري
                    name_item.setForeground(QColor("#8B0000"))  # أحمر داكن
                    name_item.setBackground(QColor("#FFE4E1"))  # أحمر فاتح جداً
                    name_item.setToolTip(f"⚠️ عامل مدين: {employee_data['name']}\n💸 مبلغ مستحق: {abs(balance):,.0f} ر.س")
                else:
                    # عمال عاديين - رمادي أنيق
                    name_item.setForeground(QColor("#2C3E50"))  # رمادي داكن
                    name_item.setBackground(QColor("#F8F9FA"))  # رمادي فاتح جداً
                    name_item.setToolTip(f"👤 عامل عادي: {employee_data['name']}\n💰 رصيد صفر")

                self.employees_table.setItem(row, 1, name_item)

                # 3. الهاتف مع ألوان متدرجة حسب نوع الرقم مثل العملاء
                phone_clean = employee_data["phone"].replace(" ", "").replace("-", "")
                if phone_clean.startswith("966"):
                    phone_display = f"+966 {phone_clean[3:6]} {phone_clean[6:9]} {phone_clean[9:]}"
                elif phone_clean.startswith("05"):
                    phone_display = f"0{phone_clean[1:3]} {phone_clean[3:6]} {phone_clean[6:]}"
                else:
                    phone_display = employee_data["phone"]

                phone_item = QTableWidgetItem(phone_display)
                phone_item.setFont(unified_bold_font)
                phone_item.setTextAlignment(Qt.AlignCenter)

                # ألوان متدرجة حسب نوع الرقم مثل العملاء
                if phone_clean.startswith("966") or phone_clean.startswith("00966"):
                    # رقم دولي - أزرق فاخر
                    phone_item.setForeground(QColor("#1E3A8A"))  # أزرق داكن
                    phone_item.setBackground(QColor("#DBEAFE"))  # أزرق فاتح
                    phone_item.setToolTip(f"🌍 رقم دولي: {phone_display}\n📞 انقر نقرتين للاتصال\n📱 واتساب متاح")
                elif phone_clean.startswith("05"):
                    # رقم جوال سعودي - أخضر مميز
                    phone_item.setForeground(QColor("#059669"))  # أخضر داكن
                    phone_item.setBackground(QColor("#D1FAE5"))  # أخضر فاتح
                    phone_item.setToolTip(f"📱 جوال سعودي: {phone_display}\n📞 انقر نقرتين للاتصال\n💬 واتساب متاح")
                else:
                    # رقم عادي - بنفسجي أنيق
                    phone_item.setForeground(QColor("#7C3AED"))  # بنفسجي داكن
                    phone_item.setBackground(QColor("#EDE9FE"))  # بنفسجي فاتح
                    phone_item.setToolTip(f"☎️ هاتف: {phone_display}\n📞 انقر نقرتين للاتصال")

                self.employees_table.setItem(row, 2, phone_item)

                # 4. البريد الإلكتروني - لون أسود موحد مثل العملاء
                email_text = employee_data["email"]
                if len(email_text) > 25:
                    email_display = email_text[:22] + "..."
                else:
                    email_display = email_text

                email_item = QTableWidgetItem(email_display)
                email_item.setFont(unified_font)
                email_item.setTextAlignment(Qt.AlignCenter)

                # لون أسود موحد لجميع الإيميلات مثل العملاء
                email_item.setForeground(QColor("#000000"))  # أسود موحد
                email_item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة موحدة

                # تحديد نوع الإيميل في التلميح فقط
                if "@gmail.com" in employee_data["email"].lower():
                    email_item.setToolTip(f"📧 Gmail: {employee_data['email']}\n💡 انقر نقرتين لإرسال إيميل")
                elif "@outlook.com" in employee_data["email"].lower() or "@hotmail.com" in employee_data["email"].lower():
                    email_item.setToolTip(f"📧 Outlook: {employee_data['email']}\n💡 انقر نقرتين لإرسال إيميل")
                elif "@yahoo.com" in employee_data["email"].lower():
                    email_item.setToolTip(f"📧 Yahoo: {employee_data['email']}\n💡 انقر نقرتين لإرسال إيميل")
                else:
                    email_item.setToolTip(f"📧 إيميل: {employee_data['email']}\n💡 انقر نقرتين لإرسال إيميل")

                self.employees_table.setItem(row, 3, email_item)

                # 5. العنوان
                address_text = employee_data["address"]
                if len(address_text) > 20:
                    address_display = address_text[:17] + "..."
                else:
                    address_display = address_text

                address_item = QTableWidgetItem(address_display)
                address_item.setTextAlignment(Qt.AlignCenter)
                address_item.setFont(unified_bold_font)
                address_item.setForeground(QColor("#000000"))  # أسود عريض
                address_item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة
                address_item.setToolTip(f"📍 العنوان: {employee_data['address']}\n💡 انقر نقرتين لفتح الخريطة")
                self.employees_table.setItem(row, 4, address_item)

                # 6. الرصيد مع تصميم متطور وجذاب مثل العملاء تماماً
                balance_text = f"{balance:,.0f}"
                balance_item = QTableWidgetItem(balance_text)
                balance_item.setTextAlignment(Qt.AlignCenter)
                # تطبيق خط موحد ثابت للرصيد مثل العملاء
                balance_font = QFont("Segoe UI", 11, QFont.Bold)  # حجم ثابت 11
                balance_item.setFont(balance_font)

                # تلوين الرصيد حسب القيمة مع ألوان واضحة ومميزة مثل العملاء
                if balance > 10000:
                    # رصيد عالي جداً - أخضر ذهبي مميز
                    balance_item.setForeground(QColor("#1a5f3f"))  # أخضر داكن
                    balance_item.setBackground(QColor("#d4edda"))  # أخضر فاتح
                    balance_item.setToolTip(f"💰 رصيد ممتاز: {balance_text}\n⭐ عامل VIP")
                elif balance > 0:
                    # رصيد موجب - أخضر واضح
                    balance_item.setForeground(QColor("#155724"))  # أخضر داكن
                    balance_item.setBackground(QColor("#d1ecf1"))  # أخضر فاتح جداً
                    balance_item.setToolTip(f"💰 رصيد موجب: {balance_text}\n✅ عامل نشط")
                elif balance < -5000:
                    # رصيد سالب عالي - أحمر قوي
                    balance_item.setForeground(QColor("#721c24"))  # أحمر داكن جداً
                    balance_item.setBackground(QColor("#f8d7da"))  # أحمر فاتح
                    balance_item.setToolTip(f"💰 رصيد مدين عالي: {balance_text}\n🚨 يحتاج متابعة")
                elif balance < 0:
                    # رصيد سالب - أحمر واضح
                    balance_item.setForeground(QColor("#856404"))  # برتقالي داكن
                    balance_item.setBackground(QColor("#fff3cd"))  # أصفر فاتح
                    balance_item.setToolTip(f"💰 رصيد مدين: {balance_text}\n⚠️ يحتاج متابعة")
                else:
                    # رصيد صفر - رمادي
                    balance_item.setForeground(QColor("#495057"))  # رمادي داكن
                    balance_item.setBackground(QColor("#e9ecef"))  # رمادي فاتح
                    balance_item.setToolTip(f"💰 رصيد صفر: {balance_text}\n➖ لا يوجد رصيد")

                self.employees_table.setItem(row, 5, balance_item)
                print(f"🎨 تم تلوين البيانات التجريبية للصف {row}: {balance}")

                # 7. المنصب/الوظيفة
                position_text = employee_data["position"]
                if len(position_text) > 15:
                    position_display = position_text[:12] + "..."
                else:
                    position_display = position_text

                position_item = QTableWidgetItem(position_display)
                position_item.setTextAlignment(Qt.AlignCenter)
                position_item.setFont(unified_bold_font)
                position_item.setForeground(QColor("#000000"))  # أسود عريض
                position_item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة
                position_item.setToolTip(f"💼 المنصب: {employee_data['position']}")
                self.employees_table.setItem(row, 6, position_item)

                # 8. الملاحظات
                notes_text = employee_data["notes"]
                if len(notes_text) > 25:
                    notes_display = notes_text[:22] + "..."
                else:
                    notes_display = notes_text

                notes_item = QTableWidgetItem(notes_display)
                notes_item.setTextAlignment(Qt.AlignCenter)
                notes_item.setFont(unified_bold_font)
                notes_item.setForeground(QColor("#000000"))  # أسود عريض
                notes_item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة
                notes_item.setToolTip(employee_data["notes"])  # النص الكامل في التلميح
                self.employees_table.setItem(row, 7, notes_item)

                # 9. الحالة مع تصميم احترافي محسن مثل العملاء
                # تحديد الحالة بناءً على الرصيد مع إضافة حالة "عادي" مثل العملاء
                if balance > 0:
                    status_text = "🟢 نشط"
                    status_color = QColor("#38a169")  # أخضر للنشط
                    status_bg = QColor("#c6f6d5")     # خلفية خضراء فاتحة
                elif balance == 0:
                    status_text = "⚫ عادى"  # النص مع علامة سوداء
                    status_color = QColor("#000000")  # أسود للعادى
                    status_bg = QColor("#f8f9fa")     # خلفية رمادية فاتحة
                else:
                    status_text = "🔴 مدين"
                    status_color = QColor("#e53e3e")  # أحمر للمدين
                    status_bg = QColor("#fed7d7")     # خلفية حمراء فاتحة

                # إنشاء عنصر الحالة مع تنسيق موحد
                status_item = QTableWidgetItem(status_text)
                status_item.setTextAlignment(Qt.AlignCenter)

                # تطبيق خط موحد للحالة (حجم ثابت 12 عريض) مثل العملاء
                status_font = QFont("Segoe UI", 12, QFont.Bold)
                status_item.setFont(status_font)

                # تطبيق الألوان
                status_item.setForeground(status_color)
                status_item.setBackground(status_bg)

                # إضافة تلميح للحالة مثل العملاء
                if balance > 0:
                    status_item.setToolTip(f"🟢 عامل نشط\n💰 الرصيد: {balance:,.0f}")
                elif balance == 0:
                    status_item.setToolTip(f"⚫ عامل عادى\n💰 الرصيد: صفر")
                else:
                    status_item.setToolTip(f"🔴 عامل مدين\n💰 المبلغ المستحق: {abs(balance):,.0f}")

                self.employees_table.setItem(row, 8, status_item)

                # 10. التاريخ
                date_text = "2024-01-01"
                date_item = QTableWidgetItem(date_text)
                date_item.setForeground(QColor("#4a5568"))  # رمادي للتاريخ الموجود
                date_item.setBackground(QColor("#f7fafc"))  # خلفية رمادية فاتحة
                date_item.setToolTip(f"📅 تاريخ التوظيف: {date_text}")
                date_item.setTextAlignment(Qt.AlignCenter)
                date_item.setFont(unified_font)  # خط موحد مثل العملاء
                self.employees_table.setItem(row, 9, date_item)

            print(f"✅ تم إنشاء {len(test_employees)} عامل تجريبي مع تفاصيل متطورة")

            # تطبيق التحسينات فوراً على البيانات التجريبية مطابق للعملاء
            print("🎨 تطبيق التحسينات على البيانات التجريبية...")

            # تطبيق الأعراض الصحيحة أولاً
            self.employees_table.setColumnWidth(0, 120)  # الرقم - مطابق للعملاء
            self.employees_table.setColumnWidth(1, 150)  # الاسم - مطابق للعملاء
            self.employees_table.setColumnWidth(2, 220)  # الهاتف - مطابق للعملاء
            self.employees_table.setColumnWidth(3, 180)  # الإيميل - مطابق للعملاء
            self.employees_table.setColumnWidth(4, 150)  # العنوان - مطابق للعملاء
            self.employees_table.setColumnWidth(5, 200)  # الرصيد - مطابق للعملاء
            self.employees_table.setColumnWidth(6, 155)  # المنصب - مطابق لعمود الرصيد في الموردين
            self.employees_table.setColumnWidth(7, 105)  # الملاحظات - مطابق للعملاء
            self.employees_table.setColumnWidth(8, 120)  # الحالة - مطابق للعملاء
            self.employees_table.setColumnWidth(9, 154)  # التاريخ - مطابق للعملاء

            # تطبيق ارتفاع الصفوف
            for row in range(self.employees_table.rowCount()):
                self.employees_table.setRowHeight(row, 40)

            # تطبيق التحسينات المتقدمة مثل العملاء
            self.add_visual_enhancements()
            self.highlight_important_data()
            self.add_status_indicators()

            # تطبيق ألوان الرصيد في النهاية لضمان عدم الكتابة عليها
            self.apply_employees_balance_colors()

            print("✅ تم تطبيق جميع التحسينات على البيانات التجريبية بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في إنشاء البيانات التجريبية: {str(e)}")

    def smart_update_table_data(self, employees):
        """تحديث ذكي للبيانات مع الحفاظ الكامل على التنسيق مطابق للعملاء"""
        try:
            print("🔄 تحديث ذكي للبيانات مع الحفاظ الكامل على التنسيق...")

            # تحديث عدد الصفوف إذا لزم الأمر
            current_rows = self.employees_table.rowCount()
            needed_rows = len(employees)

            if current_rows != needed_rows:
                self.employees_table.setRowCount(needed_rows)

            # تحديث البيانات في الخلايا الموجودة بدون إعادة إنشاء
            for row, employee in enumerate(employees):
                # البيانات الأساسية مع أيقونات حسب حالة العامل
                # اختيار الأيقونة حسب الرصيد والحالة
                if hasattr(employee, 'balance') and employee.balance > 10000:
                    icon = "💎"  # VIP - مبلغ كبير جداً
                    status = "VIP"
                elif hasattr(employee, 'balance') and employee.balance > 1000:
                    icon = "⭐"  # ممتاز - مبلغ كبير
                    status = "ممتاز"
                elif hasattr(employee, 'balance') and employee.balance > 0:
                    icon = "🟢"  # جيد - مبلغ موجب
                    status = "جيد"
                elif hasattr(employee, 'balance') and employee.balance == 0:
                    icon = "⚫"  # عادي - رصيد صفر
                    status = "عادي"
                else:
                    icon = "🔴"  # مدين - رصيد سالب
                    status = "مدين"

                # تحديث البيانات مع الحفاظ على التنسيق
                columns_data = [
                    f"{icon} {employee.id}",
                    employee.name,
                    employee.phone or "غير متوفر",
                    employee.email or "غير متوفر",
                    employee.address or "غير متوفر",
                    f"{getattr(employee, 'balance', 0):,.0f}",
                    employee.position or "غير محدد",
                    employee.notes or "غير متوفر",
                    f"{icon} {status}",
                    employee.hire_date.strftime("%Y-%m-%d") if hasattr(employee, 'hire_date') and employee.hire_date else "2024-01-01"
                ]

                for col, data in enumerate(columns_data):
                    item = self.employees_table.item(row, col)
                    if item:
                        item.setText(str(data))
                    else:
                        new_item = QTableWidgetItem(str(data))
                        new_item.setTextAlignment(Qt.AlignCenter)
                        new_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                        self.employees_table.setItem(row, col, new_item)

            # تطبيق التحسينات بعد التحديث
            # self.apply_simple_enhancements()  # محذوف
            self.apply_employees_balance_colors()

            print("✅ تم التحديث الذكي للبيانات بنجاح")

        except Exception as e:
            print(f"❌ خطأ في التحديث الذكي: {str(e)}")

    def add_visual_enhancements(self):
        try:
            print("🎨 إضافة التحسينات المرئية...")

            # تحسين ألوان الجدول مع ضمان الوضوح
            enhanced_style = """
                QTableWidget {
                    background-color: #ffffff;
                    alternate-background-color: #f8f9fa;
                    selection-background-color: #007bff;
                    selection-color: white;
                    gridline-color: #dee2e6;
                    border: 3px solid #007bff;
                    border-radius: 10px;
                    font-family: 'Segoe UI', Arial, sans-serif;
                    font-size: 11px;
                }
                QTableWidget::item {
                    padding: 10px;
                    border-bottom: 1px solid #dee2e6;
                    font-weight: bold;
                }
                QTableWidget::item:selected {
                    background-color: #007bff;
                    color: white;
                }
                QHeaderView::section {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                    color: white;
                    border: 2px solid #5a67d8;
                    padding: 8px;
                    font-weight: bold;
                    font-size: 12px;
                }

            self.employees_table.setStyleSheet(enhanced_style)
            self.employees_table.setAlternatingRowColors(True)

            print("✅ تم تطبيق التحسينات المرئية بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في إضافة التحسينات المرئية: {str(e)}")

    def highlight_important_data(self):
        """تمييز البيانات المهمة بألوان مختلفة مطابق للعملاء"""
        try:
            print("🌈 تمييز البيانات المهمة...")

            for row in range(self.employees_table.rowCount()):
                # الحصول على الرصيد
                balance_item = self.employees_table.item(row, 5)  # عمود الرصيد
                if balance_item and balance_item.text():
                    try:
                        balance_text = balance_item.text().replace(',', '').replace(' ', '')
                        balance = float(balance_text)

                        # تحديد اللون حسب الرصيد
                        row_color = None
                        text_color = QColor(0, 0, 0)  # أسود افتراضي

                        if balance > 10000:
                            # عامل عالي القيمة - أخضر فاتح
                            row_color = QColor(220, 255, 220)
                            text_color = QColor(0, 100, 0)
                        elif balance > 1000:
                            # عامل جيد - أخضر فاتح أقل
                            row_color = QColor(240, 255, 240)
                            text_color = QColor(0, 120, 0)
                        elif balance < -1000:
                            # عامل مدين - أحمر فاتح
                            row_color = QColor(255, 220, 220)
                            text_color = QColor(150, 0, 0)
                        elif balance < 0:
                            # عامل مدين قليل - أحمر فاتح أقل
                            row_color = QColor(255, 240, 240)
                            text_color = QColor(120, 0, 0)

                        # تطبيق الألوان على الصف
                        if row_color:
                            for col in range(self.employees_table.columnCount()):
                                item = self.employees_table.item(row, col)
                                if item:
                                    item.setBackground(row_color)
                                    item.setForeground(text_color)

                    except (ValueError, TypeError):
                        continue

            print("✅ تم تمييز البيانات المهمة بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في تمييز البيانات المهمة: {str(e)}")

    def add_status_indicators(self):
        try:
            print("🚦 إضافة مؤشرات الحالة...")

            for row in range(self.employees_table.rowCount()):
                # إضافة مؤشر الحالة في العمود الأول
                status_item = self.employees_table.item(row, 0)
                balance_item = self.employees_table.item(row, 5)

                if status_item and balance_item and balance_item.text():
                    try:
                        # استخراج الرقم من النص (إزالة الرموز التعبيرية إذا وجدت)
                        employee_id_text = status_item.text()
                        employee_id_match = re.search(r'\d+', employee_id_text)
                        if not employee_id_match:
                            continue
                        employee_id = int(employee_id_match.group())

                        # الحصول على الرصيد
                        balance_text = balance_item.text().replace(',', '').replace(' ', '')
                        balance = float(balance_text)

                        # تحديد المؤشر والحالة
                        if balance > 10000:
                            indicator = "💎"
                            status_text = "VIP"
                            color = QColor(255, 215, 0)  # ذهبي
                        elif balance > 1000:
                            indicator = "⭐"
                            status_text = "ممتاز"
                            color = QColor(0, 255, 0)  # أخضر
                        elif balance > 0:
                            indicator = "🟢"
                            status_text = "جيد"
                            color = QColor(144, 238, 144)  # أخضر فاتح
                        elif balance == 0:
                            indicator = "⚫"
                            status_text = "عادي"
                            color = QColor(192, 192, 192)  # رمادي
                        else:
                            indicator = "🔴"
                            status_text = "مدين"
                            color = QColor(255, 182, 193)  # أحمر فاتح

                        # تحديث النص مع المؤشر
                        status_item.setText(f"{indicator} {employee_id}")
                        status_item.setBackground(color)
                        status_item.setToolTip(f"الحالة: {status_text}\nالرصيد: {balance:,.0f}")

                    except (ValueError, TypeError):
                        continue

            print("✅ تم إضافة مؤشرات الحالة بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في إضافة مؤشرات الحالة: {str(e)}")

    def force_apply_enhancements(self):
        """فرض تطبيق التحسينات المرئية مطابق للعملاء"""
        try:
            print("🔧 فرض تطبيق التحسينات المرئية...")

            # تطبيق التحسينات
            if self.employees_table.rowCount() > 0:
                self.add_visual_enhancements()
                self.highlight_important_data()
                self.add_status_indicators()
                # self.apply_simple_enhancements()  # محذوف
                self.apply_employees_balance_colors()
                print("✅ تم فرض تطبيق التحسينات بنجاح!")
            else:
                print("⚠️ لا توجد بيانات لتطبيق التحسينات")

        except Exception as e:
            print(f"❌ خطأ في فرض تطبيق التحسينات: {str(e)}")

    def create_sample_employees_data(self):
        print("🧪 إنشاء بيانات تجريبية للعمال مع التطورات المتقدمة...")

        # مسح الجدول
        self.employees_table.setRowCount(0)

        # بيانات تجريبية متطورة مع إضافة عمود الرصيد
        sample_employees = [
            {
                "id": "1 🆔",
                "name": "أحمد محمد علي",
                "phone": "+966 50 123 4567",
                "email": "<EMAIL>",
                "address": "الرياض، حي النخيل",
                "balance": "15,000",  # رصيد موجب
                "position": "مهندس برمجيات",
                "notes": "خبرة 5 سنوات في التطوير",
                "status": "🟢 نشط",
                "date": "2023-01-15"
            },
            {
                "id": "2 🆔",
                "name": "فاطمة أحمد السالم",
                "phone": "+966 55 987 6543",
                "email": "<EMAIL>",
                "address": "جدة، حي الزهراء",
                "balance": "-2,500",  # رصيد سالب
                "position": "مديرة مشاريع",
                "notes": "قائدة فريق ممتازة",
                "status": "🟢 نشط",
                "date": "2022-08-20"
            },
            {
                "id": "3 🆔",
                "name": "محمد عبدالله الغامدي",
                "phone": "+966 56 456 7890",
                "email": "<EMAIL>",
                "address": "الدمام، حي الشاطئ",
                "balance": "8,750",  # رصيد موجب
                "position": "محاسب مالي",
                "notes": "دقيق في العمل",
                "status": "🆕 جديد",
                "date": "2024-01-10"
            },
            {
                "id": "4 🆔",
                "name": "نورا سعد الحربي",
                "phone": "📵 غير متوفر",
                "email": "غير متوفر",
                "address": "مكة، حي العزيزية",
                "balance": "0",  # رصيد متوازن
                "position": "مصممة جرافيك",
                "notes": "مبدعة في التصميم",
                "status": "⚫ خبير",
                "date": "2020-03-05"
            },
            {
                "id": "5 🆔",
                "name": "خالد يوسف المطيري",
                "phone": "+966 50 789 0123",
                "email": "<EMAIL>",
                "address": "الطائف، حي الملك فهد",
                "balance": "-1,200",  # رصيد سالب
                "position": "مطور تطبيقات",
                "notes": "متخصص في تطبيقات الجوال",
                "status": "🟢 نشط",
                "date": "2023-06-12"
            }
        ]

        # إعداد الخطوط الموحدة
        unified_font = QFont("Segoe UI", 11, QFont.Bold)
        unified_font.setLetterSpacing(QFont.AbsoluteSpacing, 0.5)
        unified_font.setStyleHint(QFont.SansSerif)
        unified_font.setWeight(QFont.Bold)

        # ملء الجدول بالبيانات التجريبية
        for row, emp_data in enumerate(sample_employees):
            self.employees_table.insertRow(row)

            # الأعمدة بالترتيب: ID, الاسم, الهاتف, الإيميل, العنوان, الرصيد, المنصب, الملاحظات, الحالة, التاريخ
            columns_data = [
                emp_data["id"],
                emp_data["name"],
                emp_data["phone"],
                emp_data["email"],
                emp_data["address"],
                emp_data["balance"],
                emp_data["position"],
                emp_data["notes"],
                emp_data["status"],
                emp_data["date"]
            ]

            for col, data in enumerate(columns_data):
                item = QTableWidgetItem(str(data))
                item.setTextAlignment(Qt.AlignCenter)
                item.setFont(unified_font)

                # تلوين البيانات حسب النوع
                if col == 2 and "غير متوفر" in str(data):  # الهاتف
                    item.setForeground(QColor("#DC2626"))  # أحمر
                    item.setBackground(QColor("#FEE2E2"))  # خلفية حمراء فاتحة
                elif col == 3 and "غير متوفر" in str(data):  # الإيميل
                    item.setForeground(QColor("#DC2626"))  # أحمر
                    item.setBackground(QColor("#FEE2E2"))  # خلفية حمراء فاتحة
                elif col == 5:  # الرصيد - تطبيق ألوان مثل العملاء
                    balance_str = str(data).replace(",", "")
                    try:
                        balance_value = float(balance_str)
                        if balance_value > 0:
                            item.setForeground(QColor("#059669"))  # أخضر للموجب
                            item.setBackground(QColor("#d1fae5"))  # خلفية خضراء فاتحة
                        elif balance_value < 0:
                            item.setForeground(QColor("#dc2626"))  # أحمر للسالب
                            item.setBackground(QColor("#fee2e2"))  # خلفية حمراء فاتحة
                        else:
                            item.setForeground(QColor("#6b7280"))  # رمادي للصفر
                            item.setBackground(QColor("#f3f4f6"))  # خلفية رمادية فاتحة
                    except:
                        item.setForeground(QColor("#000000"))  # أسود عادي
                        item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة
                elif col == 8:  # الحالة
                    if "🟢 نشط" in str(data):
                        item.setForeground(QColor("#38a169"))  # أخضر
                        item.setBackground(QColor("#c6f6d5"))  # خلفية خضراء فاتحة
                    elif "🆕 جديد" in str(data):
                        item.setForeground(QColor("#17a2b8"))  # أزرق
                        item.setBackground(QColor("#d1ecf1"))  # خلفية زرقاء فاتحة
                    elif "⚫ خبير" in str(data):
                        item.setForeground(QColor("#000000"))  # أسود
                        item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة
                else:
                    # باقي البيانات بلون أسود عريض
                    item.setForeground(QColor("#000000"))  # أسود عريض
                    item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة

                self.employees_table.setItem(row, col, item)

        # تطبيق الأعراض الصحيحة
        self.apply_employees_correct_widths()

        # تطبيق التحسينات المرئية المتقدمة على البيانات التجريبية
        self.apply_employees_advanced_visual_enhancements()

        # تطبيق ألوان الرصيد مثل العملاء
        self.apply_employees_balance_colors()

        # تطبيق ألوان الحالة المتطورة
        self.apply_employees_status_colors()

        # تطبيق التحسينات النهائية المتقدمة
        # self.apply_employees_final_advanced_enhancements()  # محذوف

        # فرض تطبيق جميع التحديثات المرئية
        self.force_employees_visual_updates()

        # تطبيق التحسينات المتقدمة الجديدة مثل العملاء
        self.add_visual_enhancements()
        self.highlight_important_data()
        self.add_status_indicators()
        self.force_apply_enhancements()

        print(f"✅ تم إنشاء {len(sample_employees)} عامل تجريبي مع التحسينات المتقدمة مطابقة للعملاء")

    def filter_employees(self):
        """تصفية العمال بناءً على نص البحث المتطور - موحد مع العملاء والموردين"""
        search_text = self.search_edit.text().strip().lower()

        if not search_text:
            print("🔄 مسح البحث - العودة للبيانات الكاملة...")
            self.refresh_data()
            return

        print(f"🔍 البحث المتطور عن: {search_text}")

        try:
            # البحث المتطور في جميع الحقول مع دعم البحث الذكي

            # تقسيم النص للبحث بكلمات متعددة
            search_terms = search_text.split()

            # إنشاء شروط البحث المتقدمة
            search_conditions = []

            for term in search_terms:
                term_conditions = [
                    Employee.name.like(f"%{term}%"),
                    Employee.phone.like(f"%{term}%"),
                    Employee.email.like(f"%{term}%"),
                    Employee.address.like(f"%{term}%"),
                    Employee.position.like(f"%{term}%"),
                    Employee.notes.like(f"%{term}%")
                ]
                search_conditions.append(or_(*term_conditions))

            # البحث بجميع الكلمات (AND) أو أي كلمة (OR) حسب عدد الكلمات
            if len(search_terms) > 1:
                # إذا كان هناك أكثر من كلمة، ابحث عن النتائج التي تحتوي على أي من الكلمات
                final_condition = or_(*search_conditions)
            else:
                # إذا كانت كلمة واحدة، ابحث عنها في جميع الحقول
                final_condition = search_conditions[0] if search_conditions else None

            if final_condition is not None:
                employees = self.session.query(Employee).filter(final_condition).all()
            else:
                employees = []

            print(f"📊 تم العثور على {len(employees)} عامل من أصل {self.session.query(Employee).count()}")

            # إضافة إحصائيات البحث المتقدمة
            if employees:
                # تصنيف النتائج حسب الصلة
                exact_matches = []
                partial_matches = []

                for emp in employees:
                    # البحث عن التطابق التام في الاسم
                    if search_text in emp.name.lower():
                        exact_matches.append(emp)
                    else:
                        partial_matches.append(emp)

                # ترتيب النتائج: التطابق التام أولاً
                sorted_employees = exact_matches + partial_matches
                self.populate_table(sorted_employees)

                # عرض إحصائيات مفصلة
                stats_msg = f"🎯 نتائج البحث:\n"
                stats_msg += f"📍 تطابق تام: {len(exact_matches)}\n"
                stats_msg += f"📝 تطابق جزئي: {len(partial_matches)}\n"
                stats_msg += f"📊 إجمالي النتائج: {len(employees)}"
                print(stats_msg)

            else:
                self.populate_table([])
                print("❌ لم يتم العثور على نتائج مطابقة")

            # تطبيق التحسينات المرئية بعد التصفية مطابق للعملاء
            # self.apply_simple_enhancements()  # محذوف
            self.apply_employees_balance_colors()
            self.apply_employees_advanced_visual_enhancements()
            self.apply_employees_status_colors()

            # تطبيق التحسينات المتقدمة الجديدة مثل العملاء
            self.add_visual_enhancements()
            self.highlight_important_data()
            self.add_status_indicators()
            self.force_apply_enhancements()

            # تمييز النتائج في الجدول
            self.highlight_search_results(search_text)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء البحث المتطور: {str(e)}")

    def highlight_search_results(self, search_text):
        try:
            if not search_text:
                return

            search_terms = search_text.lower().split()

            for row in range(self.employees_table.rowCount()):
                for col in range(self.employees_table.columnCount()):
                    item = self.employees_table.item(row, col)
                    if item:
                        item_text = item.text().lower()

                        # التحقق من وجود أي من كلمات البحث
                        found_match = any(term in item_text for term in search_terms)

                        if found_match:
                            # تمييز الخلية التي تحتوي على نتيجة البحث
                            current_bg = item.background()
                            item.setBackground(QColor("#fff3cd"))  # خلفية صفراء فاتحة
                            item.setToolTip(f"🔍 نتيجة بحث: {search_text}")

        except Exception as e:
            print(f"❌ خطأ في تمييز نتائج البحث: {str(e)}")

    def add_smart_search_suggestions(self):
        """إضافة اقتراحات البحث الذكية"""
        try:
            # الحصول على أكثر الكلمات بحثاً
            common_names = self.session.query(Employee.name).limit(10).all()
            common_positions = self.session.query(Employee.position).filter(
                Employee.position.isnot(None)
            ).distinct().limit(5).all()

            suggestions = []
            suggestions.extend([name[0] for name in common_names if name[0]])
            suggestions.extend([pos[0] for pos in common_positions if pos[0]])

            # يمكن إضافة هذه الاقتراحات لحقل البحث لاحقاً
            return suggestions[:10]  # أفضل 10 اقتراحات

        except Exception as e:
            print(f"❌ خطأ في إنشاء اقتراحات البحث: {str(e)}")
            return []

    def filter_by_status(self, status_type='all'):
        try:

            if status_type == 'active':
                # العمال النشطين (لديهم أجور في آخر 30 يوم)
                thirty_days_ago = datetime.now() - timedelta(days=30)
                active_employee_ids = self.session.query(DailyWage.employee_id).filter(
                    DailyWage.wage_date >= thirty_days_ago
                ).distinct().all()
                active_ids = [emp_id[0] for emp_id in active_employee_ids]
                employees = self.session.query(Employee).filter(Employee.id.in_(active_ids)).all()
            elif status_type == 'new':
                # العمال الجدد (تم توظيفهم في آخر 30 يوم)
                thirty_days_ago = datetime.now() - timedelta(days=30)
                employees = self.session.query(Employee).filter(
                    Employee.hire_date >= thirty_days_ago
                ).all()
            else:
                # جميع العمال
                employees = self.session.query(Employee).all()

            self.populate_table(employees)
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء التصفية: {str(e)}")

    def filter_by_position(self):
        """تصفية العمال حسب المنصب"""
        try:

            # الحصول على جميع المناصب المتاحة
            positions = self.session.query(Employee.position).filter(
                Employee.position.isnot(None),
                Employee.position != ''
            ).distinct().all()

            position_list = [pos[0] for pos in positions if pos[0]]

            if not position_list:
                show_info_message("معلومات", "لا توجد مناصب محددة للعمال")
                return

            # عرض قائمة المناصب للاختيار
            position, ok = QInputDialog.getItem(
                self,
                "اختيار المنصب",
                "اختر المنصب للتصفية:",
                position_list,
                0,
                False
            )

            if ok and position:
                employees = self.session.query(Employee).filter(
                    Employee.position == position
                ).all()
                self.populate_table(employees)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء التصفية حسب المنصب: {str(e)}")

    def filter_by_balance(self, balance_type='all'):
        print(f"🔍 تصفية العمال المتطورة حسب الرصيد: {balance_type}")

        try:

            if balance_type == 'positive':
                # العمال الذين لهم مبلغ (رصيد موجب)
                employees = self.session.query(Employee).filter(Employee.balance > 0).order_by(Employee.balance.desc()).all()
                filter_desc = "العمال الذين لهم مبلغ (رصيد موجب)"
            elif balance_type == 'negative':
                # العمال الذين عليهم مبلغ (رصيد سالب)
                employees = self.session.query(Employee).filter(Employee.balance < 0).order_by(Employee.balance.asc()).all()
                filter_desc = "العمال الذين عليهم مبلغ (رصيد سالب)"
            elif balance_type == 'zero':
                # العمال بدون رصيد (رصيد صفر)
                employees = self.session.query(Employee).filter(Employee.balance == 0).all()
                filter_desc = "العمال بدون رصيد (رصيد صفر)"
            elif balance_type == 'high':
                # العمال ذوو الرصيد العالي (أكثر من 1000)
                employees = self.session.query(Employee).filter(Employee.balance > 1000).order_by(Employee.balance.desc()).all()
                filter_desc = "العمال ذوو الرصيد العالي (أكثر من 1000)"
            elif balance_type == 'low':
                # العمال ذوو الرصيد المنخفض (أقل من -500)
                employees = self.session.query(Employee).filter(Employee.balance < -500).order_by(Employee.balance.asc()).all()
                filter_desc = "العمال ذوو الرصيد المنخفض (أقل من -500)"
            else:
                # جميع العمال مرتبين حسب الرصيد
                employees = self.session.query(Employee).order_by(Employee.balance.desc()).all()
                filter_desc = "جميع العمال مرتبين حسب الرصيد"

            # حساب إحصائيات مفصلة
            total_employees = self.session.query(Employee).count()
            total_balance = sum(emp.balance for emp in employees)
            avg_balance = total_balance / len(employees) if employees else 0

            print(f"📊 تم العثور على {len(employees)} عامل من أصل {total_employees}")
            print(f"💰 إجمالي الرصيد: {total_balance:,.2f}")
            print(f"📈 متوسط الرصيد: {avg_balance:,.2f}")

            self.populate_table(employees)

            # عرض معلومات التصفية في شريط الحالة
            if hasattr(self, 'status_label'):
                status_text = f"🔍 {filter_desc} | 📊 {len(employees)} عامل | 💰 إجمالي: {total_balance:,.0f}"
                self.status_label.setText(status_text)

            # تطبيق التحسينات المرئية مع تمييز خاص للرصيد
            # self.apply_simple_enhancements()  # محذوف
            self.apply_employees_balance_colors()
            self.apply_employees_advanced_visual_enhancements()
            self.apply_employees_status_colors()

            # تطبيق التحسينات المتقدمة
            self.add_visual_enhancements()
            self.highlight_important_data()
            self.add_status_indicators()
            self.force_apply_enhancements()

            # تمييز خاص للرصيد المفلتر
            self.highlight_filtered_balance(balance_type)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء التصفية المتطورة: {str(e)}")

    def highlight_filtered_balance(self, balance_type):
        """تمييز الرصيد المفلتر في الجدول"""
        try:
            balance_col = 5  # عمود الرصيد

            for row in range(self.employees_table.rowCount()):
                balance_item = self.employees_table.item(row, balance_col)
                if balance_item:
                    try:
                        balance_text = balance_item.text().replace(',', '').replace('ر.س', '').strip()
                        balance = float(balance_text) if balance_text else 0.0

                        # تطبيق تمييز خاص حسب نوع التصفية
                        if balance_type == 'positive' and balance > 0:
                            balance_item.setBackground(QColor("#d4edda"))  # أخضر فاتح
                            balance_item.setToolTip(f"💰 رصيد موجب: {balance:,.0f}")
                        elif balance_type == 'negative' and balance < 0:
                            balance_item.setBackground(QColor("#f8d7da"))  # أحمر فاتح
                            balance_item.setToolTip(f"⚠️ رصيد سالب: {balance:,.0f}")
                        elif balance_type == 'zero' and balance == 0:
                            balance_item.setBackground(QColor("#e2e3e5"))  # رمادي فاتح
                            balance_item.setToolTip("⚪ رصيد صفر")
                        elif balance_type == 'high' and balance > 1000:
                            balance_item.setBackground(QColor("#fff3cd"))  # ذهبي فاتح
                            balance_item.setToolTip(f"🌟 رصيد عالي: {balance:,.0f}")
                        elif balance_type == 'low' and balance < -500:
                            balance_item.setBackground(QColor("#f5c6cb"))  # أحمر داكن فاتح
                            balance_item.setToolTip(f"🔻 رصيد منخفض: {balance:,.0f}")

                    except ValueError:
                        pass

        except Exception as e:
            print(f"❌ خطأ في تمييز الرصيد المفلتر: {str(e)}")

    def add_quick_filters(self):
        try:
            # يمكن إضافة أزرار فلترة سريعة في المستقبل
            quick_filters = {
                'today_hired': 'موظفين اليوم',
                'this_week_hired': 'موظفين هذا الأسبوع',
                'this_month_hired': 'موظفين هذا الشهر',
                'active_workers': 'عمال نشطين',
                'inactive_workers': 'عمال غير نشطين',
                'high_performers': 'عمال متميزين',
                'need_attention': 'يحتاجون متابعة'
            }
            return quick_filters

        except Exception as e:
            print(f"❌ خطأ في إنشاء الفلاتر السريعة: {str(e)}")
            return {}

    def show_columns_dialog(self):
        """عرض نافذة إدارة الأعمدة المتطورة والمحسنة - مطابقة للعملاء والموردين"""
        try:
                                       QLabel, QFrame, QHBoxLayout, QPushButton, QScrollArea, QWidget,
                                       QGraphicsDropShadowEffect)

            dialog = QDialog(self)
            dialog.setWindowTitle("🔧 إدارة الأعمدة المتطورة والذكية")
            dialog.setFixedSize(500, 650)
            dialog.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint)

            # تطبيق النمط الموحد المتطور للحوار مع تأثيرات متقدمة
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #667eea, stop:0.2 #764ba2, stop:0.4 #f093fb,
                        stop:0.6 #4facfe, stop:0.8 #00f2fe, stop:1 #43e97b);
                    border: 4px solid qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #3b82f6, stop:0.5 #8b5cf6, stop:1 #06b6d4);
                    border-radius: 0px;
                    font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                }
            """)

            # إضافة تأثير الظل للنافذة
            shadow_effect = QGraphicsDropShadowEffect()
            shadow_effect.setBlurRadius(30)
            shadow_effect.setColor(QColor(0, 0, 0, 100))
            shadow_effect.setOffset(0, 10)
            dialog.setGraphicsEffect(shadow_effect)

            layout = QVBoxLayout()
            layout.setSpacing(20)
            layout.setContentsMargins(25, 25, 25, 25)

            # شريط العنوان المتطور
            title_bar = QFrame()
            title_bar.setFixedHeight(60)
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #1e3a8a, stop:0.3 #3b82f6, stop:0.7 #8b5cf6, stop:1 #ec4899);
                    border: none;
                    border-radius: 0px;
                    margin: 0px;
                    padding: 0px;
                }
            """)
            title_bar_layout = QHBoxLayout(title_bar)
            title_bar_layout.setContentsMargins(15, 0, 15, 0)
            title_bar_layout.setSpacing(15)

            # أزرار التحكم
            left_section = QFrame()
            left_section.setStyleSheet("QFrame { background: transparent; }")
            left_layout = QHBoxLayout(left_section)
            left_layout.setContentsMargins(0, 0, 0, 0)
            left_layout.setSpacing(8)

            # زر إغلاق
            close_btn = QPushButton("✕")
            close_btn.setFixedSize(30, 30)
                QPushButton {
                    background: rgba(239, 68, 68, 0.8);
                    color: white;
                    border: none;
                    border-radius: 0px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: rgba(220, 38, 38, 0.9);
                    transform: scale(1.1);
                }
            """)
            close_btn.clicked.connect(dialog.reject)

            # زر تصغير
            minimize_btn = QPushButton("🗕")
            minimize_btn.setFixedSize(30, 30)
                QPushButton {
                    background: rgba(255, 255, 255, 0.2);
                    color: white;
                    border: none;
                    border-radius: 0px;
                    font-size: 12px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: rgba(255, 255, 255, 0.3);
                    transform: scale(1.1);
                }
            """)
            minimize_btn.clicked.connect(dialog.showMinimized)

            left_layout.addWidget(close_btn)
            left_layout.addWidget(minimize_btn)
            left_layout.addStretch()

            # عنوان النافذة
            center_section = QFrame()
            center_section.setStyleSheet("QFrame { background: transparent; }")
            center_layout = QHBoxLayout(center_section)
            center_layout.setContentsMargins(0, 0, 0, 0)

            title_icon = QLabel("🔧")
                QLabel {
                    font-size: 24px;
                    color: white;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 12px;
                    padding: 8px;
                    min-width: 40px;
                    max-width: 40px;
                    text-align: center;
                }
            """)
            title_icon.setAlignment(Qt.AlignCenter)

            title_text = QLabel("إدارة أعمدة العمال")
                QLabel {
                    color: white;
                    font-weight: bold;
                    font-size: 18px;
                    background: transparent;
                    margin-left: 10px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
                }
            """)

            center_layout.addWidget(title_icon)
            center_layout.addWidget(title_text)

            # الجانب الأيمن
            right_section = QFrame()
            right_section.setStyleSheet("QFrame { background: transparent; }")
            right_layout = QHBoxLayout(right_section)
            right_layout.setContentsMargins(0, 0, 0, 0)
            right_layout.addStretch()

            title_bar_layout.addWidget(left_section, 1)
            title_bar_layout.addWidget(center_section, 0, Qt.AlignCenter)
            title_bar_layout.addWidget(right_section, 1)
            layout.addWidget(title_bar)

            # إضافة وظيفة السحب للنافذة
            def mousePressEvent(event):
                if event.button() == Qt.LeftButton:
                    dialog.drag_start_position = event.globalPos() - dialog.frameGeometry().topLeft()
                    dialog.dragging = True
                    dialog.setCursor(Qt.ClosedHandCursor)
                    event.accept()

            def mouseMoveEvent(event):
                if event.buttons() == Qt.LeftButton and hasattr(dialog, 'drag_start_position') and dialog.dragging:
                    new_position = event.globalPos() - dialog.drag_start_position
                    screen = QApplication.desktop().screenGeometry()
                    dialog_size = dialog.frameGeometry()

                    min_x = 0
                    min_y = 0
                    max_x = screen.width() - dialog_size.width()
                    max_y = screen.height() - dialog_size.height()

                    x = max(min_x, min(new_position.x(), max_x))
                    y = max(min_y, min(new_position.y(), max_y))

                    dialog.move(x, y)
                    event.accept()

            def mouseReleaseEvent(event):
                if event.button() == Qt.LeftButton:
                    dialog.dragging = False
                    dialog.setCursor(Qt.ArrowCursor)
                    event.accept()

            title_bar.mousePressEvent = mousePressEvent
            title_bar.mouseMoveEvent = mouseMoveEvent
            title_bar.mouseReleaseEvent = mouseReleaseEvent
            dialog.mousePressEvent = mousePressEvent
            dialog.mouseMoveEvent = mouseMoveEvent
            dialog.mouseReleaseEvent = mouseReleaseEvent
            dialog.dragging = False

            # شريط الإحصائيات
            stats_frame = QFrame()
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.1),
                        stop:1 rgba(139, 92, 246, 0.1));
                    border: 2px solid rgba(59, 130, 246, 0.3);
                    border-radius: 15px;
                    padding: 10px;
                    margin-bottom: 15px;
                }
            """)
            stats_layout = QHBoxLayout(stats_frame)

            total_columns = 10  # عدد أعمدة جدول العمال
            stats_label = QLabel(f"📊 إجمالي الأعمدة: {total_columns} | 👁️ مرئية: {total_columns} | 🔒 مخفية: 0")
                QLabel {
                    color: #374151;
                    font-size: 14px;
                    font-weight: 600;
                    background: transparent;
                }
            """)
            stats_label.setAlignment(Qt.AlignCenter)
            stats_layout.addWidget(stats_label)
            layout.addWidget(stats_frame)

            # وصف تفصيلي
            desc_label = QLabel("✨ اختر الأعمدة التي تريد عرضها في جدول العمال")
                QLabel {
                    color: #475569;
                    font-size: 14px;
                    font-weight: 500;
                    padding: 10px;
                    background: rgba(255, 255, 255, 0.7);
                    border-radius: 10px;
                    border: 2px solid rgba(59, 130, 246, 0.2);
                }
            """)
            desc_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(desc_label)

            # منطقة التمرير للأعمدة
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
                QScrollArea {
                    border: 2px solid #e2e8f0;
                    border-radius: 12px;
                    background: white;
                }
                QScrollBar:vertical {
                    background: #f1f5f9;
                    width: 12px;
                    border-radius: 6px;
                }
                QScrollBar::handle:vertical {
                    background: #3b82f6;
                    border-radius: 6px;
                    min-height: 20px;
                }
            """)

            scroll_widget = QWidget()
            scroll_layout = QVBoxLayout(scroll_widget)
            scroll_layout.setSpacing(8)
            scroll_layout.setContentsMargins(15, 15, 15, 15)

            # قائمة الأعمدة مع checkboxes متطورة للعمال
            self.column_checkboxes = {}
            column_names = [
                ("🆔", "رقم العامل", "#6366f1"),
                ("👤", "اسم العامل", "#10b981"),
                ("📱", "رقم الهاتف", "#f59e0b"),
                ("📧", "البريد الإلكتروني", "#ef4444"),
                ("📍", "العنوان", "#8b5cf6"),
                ("💰", "الرصيد المالي", "#06b6d4"),
                ("💼", "المنصب", "#84cc16"),
                ("📝", "الملاحظات", "#f97316"),
                ("🔄", "حالة العامل", "#ec4899"),
                ("📅", "تاريخ التوظيف", "#14b8a6")
            ]

            for i, (icon, name, color) in enumerate(column_names):
                checkbox_frame = QFrame()
                    QFrame {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 rgba(255, 255, 255, 0.9),
                            stop:1 {color}20);
                        border: 2px solid {color}40;
                        border-radius: 12px;
                        padding: 8px;
                        margin: 2px;
                    }}
                    QFrame:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 rgba(255, 255, 255, 0.95),
                            stop:1 {color}30);
                        border: 2px solid {color}60;
                        transform: scale(1.02);
                    }}
                """)

                checkbox_layout = QHBoxLayout(checkbox_frame)
                checkbox_layout.setContentsMargins(10, 5, 10, 5)

                checkbox = QCheckBox()
                is_column_checked = not self.employees_table.isColumnHidden(i)
                checkbox.setChecked(is_column_checked)

                if is_column_checked:
                    checkbox.setText(" ✓")
                else:
                    checkbox.setText("")

                    QCheckBox {{
                        font-size: 16px;
                        font-weight: bold;
                        color: #10b981;
                        spacing: 8px;
                    }}
                    QCheckBox::indicator {{
                        width: 24px;
                        height: 24px;
                        border: 2px solid {color};
                        border-radius: 6px;
                        background: white;
                    }}
                    QCheckBox::indicator:checked {{
                        background: {color};
                        border: 2px solid {color};
                    }}
                    QCheckBox::indicator:hover {{
                        border: 3px solid {color};
                        background: rgba(16, 185, 129, 0.1);
                    }}
                """)

                def update_check_mark(state, cb=checkbox):
                    if state == 2:
                        cb.setText(" ✓")
                    else:
                        cb.setText("")

                checkbox.stateChanged.connect(update_check_mark)

                icon_label = QLabel(icon)
                    QLabel {{
                        font-size: 18px;
                        color: {color};
                        font-weight: bold;
                        min-width: 25px;
                        text-align: center;
                    }}
                """)

                is_checked = not self.employees_table.isColumnHidden(i)
                check_mark = " ✅" if is_checked else ""

                name_label = QLabel(f"{name}{check_mark}")
                    QLabel {
                        font-size: 14px;
                        font-weight: 600;
                        color: #374151;
                    }
                """)

                def update_label_text(state, label=name_label, original_name=name):
                    if state == 2:
                        label.setText(f"{original_name} ✅")
                    else:
                        label.setText(original_name)

                checkbox.stateChanged.connect(update_label_text)

                checkbox_layout.addWidget(checkbox)
                checkbox_layout.addWidget(icon_label)
                checkbox_layout.addWidget(name_label)
                checkbox_layout.addStretch()

                self.column_checkboxes[i] = checkbox
                scroll_layout.addWidget(checkbox_frame)

            scroll_area.setWidget(scroll_widget)
            layout.addWidget(scroll_area)

            # أزرار الحوار المتطورة
            buttons_frame = QFrame()
                QFrame {
                    background: rgba(255, 255, 255, 0.8);
                    border-radius: 12px;
                    padding: 10px;
                }
            """)
            buttons_layout = QHBoxLayout(buttons_frame)
            buttons_layout.setSpacing(6)
            buttons_layout.setContentsMargins(8, 8, 8, 8)

            # أزرار التحكم
            select_all_btn = QPushButton("✅ تحديد الكل")
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #10b981, stop:1 #059669);
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 12px 18px;
                    font-size: 14px;
                    font-weight: 600;
                    text-align: center;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #059669, stop:1 #047857);
                    transform: translateY(-2px);
                }
            """)
            select_all_btn.clicked.connect(lambda: self.toggle_all_employee_columns(True))
            buttons_layout.addWidget(select_all_btn, 1)

            deselect_all_btn = QPushButton("❌ إلغاء الكل")
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #ef4444, stop:1 #dc2626);
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 12px 18px;
                    font-size: 14px;
                    font-weight: 600;
                    text-align: center;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #dc2626, stop:1 #b91c1c);
                    transform: translateY(-2px);
                }
            """)
            deselect_all_btn.clicked.connect(lambda: self.toggle_all_employee_columns(False))
            buttons_layout.addWidget(deselect_all_btn, 1)

            apply_btn = QPushButton("تطبيق التغييرات")
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #3b82f6, stop:1 #1d4ed8);
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 12px 18px;
                    font-size: 14px;
                    font-weight: 600;
                    text-align: center;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #1d4ed8, stop:1 #1e40af);
                    transform: translateY(-2px);
                }
            """)
            apply_btn.clicked.connect(lambda: self.apply_employee_column_settings(dialog))
            buttons_layout.addWidget(apply_btn, 2)

            layout.addWidget(buttons_frame)
            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إدارة الأعمدة: {str(e)}")

    def toggle_all_employee_columns(self, checked):
        for checkbox in self.column_checkboxes.values():
            checkbox.setChecked(checked)

    def apply_employee_column_settings(self, dialog):
        """تطبيق إعدادات أعمدة العمال"""
        try:
            for i, (col_key, checkbox) in enumerate(self.column_checkboxes.items()):
                self.employees_table.setColumnHidden(i, not checkbox.isChecked())

            show_info_message("تم", "تم تطبيق إعدادات الأعمدة بنجاح")
            dialog.accept()
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تطبيق الإعدادات: {str(e)}")

    def show_bulk_operations_dialog(self):
        try:
            selected_count = self.get_selected_employees_count()
            if selected_count == 0:
                show_error_message("تحذير", "يرجى تحديد عامل واحد أو أكثر أولاً\n\nاستخدم Ctrl+Click أو Shift+Click لتحديد عدة عمال")
                return

                                       QHBoxLayout, QFrame, QScrollArea, QWidget,
                                       QGraphicsDropShadowEffect, QComboBox, QSpinBox,
                                       QLineEdit, QTextEdit, QGroupBox, QGridLayout)

            dialog = QDialog(self)
            dialog.setWindowTitle("📋 العمليات المجمعة المتطورة")
            dialog.setFixedSize(600, 700)
            dialog.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint)

            # تطبيق النمط الموحد المتطور للحوار
            dialog.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #667eea, stop:0.2 #764ba2, stop:0.4 #f093fb,
                        stop:0.6 #4facfe, stop:0.8 #00f2fe, stop:1 #43e97b);
                    border: 4px solid qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #3b82f6, stop:0.5 #8b5cf6, stop:1 #06b6d4);
                    border-radius: 0px;
                    font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                }

            # إضافة تأثير الظل للنافذة
            shadow_effect = QGraphicsDropShadowEffect()
            shadow_effect.setBlurRadius(30)
            shadow_effect.setColor(QColor(0, 0, 0, 100))
            shadow_effect.setOffset(0, 10)
            dialog.setGraphicsEffect(shadow_effect)

            layout = QVBoxLayout()
            layout.setSpacing(20)
            layout.setContentsMargins(25, 25, 25, 25)

            # شريط العنوان المتطور
            title_bar = QFrame()
            title_bar.setFixedHeight(60)
            title_bar.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #1e3a8a, stop:0.3 #3b82f6, stop:0.7 #8b5cf6, stop:1 #ec4899);
                    border: none;
                    border-radius: 0px;
                    margin: 0px;
                    padding: 0px;
                }
            title_bar_layout = QHBoxLayout(title_bar)
            title_bar_layout.setContentsMargins(15, 0, 15, 0)
            title_bar_layout.setSpacing(15)

            # أزرار التحكم
            left_section = QFrame()
            left_section.setStyleSheet("QFrame { background: transparent; }")
            left_layout = QHBoxLayout(left_section)
            left_layout.setContentsMargins(0, 0, 0, 0)
            left_layout.setSpacing(8)

            # زر إغلاق
            close_btn = QPushButton("✕")
            close_btn.setFixedSize(30, 30)
            close_btn.setStyleSheet("""
                QPushButton {
                    background: rgba(239, 68, 68, 0.8);
                    color: white;
                    border: none;
                    border-radius: 0px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: rgba(220, 38, 38, 0.9);
                    transform: scale(1.1);
                }
            close_btn.clicked.connect(dialog.reject)

            left_layout.addWidget(close_btn)
            left_layout.addStretch()

            # عنوان النافذة
            center_section = QFrame()
            center_section.setStyleSheet("QFrame { background: transparent; }")
            center_layout = QHBoxLayout(center_section)
            center_layout.setContentsMargins(0, 0, 0, 0)

            title_icon = QLabel("📋")
            title_icon.setStyleSheet("""
                QLabel {
                    font-size: 24px;
                    color: white;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 12px;
                    padding: 8px;
                    min-width: 40px;
                    max-width: 40px;
                    text-align: center;
                }
            title_icon.setAlignment(Qt.AlignCenter)

            title_text = QLabel("العمليات المجمعة للعمال")
            title_text.setStyleSheet("""
                QLabel {
                    color: white;
                    font-weight: bold;
                    font-size: 18px;
                    background: transparent;
                    margin-left: 10px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
                }

            center_layout.addWidget(title_icon)
            center_layout.addWidget(title_text)

            # الجانب الأيمن
            right_section = QFrame()
            right_section.setStyleSheet("QFrame { background: transparent; }")
            right_layout = QHBoxLayout(right_section)
            right_layout.setContentsMargins(0, 0, 0, 0)
            right_layout.addStretch()

            title_bar_layout.addWidget(left_section, 1)
            title_bar_layout.addWidget(center_section, 0, Qt.AlignCenter)
            title_bar_layout.addWidget(right_section, 1)
            layout.addWidget(title_bar)

            # شريط الإحصائيات
            stats_frame = QFrame()
            stats_frame.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.1),
                        stop:1 rgba(139, 92, 246, 0.1));
                    border: 2px solid rgba(59, 130, 246, 0.3);
                    border-radius: 15px;
                    padding: 10px;
                    margin-bottom: 15px;
                }
            stats_layout = QHBoxLayout(stats_frame)

            stats_label = QLabel(f"👥 العمال المحددين: {selected_count} | 🔄 جاهز للعمليات المجمعة")
            stats_label.setStyleSheet("""
                QLabel {
                    color: #374151;
                    font-size: 14px;
                    font-weight: 600;
                    background: transparent;
                }
            stats_label.setAlignment(Qt.AlignCenter)
            stats_layout.addWidget(stats_label)
            layout.addWidget(stats_frame)

            # وصف تفصيلي
            desc_label = QLabel("✨ اختر العملية المطلوبة لتطبيقها على العمال المحددين")
            desc_label.setStyleSheet("""
                QLabel {
                    color: #475569;
                    font-size: 14px;
                    font-weight: 500;
                    padding: 10px;
                    background: rgba(255, 255, 255, 0.7);
                    border-radius: 10px;
                    border: 2px solid rgba(59, 130, 246, 0.2);
                }
            desc_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(desc_label)

            # منطقة التمرير للعمليات
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setStyleSheet("""
                QScrollArea {
                    border: 2px solid #e2e8f0;
                    border-radius: 12px;
                    background: white;
                }
                QScrollBar:vertical {
                    background: #f1f5f9;
                    width: 12px;
                    border-radius: 6px;
                }
                QScrollBar::handle:vertical {
                    background: #3b82f6;
                    border-radius: 6px;
                    min-height: 20px;
                }

            scroll_widget = QWidget()
            scroll_layout = QVBoxLayout(scroll_widget)
            scroll_layout.setSpacing(15)
            scroll_layout.setContentsMargins(15, 15, 15, 15)

            # مجموعة تحديث الحالة
            status_group = QGroupBox("🔄 تحديث حالة العمال")
            status_group.setStyleSheet("""
                QGroupBox {
                    font-size: 16px;
                    font-weight: bold;
                    color: #374151;
                    border: 2px solid #3b82f6;
                    border-radius: 10px;
                    margin-top: 10px;
                    padding-top: 10px;
                    background: rgba(59, 130, 246, 0.05);
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 10px 0 10px;
                    background: white;
                    border-radius: 5px;
                }
            status_layout = QGridLayout(status_group)

            status_label = QLabel("الحالة الجديدة:")
            self.bulk_status_combo = QComboBox()
            self.bulk_status_combo.addItems(["نشط", "غير نشط", "في إجازة", "مفصول"])
            self.bulk_status_combo.setStyleSheet("""
                QComboBox {
                    padding: 8px;
                    border: 2px solid #3b82f6;
                    border-radius: 6px;
                    background: white;
                    font-size: 14px;
                }

            update_status_btn = QPushButton("📝 تحديث الحالة")
            update_status_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #3b82f6, stop:1 #1d4ed8);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 15px;
                    font-size: 14px;
                    font-weight: 600;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #1d4ed8, stop:1 #1e40af);
                }
            update_status_btn.clicked.connect(lambda: self.bulk_update_employee_status(dialog))

            status_layout.addWidget(status_label, 0, 0)
            status_layout.addWidget(self.bulk_status_combo, 0, 1)
            status_layout.addWidget(update_status_btn, 0, 2)
            scroll_layout.addWidget(status_group)

            # مجموعة تحديث الرصيد
            balance_group = QGroupBox("💰 تحديث رصيد العمال")
            balance_group.setStyleSheet("""
                QGroupBox {
                    font-size: 16px;
                    font-weight: bold;
                    color: #374151;
                    border: 2px solid #10b981;
                    border-radius: 10px;
                    margin-top: 10px;
                    padding-top: 10px;
                    background: rgba(16, 185, 129, 0.05);
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 10px 0 10px;
                    background: white;
                    border-radius: 5px;
                }
            balance_layout = QGridLayout(balance_group)

            balance_label = QLabel("المبلغ:")
            self.bulk_balance_spin = QSpinBox()
            self.bulk_balance_spin.setRange(-999999, 999999)
            self.bulk_balance_spin.setValue(0)
            self.bulk_balance_spin.setStyleSheet("""
                QSpinBox {
                    padding: 8px;
                    border: 2px solid #10b981;
                    border-radius: 6px;
                    background: white;
                    font-size: 14px;
                }

            balance_type_combo = QComboBox()
            balance_type_combo.addItems(["إضافة للرصيد", "خصم من الرصيد", "تعيين رصيد جديد"])
            balance_type_combo.setStyleSheet("""
                QComboBox {
                    padding: 8px;
                    border: 2px solid #10b981;
                    border-radius: 6px;
                    background: white;
                    font-size: 14px;
                }

            update_balance_btn = QPushButton("💰 تحديث الرصيد")
            update_balance_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #10b981, stop:1 #059669);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 15px;
                    font-size: 14px;
                    font-weight: 600;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #059669, stop:1 #047857);
                }
            update_balance_btn.clicked.connect(lambda: self.bulk_update_employee_balance(dialog))

            balance_layout.addWidget(balance_label, 0, 0)
            balance_layout.addWidget(self.bulk_balance_spin, 0, 1)
            balance_layout.addWidget(balance_type_combo, 1, 0, 1, 2)
            balance_layout.addWidget(update_balance_btn, 1, 2)
            scroll_layout.addWidget(balance_group)

            # مجموعة العمليات الأخرى
            other_group = QGroupBox("🔧 عمليات أخرى")
            other_group.setStyleSheet("""
                QGroupBox {
                    font-size: 16px;
                    font-weight: bold;
                    color: #374151;
                    border: 2px solid #ef4444;
                    border-radius: 10px;
                    margin-top: 10px;
                    padding-top: 10px;
                    background: rgba(239, 68, 68, 0.05);
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 10px 0 10px;
                    background: white;
                    border-radius: 5px;
                }
            other_layout = QGridLayout(other_group)

            delete_btn = QPushButton("🗑️ حذف العمال المحددين")
            delete_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #ef4444, stop:1 #dc2626);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 15px;
                    font-size: 14px;
                    font-weight: 600;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #dc2626, stop:1 #b91c1c);
                }
            delete_btn.clicked.connect(lambda: self.bulk_delete_employees_confirmed(dialog))

            export_btn = QPushButton("📤 تصدير العمال المحددين")
            export_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #8b5cf6, stop:1 #7c3aed);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 15px;
                    font-size: 14px;
                    font-weight: 600;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #7c3aed, stop:1 #6d28d9);
                }
            export_btn.clicked.connect(lambda: self.export_selected_employees_confirmed(dialog))

            other_layout.addWidget(delete_btn, 0, 0)
            other_layout.addWidget(export_btn, 0, 1)
            scroll_layout.addWidget(other_group)

            scroll_area.setWidget(scroll_widget)
            layout.addWidget(scroll_area)

            # أزرار الحوار
            buttons_frame = QFrame()
            buttons_frame.setStyleSheet("""
                QFrame {
                    background: rgba(255, 255, 255, 0.8);
                    border-radius: 12px;
                    padding: 10px;
                }
            buttons_layout = QHBoxLayout(buttons_frame)
            buttons_layout.setSpacing(10)
            buttons_layout.setContentsMargins(10, 10, 10, 10)

            close_dialog_btn = QPushButton("إغلاق")
            close_dialog_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #6b7280, stop:1 #4b5563);
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 12px 20px;
                    font-size: 14px;
                    font-weight: 600;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #4b5563, stop:1 #374151);
                }
            close_dialog_btn.clicked.connect(dialog.reject)
            buttons_layout.addStretch()
            buttons_layout.addWidget(close_dialog_btn)

            layout.addWidget(buttons_frame)
            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في العمليات المجمعة: {str(e)}")

    def get_selected_employees_count(self):
        """الحصول على عدد العمال المحددين"""
        try:
            selected_rows = set()
            for item in self.employees_table.selectedItems():
                selected_rows.add(item.row())
            return len(selected_rows)
        except Exception as e:
            print(f"خطأ في حساب العمال المحددين: {str(e)}")
            return 0

    def bulk_update_employee_status(self, parent_dialog):
        try:
            selected_rows = set()
            for item in self.employees_table.selectedItems():
                selected_rows.add(item.row())

            if not selected_rows:
                show_error_message("خطأ", "لم يتم تحديد أي عمال")
                return

            new_status = self.bulk_status_combo.currentText()

            if show_confirmation_message("تأكيد", f"هل تريد تحديث حالة {len(selected_rows)} عامل إلى '{new_status}'؟"):
                updated_count = 0
                for row in selected_rows:
                    try:
                        # تحديث الحالة في الجدول
                        status_item = self.employees_table.item(row, 8)  # عمود الحالة
                        if status_item:
                            status_item.setText(new_status)
                            updated_count += 1
                    except Exception as e:
                        print(f"خطأ في تحديث الصف {row}: {str(e)}")

                show_info_message("تم", f"تم تحديث حالة {updated_count} عامل بنجاح")
                parent_dialog.accept()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تحديث الحالة: {str(e)}")

    def bulk_update_employee_balance(self, parent_dialog):
        """تحديث رصيد العمال المحددين"""
        try:
            selected_rows = set()
            for item in self.employees_table.selectedItems():
                selected_rows.add(item.row())

            if not selected_rows:
                show_error_message("خطأ", "لم يتم تحديد أي عمال")
                return

            amount = self.bulk_balance_spin.value()

            if show_confirmation_message("تأكيد", f"هل تريد تحديث رصيد {len(selected_rows)} عامل بمبلغ {amount}؟"):
                updated_count = 0
                for row in selected_rows:
                    try:
                        # تحديث الرصيد في الجدول
                        balance_item = self.employees_table.item(row, 5)  # عمود الرصيد
                        if balance_item:
                            current_balance = float(balance_item.text().replace(',', ''))
                            new_balance = current_balance + amount
                            balance_item.setText(f"{new_balance:,.0f}")
                            updated_count += 1
                    except Exception as e:
                        print(f"خطأ في تحديث الصف {row}: {str(e)}")

                show_info_message("تم", f"تم تحديث رصيد {updated_count} عامل بنجاح")
                parent_dialog.accept()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تحديث الرصيد: {str(e)}")

    def bulk_delete_employees_confirmed(self, parent_dialog):
        try:
            selected_rows = set()
            for item in self.employees_table.selectedItems():
                selected_rows.add(item.row())

            if not selected_rows:
                show_error_message("خطأ", "لم يتم تحديد أي عمال")
                return

            if show_confirmation_message("تحذير", f"هل تريد حذف {len(selected_rows)} عامل نهائياً؟\n\nهذا الإجراء لا يمكن التراجع عنه!"):
                # حذف الصفوف من الجدول (من الأسفل للأعلى لتجنب تغيير الفهارس)
                for row in sorted(selected_rows, reverse=True):
                    self.employees_table.removeRow(row)

                show_info_message("تم", f"تم حذف {len(selected_rows)} عامل بنجاح")
                parent_dialog.accept()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في الحذف: {str(e)}")

    def export_selected_employees_confirmed(self, parent_dialog):
        """تصدير العمال المحددين"""
        try:
            selected_rows = set()
            for item in self.employees_table.selectedItems():
                selected_rows.add(item.row())

            if not selected_rows:
                show_error_message("خطأ", "لم يتم تحديد أي عمال")
                return

            show_info_message("تم", f"سيتم تصدير {len(selected_rows)} عامل قريباً")
            parent_dialog.accept()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير: {str(e)}")

    def show_advanced_filters_dialog(self):
        try:
                                       QFrame, QPushButton, QWidget, QScrollArea,
                                       QGraphicsDropShadowEffect, QGroupBox, QGridLayout,
                                       QLineEdit, QComboBox, QSpinBox, QDateEdit)

            dialog = QDialog(self)
            dialog.setWindowTitle("🎯 الفلاتر المتقدمة الذكية")
            dialog.setFixedSize(650, 750)
            dialog.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint)

            # تطبيق النمط الموحد المتطور للحوار
            dialog.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #667eea, stop:0.2 #764ba2, stop:0.4 #f093fb,
                        stop:0.6 #4facfe, stop:0.8 #00f2fe, stop:1 #43e97b);
                    border: 4px solid qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #3b82f6, stop:0.5 #8b5cf6, stop:1 #06b6d4);
                    border-radius: 0px;
                    font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                }

            # إضافة تأثير الظل للنافذة
            shadow_effect = QGraphicsDropShadowEffect()
            shadow_effect.setBlurRadius(30)
            shadow_effect.setColor(QColor(0, 0, 0, 100))
            shadow_effect.setOffset(0, 10)
            dialog.setGraphicsEffect(shadow_effect)

            layout = QVBoxLayout()
            layout.setSpacing(20)
            layout.setContentsMargins(25, 25, 25, 25)

            # شريط العنوان المتطور
            title_bar = QFrame()
            title_bar.setFixedHeight(60)
            title_bar.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #1e3a8a, stop:0.3 #3b82f6, stop:0.7 #8b5cf6, stop:1 #ec4899);
                    border: none;
                    border-radius: 0px;
                    margin: 0px;
                    padding: 0px;
                }
            title_bar_layout = QHBoxLayout(title_bar)
            title_bar_layout.setContentsMargins(15, 0, 15, 0)
            title_bar_layout.setSpacing(15)

            # أزرار التحكم
            left_section = QFrame()
            left_section.setStyleSheet("QFrame { background: transparent; }")
            left_layout = QHBoxLayout(left_section)
            left_layout.setContentsMargins(0, 0, 0, 0)
            left_layout.setSpacing(8)

            # زر إغلاق
            close_btn = QPushButton("✕")
            close_btn.setFixedSize(30, 30)
            close_btn.setStyleSheet("""
                QPushButton {
                    background: rgba(239, 68, 68, 0.8);
                    color: white;
                    border: none;
                    border-radius: 0px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: rgba(220, 38, 38, 0.9);
                    transform: scale(1.1);
                }
            close_btn.clicked.connect(dialog.reject)

            left_layout.addWidget(close_btn)
            left_layout.addStretch()

            # عنوان النافذة
            center_section = QFrame()
            center_section.setStyleSheet("QFrame { background: transparent; }")
            center_layout = QHBoxLayout(center_section)
            center_layout.setContentsMargins(0, 0, 0, 0)

            title_icon = QLabel("🎯")
            title_icon.setStyleSheet("""
                QLabel {
                    font-size: 24px;
                    color: white;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 12px;
                    padding: 8px;
                    min-width: 40px;
                    max-width: 40px;
                    text-align: center;
                }
            title_icon.setAlignment(Qt.AlignCenter)

            title_text = QLabel("الفلاتر المتقدمة للعمال")
            title_text.setStyleSheet("""
                QLabel {
                    color: white;
                    font-weight: bold;
                    font-size: 18px;
                    background: transparent;
                    margin-left: 10px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
                }

            center_layout.addWidget(title_icon)
            center_layout.addWidget(title_text)

            # الجانب الأيمن
            right_section = QFrame()
            right_section.setStyleSheet("QFrame { background: transparent; }")
            right_layout = QHBoxLayout(right_section)
            right_layout.setContentsMargins(0, 0, 0, 0)
            right_layout.addStretch()

            title_bar_layout.addWidget(left_section, 1)
            title_bar_layout.addWidget(center_section, 0, Qt.AlignCenter)
            title_bar_layout.addWidget(right_section, 1)
            layout.addWidget(title_bar)

            # وصف تفصيلي
            desc_label = QLabel("✨ استخدم الفلاتر المتقدمة للبحث الدقيق في بيانات العمال")
            desc_label.setStyleSheet("""
                QLabel {
                    color: #475569;
                    font-size: 14px;
                    font-weight: 500;
                    padding: 10px;
                    background: rgba(255, 255, 255, 0.7);
                    border-radius: 10px;
                    border: 2px solid rgba(59, 130, 246, 0.2);
                }
            desc_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(desc_label)

            # منطقة التمرير للفلاتر
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setStyleSheet("""
                QScrollArea {
                    border: 2px solid #e2e8f0;
                    border-radius: 12px;
                    background: white;
                }
                QScrollBar:vertical {
                    background: #f1f5f9;
                    width: 12px;
                    border-radius: 6px;
                }
                QScrollBar::handle:vertical {
                    background: #3b82f6;
                    border-radius: 6px;
                    min-height: 20px;
                }

            scroll_widget = QWidget()
            scroll_layout = QVBoxLayout(scroll_widget)
            scroll_layout.setSpacing(15)
            scroll_layout.setContentsMargins(15, 15, 15, 15)

            # مجموعة البيانات الأساسية
            basic_group = QGroupBox("👤 البيانات الأساسية")
            basic_group.setStyleSheet("""
                QGroupBox {
                    font-size: 16px;
                    font-weight: bold;
                    color: #374151;
                    border: 2px solid #3b82f6;
                    border-radius: 10px;
                    margin-top: 10px;
                    padding-top: 10px;
                    background: rgba(59, 130, 246, 0.05);
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 10px 0 10px;
                    background: white;
                    border-radius: 5px;
                }
            basic_layout = QGridLayout(basic_group)

            # فلتر الاسم
            basic_layout.addWidget(QLabel("الاسم:"), 0, 0)
            self.filter_name = QLineEdit()
            self.filter_name.setPlaceholderText("ابحث بالاسم...")
            self.filter_name.setStyleSheet("""
                QLineEdit {
                    padding: 8px;
                    border: 2px solid #3b82f6;
                    border-radius: 6px;
                    background: white;
                    font-size: 14px;
                }
            basic_layout.addWidget(self.filter_name, 0, 1)

            # فلتر الهاتف
            basic_layout.addWidget(QLabel("الهاتف:"), 0, 2)
            self.filter_phone = QLineEdit()
            self.filter_phone.setPlaceholderText("ابحث بالهاتف...")
            self.filter_phone.setStyleSheet("""
                QLineEdit {
                    padding: 8px;
                    border: 2px solid #3b82f6;
                    border-radius: 6px;
                    background: white;
                    font-size: 14px;
                }
            basic_layout.addWidget(self.filter_phone, 0, 3)

            # فلتر الإيميل
            basic_layout.addWidget(QLabel("الإيميل:"), 1, 0)
            self.filter_email = QLineEdit()
            self.filter_email.setPlaceholderText("ابحث بالإيميل...")
            self.filter_email.setStyleSheet("""
                QLineEdit {
                    padding: 8px;
                    border: 2px solid #3b82f6;
                    border-radius: 6px;
                    background: white;
                    font-size: 14px;
                }
            basic_layout.addWidget(self.filter_email, 1, 1)

            # فلتر العنوان
            basic_layout.addWidget(QLabel("العنوان:"), 1, 2)
            self.filter_address = QLineEdit()
            self.filter_address.setPlaceholderText("ابحث بالعنوان...")
            self.filter_address.setStyleSheet("""
                QLineEdit {
                    padding: 8px;
                    border: 2px solid #3b82f6;
                    border-radius: 6px;
                    background: white;
                    font-size: 14px;
                }
            basic_layout.addWidget(self.filter_address, 1, 3)

            scroll_layout.addWidget(basic_group)

            # مجموعة الرصيد والمالية
            financial_group = QGroupBox("💰 البيانات المالية")
            financial_group.setStyleSheet("""
                QGroupBox {
                    font-size: 16px;
                    font-weight: bold;
                    color: #374151;
                    border: 2px solid #10b981;
                    border-radius: 10px;
                    margin-top: 10px;
                    padding-top: 10px;
                    background: rgba(16, 185, 129, 0.05);
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 10px 0 10px;
                    background: white;
                    border-radius: 5px;
                }
            financial_layout = QGridLayout(financial_group)

            # نوع الرصيد
            financial_layout.addWidget(QLabel("نوع الرصيد:"), 0, 0)
            self.filter_balance_type = QComboBox()
            self.filter_balance_type.addItems(["الكل", "موجب", "سالب", "صفر"])
            self.filter_balance_type.setStyleSheet("""
                QComboBox {
                    padding: 8px;
                    border: 2px solid #10b981;
                    border-radius: 6px;
                    background: white;
                    font-size: 14px;
                }
            financial_layout.addWidget(self.filter_balance_type, 0, 1)

            # نطاق الرصيد
            financial_layout.addWidget(QLabel("الرصيد من:"), 0, 2)
            self.filter_balance_min = QSpinBox()
            self.filter_balance_min.setRange(-999999, 999999)
            self.filter_balance_min.setValue(-999999)
            self.filter_balance_min.setStyleSheet("""
                QSpinBox {
                    padding: 8px;
                    border: 2px solid #10b981;
                    border-radius: 6px;
                    background: white;
                    font-size: 14px;
                }
            financial_layout.addWidget(self.filter_balance_min, 0, 3)

            financial_layout.addWidget(QLabel("إلى:"), 1, 2)
            self.filter_balance_max = QSpinBox()
            self.filter_balance_max.setRange(-999999, 999999)
            self.filter_balance_max.setValue(999999)
            self.filter_balance_max.setStyleSheet("""
                QSpinBox {
                    padding: 8px;
                    border: 2px solid #10b981;
                    border-radius: 6px;
                    background: white;
                    font-size: 14px;
                }
            financial_layout.addWidget(self.filter_balance_max, 1, 3)

            scroll_layout.addWidget(financial_group)

            # مجموعة الحالة والتواريخ
            status_group = QGroupBox("🔄 الحالة والتواريخ")
            status_group.setStyleSheet("""
                QGroupBox {
                    font-size: 16px;
                    font-weight: bold;
                    color: #374151;
                    border: 2px solid #f59e0b;
                    border-radius: 10px;
                    margin-top: 10px;
                    padding-top: 10px;
                    background: rgba(245, 158, 11, 0.05);
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 10px 0 10px;
                    background: white;
                    border-radius: 5px;
                }
            status_layout = QGridLayout(status_group)

            # فلتر الحالة
            status_layout.addWidget(QLabel("الحالة:"), 0, 0)
            self.filter_status = QComboBox()
            self.filter_status.addItems(["الكل", "نشط", "غير نشط", "في إجازة", "مفصول"])
            self.filter_status.setStyleSheet("""
                QComboBox {
                    padding: 8px;
                    border: 2px solid #f59e0b;
                    border-radius: 6px;
                    background: white;
                    font-size: 14px;
                }
            status_layout.addWidget(self.filter_status, 0, 1)

            # فلتر المنصب
            status_layout.addWidget(QLabel("المنصب:"), 0, 2)
            self.filter_position = QLineEdit()
            self.filter_position.setPlaceholderText("ابحث بالمنصب...")
            self.filter_position.setStyleSheet("""
                QLineEdit {
                    padding: 8px;
                    border: 2px solid #f59e0b;
                    border-radius: 6px;
                    background: white;
                    font-size: 14px;
                }
            status_layout.addWidget(self.filter_position, 0, 3)

            # تاريخ التوظيف من
            status_layout.addWidget(QLabel("تاريخ التوظيف من:"), 1, 0)
            self.filter_start_date = QDateEdit()
            self.filter_start_date.setCalendarPopup(True)
            self.filter_start_date.setDate(QDate.currentDate().addYears(-5))
            self.filter_start_date.setStyleSheet("""
                QDateEdit {
                    padding: 8px;
                    border: 2px solid #f59e0b;
                    border-radius: 6px;
                    background: white;
                    font-size: 14px;
                }
            status_layout.addWidget(self.filter_start_date, 1, 1)

            # تاريخ التوظيف إلى
            status_layout.addWidget(QLabel("إلى:"), 1, 2)
            self.filter_end_date = QDateEdit()
            self.filter_end_date.setCalendarPopup(True)
            self.filter_end_date.setDate(QDate.currentDate())
            self.filter_end_date.setStyleSheet("""
                QDateEdit {
                    padding: 8px;
                    border: 2px solid #f59e0b;
                    border-radius: 6px;
                    background: white;
                    font-size: 14px;
                }
            status_layout.addWidget(self.filter_end_date, 1, 3)

            scroll_layout.addWidget(status_group)

            scroll_area.setWidget(scroll_widget)
            layout.addWidget(scroll_area)

            # أزرار الحوار المتطورة
            buttons_frame = QFrame()
            buttons_frame.setStyleSheet("""
                QFrame {
                    background: rgba(255, 255, 255, 0.8);
                    border-radius: 12px;
                    padding: 10px;
                }
            buttons_layout = QHBoxLayout(buttons_frame)
            buttons_layout.setSpacing(10)
            buttons_layout.setContentsMargins(10, 10, 10, 10)

            # أزرار التحكم
            apply_btn = QPushButton("🎯 تطبيق الفلاتر")
            apply_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #3b82f6, stop:1 #1d4ed8);
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 12px 20px;
                    font-size: 14px;
                    font-weight: 600;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #1d4ed8, stop:1 #1e40af);
                }
            apply_btn.clicked.connect(lambda: self.apply_advanced_employee_filters(dialog))

            reset_btn = QPushButton("🔄 إعادة تعيين")
            reset_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #f59e0b, stop:1 #d97706);
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 12px 20px;
                    font-size: 14px;
                    font-weight: 600;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #d97706, stop:1 #b45309);
                }
            reset_btn.clicked.connect(self.reset_employee_filters)

            close_dialog_btn = QPushButton("إغلاق")
            close_dialog_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #6b7280, stop:1 #4b5563);
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 12px 20px;
                    font-size: 14px;
                    font-weight: 600;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #4b5563, stop:1 #374151);
                }
            close_dialog_btn.clicked.connect(dialog.reject)

            buttons_layout.addWidget(apply_btn)
            buttons_layout.addWidget(reset_btn)
            buttons_layout.addStretch()
            buttons_layout.addWidget(close_dialog_btn)

            layout.addWidget(buttons_frame)
            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في الفلاتر المتقدمة: {str(e)}")

    def apply_advanced_employee_filters(self, dialog):
        """تطبيق الفلاتر المتقدمة على جدول العمال"""
        try:
            # جمع معايير الفلترة من الحقول
            filters = {
                'name': self.filter_name.text().strip(),
                'phone': self.filter_phone.text().strip(),
                'email': self.filter_email.text().strip(),
                'address': self.filter_address.text().strip(),
                'position': self.filter_position.text().strip(),
                'balance_type': self.filter_balance_type.currentText(),
                'min_balance': self.filter_balance_min.value(),
                'max_balance': self.filter_balance_max.value(),
                'status': self.filter_status.currentText(),
                'start_date': self.filter_start_date.date().toPyDate(),
                'end_date': self.filter_end_date.date().toPyDate()
            }

            # تطبيق الفلاتر على الجدول
            filtered_count = 0
            total_rows = self.employees_table.rowCount()

            for row in range(total_rows):
                show_row = True

                # فلتر الاسم
                if filters['name']:
                    name_item = self.employees_table.item(row, 1)
                    if name_item and filters['name'].lower() not in name_item.text().lower():
                        show_row = False

                # فلتر الهاتف
                if filters['phone'] and show_row:
                    phone_item = self.employees_table.item(row, 2)
                    if phone_item and filters['phone'] not in phone_item.text():
                        show_row = False

                # فلتر الإيميل
                if filters['email'] and show_row:
                    email_item = self.employees_table.item(row, 3)
                    if email_item and filters['email'].lower() not in email_item.text().lower():
                        show_row = False

                # فلتر العنوان
                if filters['address'] and show_row:
                    address_item = self.employees_table.item(row, 4)
                    if address_item and filters['address'].lower() not in address_item.text().lower():
                        show_row = False

                # فلتر المنصب
                if filters['position'] and show_row:
                    position_item = self.employees_table.item(row, 6)
                    if position_item and filters['position'].lower() not in position_item.text().lower():
                        show_row = False

                # فلتر الحالة
                if filters['status'] != "الكل" and show_row:
                    status_item = self.employees_table.item(row, 8)
                    if status_item and filters['status'] not in status_item.text():
                        show_row = False

                # فلتر الرصيد
                if filters['balance_type'] != "الكل" and show_row:
                    balance_item = self.employees_table.item(row, 5)
                    if balance_item:
                        try:
                            balance = float(balance_item.text().replace(',', ''))
                            if filters['balance_type'] == "موجب" and balance <= 0:
                                show_row = False
                            elif filters['balance_type'] == "سالب" and balance >= 0:
                                show_row = False
                            elif filters['balance_type'] == "صفر" and balance != 0:
                                show_row = False
                            elif balance < filters['min_balance'] or balance > filters['max_balance']:
                                show_row = False
                        except:
                            show_row = False

                self.employees_table.setRowHidden(row, not show_row)
                if show_row:
                    filtered_count += 1

            show_info_message("تم", f"تم تطبيق الفلاتر بنجاح\nعدد النتائج: {filtered_count} من أصل {total_rows}")
            dialog.accept()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تطبيق الفلاتر: {str(e)}")

    def reset_employee_filters(self):
        try:
            # إعادة تعيين جميع الحقول
            if hasattr(self, 'filter_name'):
                self.filter_name.clear()
            if hasattr(self, 'filter_phone'):
                self.filter_phone.clear()
            if hasattr(self, 'filter_email'):
                self.filter_email.clear()
            if hasattr(self, 'filter_address'):
                self.filter_address.clear()
            if hasattr(self, 'filter_position'):
                self.filter_position.clear()
            if hasattr(self, 'filter_balance_type'):
                self.filter_balance_type.setCurrentText("الكل")
            if hasattr(self, 'filter_status'):
                self.filter_status.setCurrentText("الكل")

            # إظهار جميع الصفوف
            for row in range(self.employees_table.rowCount()):
                self.employees_table.setRowHidden(row, False)

            show_info_message("تم", "تم إعادة تعيين جميع الفلاتر")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إعادة تعيين الفلاتر: {str(e)}")

    def add_employee(self):
        """إضافة موظف جديد"""
        dialog = EmployeeDialog(self, session=self.session)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                try:
                    # إنشاء موظف جديد
                    employee = Employee(
                        name=data['name'],
                        position=data['position'],
                        phone=data['phone'],
                        address=data['address'],
                        hire_date=data['hire_date'],
                        notes=data['notes']
                    )

                    self.session.add(employee)
                    self.session.flush()  # للحصول على معرف العامل

                    # إضافة أرقام الهواتف
                    if 'phones' in data and data['phones']:
                        for phone_data in data['phones']:
                            phone = EmployeePhone(
                                employee_id=employee.id,
                                phone_number=phone_data.phone_number,
                                label=phone_data.label,
                                is_primary=phone_data.is_primary
                            )
                            self.session.add(phone)

                    self.session.commit()

                    show_info_message("نجح", "تم إضافة العامل بنجاح")
                    self.refresh_data()

                except Exception as e:
                    self.session.rollback()
                    show_error_message("خطأ", f"حدث خطأ أثناء إضافة العامل: {str(e)}")

    def edit_employee(self):
        current_row = self.employees_table.currentRow()
        if current_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عامل من الجدول")
            return

        # استخراج الرقم من النص الذي قد يحتوي على أيقونة
        id_text = self.employees_table.item(current_row, 0).text()
        # استخراج الرقم فقط من النص (إزالة الأيقونات والمسافات)
        employee_id = int(re.findall(r'\d+', id_text)[0])
        employee = self.session.query(Employee).get(employee_id)

        if not employee:
            show_error_message("خطأ", "العامل غير موجود")
            return

        dialog = EmployeeDialog(self, employee=employee, session=self.session)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                try:
                    # تحديث بيانات العامل
                    employee.name = data['name']
                    employee.position = data['position']
                    employee.phone = data['phone']
                    employee.address = data['address']
                    employee.hire_date = data['hire_date']
                    employee.notes = data['notes']

                    # حذف أرقام الهواتف القديمة
                    old_phones = self.session.query(EmployeePhone).filter_by(employee_id=employee.id).all()
                    for old_phone in old_phones:
                        self.session.delete(old_phone)

                    # إضافة أرقام الهواتف الجديدة
                    if 'phones' in data and data['phones']:
                        for phone_data in data['phones']:
                            phone = EmployeePhone(
                                employee_id=employee.id,
                                phone_number=phone_data.phone_number,
                                label=phone_data.label,
                                is_primary=phone_data.is_primary
                            )
                            self.session.add(phone)

                    self.session.commit()

                    show_info_message("نجح", "تم تحديث بيانات العامل بنجاح")
                    self.refresh_data()

                except Exception as e:
                    self.session.rollback()
                    show_error_message("خطأ", f"حدث خطأ أثناء تحديث بيانات العامل: {str(e)}")

    def show_employee_details(self):
        """عرض تفاصيل الموظف"""
        current_row = self.employees_table.currentRow()
        if current_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عامل من الجدول")
            return

        # استخراج الرقم من النص الذي قد يحتوي على أيقونة
        id_text = self.employees_table.item(current_row, 0).text()
        # استخراج الرقم فقط من النص (إزالة الأيقونات والمسافات)
        employee_id = int(re.findall(r'\d+', id_text)[0])
        employee = self.session.query(Employee).get(employee_id)

        if not employee:
            show_error_message("خطأ", "العامل غير موجود")
            return

        # إنشاء نافذة عرض التفاصيل المخصصة
        dialog = EmployeeDetailsDialog(self, employee=employee, session=self.session)
        dialog.exec_()

    def delete_employee(self):
        current_row = self.employees_table.currentRow()
        if current_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عامل من الجدول")
            return

        # استخراج الرقم من النص الذي قد يحتوي على أيقونة
        id_text = self.employees_table.item(current_row, 0).text()
        # استخراج الرقم فقط من النص (إزالة الأيقونات والمسافات)
        employee_id = int(re.findall(r'\d+', id_text)[0])
        employee = self.session.query(Employee).get(employee_id)

        if not employee:
            show_error_message("خطأ", "العامل غير موجود")
            return

        if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف العامل '{employee.name}'؟\nسيتم حذف جميع البيانات المرتبطة به."):
            try:
                self.session.delete(employee)
                self.session.commit()

                show_info_message("نجح", "تم حذف العامل بنجاح")
                self.refresh_data()

            except Exception as e:
                self.session.rollback()
                show_error_message("خطأ", f"حدث خطأ أثناء حذف العامل: {str(e)}")

    def generate_employees_report(self):
        """إنشاء تقرير العمال"""
        try:
            employees = self.session.query(Employee).all()

            if not employees:
                show_info_message("تنبيه", "لا توجد بيانات عمال لإنشاء التقرير")
                return

            # إنشاء تقرير بسيط
📋 تقرير العمال - {datetime.now().strftime('%Y-%m-%d')}

👥 إجمالي العمال: {len(employees)}

📝 قائمة العمال:
"""

            for i, employee in enumerate(employees[:10], 1):  # أول 10 عمال
                report_text += f"{i}. {employee.name} - {employee.position or 'غير محدد'}\n"

            if len(employees) > 10:
                report_text += f"... و {len(employees) - 10} عامل آخر"

            show_info_message("📋 تقرير العمال", report_text)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إنشاء التقرير: {str(e)}")

    def backup_employees_data(self):
        try:

            # الحصول على جميع بيانات العمال
            employees = self.session.query(Employee).all()

            if not employees:
                show_info_message("تنبيه", "لا توجد بيانات عمال للنسخ الاحتياطي")
                return

            # تحويل البيانات إلى قاموس
            backup_data = {
                "backup_date": datetime.now().isoformat(),
                "employees_count": len(employees),
                "employees": []
            }

            for employee in employees:
                emp_data = {
                    "id": employee.id,
                    "name": employee.name,
                    "position": employee.position,
                    "phone": employee.phone,
                    "address": getattr(employee, 'address', None),
                    "hire_date": employee.hire_date.isoformat() if employee.hire_date else None,
                    "notes": employee.notes
                }
                backup_data["employees"].append(emp_data)

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ النسخة الاحتياطية",
                f"employees_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "ملفات JSON (*.json)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, ensure_ascii=False, indent=2)

                show_info_message("نجح", f"تم إنشاء النسخة الاحتياطية بنجاح\nالملف: {file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_employees_data(self):
        """استعادة بيانات العمال من نسخة احتياطية"""
        try:

            # اختيار ملف النسخة الاحتياطية
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختيار النسخة الاحتياطية",
                "",
                "ملفات JSON (*.json)"
            )

            if not file_path:
                return

            # قراءة البيانات
            with open(file_path, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)

            if "employees" not in backup_data:
                show_error_message("خطأ", "ملف النسخة الاحتياطية غير صحيح")
                return

            # تأكيد الاستعادة
            if not show_confirmation_message(
                "تأكيد الاستعادة",
                f"هل تريد استعادة {len(backup_data['employees'])} عامل من النسخة الاحتياطية؟\nسيتم إضافة العمال الجدد فقط."
            ):
                return

            # استعادة البيانات
            restored_count = 0
            for emp_data in backup_data["employees"]:
                # التحقق من عدم وجود العامل مسبقاً
                existing = self.session.query(Employee).filter_by(name=emp_data["name"]).first()
                if not existing:
                    employee = Employee(
                        name=emp_data["name"],
                        position=emp_data.get("position"),
                        phone=emp_data.get("phone"),
                        address=emp_data.get("address"),
                        hire_date=datetime.fromisoformat(emp_data["hire_date"]).date() if emp_data.get("hire_date") else None,
                        notes=emp_data.get("notes")
                    )
                    self.session.add(employee)
                    restored_count += 1

            self.session.commit()
            self.refresh_data()

            show_info_message("نجح", f"تم استعادة {restored_count} عامل بنجاح")

        except Exception as e:
            self.session.rollback()
            show_error_message("خطأ", f"حدث خطأ في استعادة البيانات: {str(e)}")

    def export_data(self):
        try:

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ كملف Excel",
                "قائمة_العمال.xlsx",
                "ملفات Excel (*.xlsx)"
            )

            if not file_path:
                return

            # جمع البيانات من الجدول
            data = []
            headers = []

            # الحصول على عناوين الأعمدة
            for col in range(self.employees_table.columnCount()):
                headers.append(self.employees_table.horizontalHeaderItem(col).text())

            # جمع البيانات من الجدول
            for row in range(self.employees_table.rowCount()):
                row_data = []
                for col in range(self.employees_table.columnCount()):
                    item = self.employees_table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)

            # محاولة استخدام openpyxl لإنشاء ملف Excel
            try:

                wb = Workbook()
                ws = wb.active
                ws.title = "قائمة العمال"

                # إضافة العناوين
                for col, header in enumerate(headers, 1):
                    cell = ws.cell(row=1, column=col, value=header)
                    cell.font = Font(bold=True, color="FFFFFF")
                    cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center")

                # إضافة البيانات
                for row_idx, row_data in enumerate(data, 2):
                    for col_idx, value in enumerate(row_data, 1):
                        ws.cell(row=row_idx, column=col_idx, value=value)

                # حفظ الملف
                wb.save(file_path)
                show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")

            except ImportError:
                # إذا لم تكن openpyxl متاحة، استخدم CSV كبديل
                csv_path = file_path.replace('.xlsx', '.csv')

                with open(csv_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(headers)
                    writer.writerows(data)

                show_info_message("تم", f"تم تصدير البيانات كملف CSV إلى:\n{csv_path}\n\nملاحظة: لتصدير Excel، يرجى تثبيت مكتبة openpyxl")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")

    def export_to_csv(self):
        """تصدير بيانات الموظفين إلى CSV"""
        try:

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف CSV", "قائمة_الموظفين.csv", "ملفات CSV (*.csv)")
            if not file_path:
                return

            # جمع البيانات من الجدول
            data = []
            headers = []

            # الحصول على عناوين الأعمدة
            for col in range(self.employees_table.columnCount()):
                headers.append(self.employees_table.horizontalHeaderItem(col).text())

            # جمع البيانات من الجدول
            for row in range(self.employees_table.rowCount()):
                row_data = []
                for col in range(self.employees_table.columnCount()):
                    item = self.employees_table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)

            # كتابة البيانات إلى ملف CSV
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(headers)
                writer.writerows(data)

            show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

    def export_to_json(self):
        try:

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف JSON", "قائمة_الموظفين.json", "ملفات JSON (*.json)")
            if not file_path:
                return

            # جمع البيانات من الجدول
            data = []
            headers = []

            # الحصول على عناوين الأعمدة
            for col in range(self.employees_table.columnCount()):
                headers.append(self.employees_table.horizontalHeaderItem(col).text())

            # جمع البيانات من الجدول
            for row in range(self.employees_table.rowCount()):
                row_data = {}
                for col in range(self.employees_table.columnCount()):
                    item = self.employees_table.item(row, col)
                    row_data[headers[col]] = item.text() if item else ""
                data.append(row_data)

            # كتابة البيانات إلى ملف JSON
            with open(file_path, 'w', encoding='utf-8') as jsonfile:
                json.dump(data, jsonfile, ensure_ascii=False, indent=2)

            show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

    def show_statistics(self):
        """عرض الإحصائيات الأساسية للعمال"""
        try:
            # إجمالي عدد العمال
            total_employees = self.session.query(Employee).count()

            # العمال النشطين (لديهم أجور في آخر 30 يوم)
            thirty_days_ago = datetime.now() - timedelta(days=30)
            active_employee_ids = self.session.query(DailyWage.employee_id).filter(
                DailyWage.wage_date >= thirty_days_ago
            ).distinct().count()

            # العمال الجدد (تم توظيفهم في آخر 30 يوم)
            new_employees = self.session.query(Employee).filter(
                Employee.hire_date >= thirty_days_ago
            ).count()

            # إجمالي الأجور المدفوعة هذا الشهر
            current_month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            monthly_wages = self.session.query(func.sum(DailyWage.net_amount)).filter(
                DailyWage.wage_date >= current_month_start
            ).scalar() or 0


📈 الإحصائيات العامة:
• إجمالي عدد العمال: {total_employees}
• عمال نشطين (آخر 30 يوم): {active_employee_ids}
• عمال جدد (آخر 30 يوم): {new_employees}
• عمال غير نشطين: {total_employees - active_employee_ids}

💰 الإحصائيات المالية:
• إجمالي الأجور هذا الشهر: {monthly_wages:.2f} ج.م
• متوسط الأجر للعامل النشط: {(monthly_wages / max(active_employee_ids, 1)):.2f} ج.م

📊 معدلات النشاط:
• نسبة العمال النشطين: {(active_employee_ids / max(total_employees, 1) * 100):.1f}%
• نسبة العمال الجدد: {(new_employees / max(total_employees, 1) * 100):.1f}%"""

            show_info_message("إحصائيات العمال", stats_text)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء حساب الإحصائيات: {str(e)}")

    def show_detailed_statistics(self):
        try:

            # إحصائيات مفصلة
            total_employees = self.session.query(Employee).count()

            # أكثر العمال نشاطاً (حسب عدد أيام العمل)
            top_employees = self.session.query(
                Employee.name,
                func.count(DailyWage.id).label('work_days_count'),
                func.sum(DailyWage.net_amount).label('total_earnings')
            ).join(DailyWage).group_by(Employee.id).order_by(func.count(DailyWage.id).desc()).limit(5).all()

            # إحصائيات المناصب
            position_stats = self.session.query(
                Employee.position,
                func.count(Employee.id).label('count')
            ).filter(Employee.position.isnot(None)).group_by(Employee.position).all()

            detailed_stats = f"""📊 إحصائيات مفصلة للعمال:

📈 الإحصائيات العامة:
• إجمالي عدد العمال: {total_employees}
• عدد المناصب المختلفة: {len(position_stats)}


            for i, emp in enumerate(top_employees[:3], 1):
                detailed_stats += f"""
{i}. {emp.name}
    • أيام العمل: {emp.work_days_count}

            detailed_stats += f"""

            for position in position_stats[:5]:
                detailed_stats += f"""

            show_info_message("إحصائيات مفصلة", detailed_stats)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء حساب الإحصائيات المفصلة: {str(e)}")

    def show_wages_analysis(self):
        """عرض تحليل الأجور"""
        try:

            # تحليل الأجور
            current_month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)

            # إجمالي الأجور هذا الشهر
            monthly_wages = self.session.query(func.sum(DailyWage.net_amount)).filter(
                DailyWage.wage_date >= current_month_start
            ).scalar() or 0

            # إجمالي السُلف هذا الشهر
            monthly_advances = self.session.query(func.sum(DailyWage.advance)).filter(
                DailyWage.wage_date >= current_month_start
            ).scalar() or 0

            # متوسط الأجر اليومي
            avg_daily_wage = self.session.query(func.avg(DailyWage.daily_amount)).scalar() or 0

            # أعلى وأقل أجر يومي
            max_daily_wage = self.session.query(func.max(DailyWage.daily_amount)).scalar() or 0
            min_daily_wage = self.session.query(func.min(DailyWage.daily_amount)).scalar() or 0


📊 إحصائيات الشهر الحالي:
• إجمالي الأجور المدفوعة: {monthly_wages:.2f} ج.م
• إجمالي السُلف المدفوعة: {monthly_advances:.2f} ج.م
• صافي الأجور: {(monthly_wages - monthly_advances):.2f} ج.م

📈 تحليل الأجور اليومية:
• متوسط الأجر اليومي: {avg_daily_wage:.2f} ج.م
• أعلى أجر يومي: {max_daily_wage:.2f} ج.م
• أقل أجر يومي: {min_daily_wage:.2f} ج.م

💡 مؤشرات:
• نسبة السُلف من الأجور: {(monthly_advances / max(monthly_wages, 1) * 100):.1f}%
• متوسط الأجر الشهري للعامل: {(monthly_wages / max(self.session.query(Employee).count(), 1)):.2f} ج.م

📝 توصيات:
• {'تقليل السُلف المدفوعة' if monthly_advances > monthly_wages * 0.3 else 'نسبة السُلف مناسبة'}
• مراجعة الأجور بانتظام
• توثيق جميع المدفوعات"""

            show_info_message("تحليل الأجور", analysis_text)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تحليل الأجور: {str(e)}")

    def show_monthly_report(self):
        try:

            # الحصول على بداية ونهاية الشهر الحالي
            now = datetime.now()
            start_of_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            if now.month == 12:
                end_of_month = start_of_month.replace(year=now.year + 1, month=1) - timedelta(days=1)
            else:
                end_of_month = start_of_month.replace(month=now.month + 1) - timedelta(days=1)

            # الحصول على أجور الشهر الحالي
            monthly_wages = self.session.query(DailyWage).filter(
                DailyWage.wage_date >= start_of_month,
                DailyWage.wage_date <= end_of_month
            ).all()

            total_amount = sum(wage.total_amount for wage in monthly_wages)
            total_advances = sum(wage.advance for wage in monthly_wages)
            net_amount = sum(wage.net_amount for wage in monthly_wages)

            active_employees = len(set(wage.employee_id for wage in monthly_wages))
            total_work_days = sum(wage.work_days for wage in monthly_wages)

            monthly_report = f"""📅 التقرير الشهري - {now.strftime('%B %Y')}:

📊 إحصائيات الشهر:
• عدد العمال النشطين: {active_employees}
• إجمالي أيام العمل: {total_work_days:.1f} يوم
• عدد المدفوعات: {len(monthly_wages)}

💰 الإحصائيات المالية:
• إجمالي الأجور: {total_amount:.2f} ج.م
• إجمالي السُلف: {total_advances:.2f} ج.م
• صافي المدفوعات: {net_amount:.2f} ج.م

📈 المتوسطات:
• متوسط الأجر للعامل: {(net_amount / max(active_employees, 1)):.2f} ج.م
• متوسط أيام العمل للعامل: {(total_work_days / max(active_employees, 1)):.1f} يوم
• متوسط الأجر اليومي: {(total_amount / max(total_work_days, 1)):.2f} ج.م

📝 ملاحظات:
• تم حساب البيانات من {start_of_month.strftime('%Y-%m-%d')} إلى {end_of_month.strftime('%Y-%m-%d')}

            show_info_message("التقرير الشهري", monthly_report)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنشاء التقرير الشهري: {str(e)}")

    def backup_employees_data(self):
        """إنشاء نسخة احتياطية من بيانات العمال"""
        try:

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ النسخة الاحتياطية",
                f"نسخة_احتياطية_العمال_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "ملفات JSON (*.json)"
            )
            if not file_path:
                return

            # جمع بيانات العمال
            employees = self.session.query(Employee).all()
            backup_data = {
                'backup_date': datetime.now().isoformat(),
                'employees_count': len(employees),
                'employees': []
            }

            for employee in employees:
                employee_data = {
                    'name': employee.name,
                    'position': employee.position,
                    'phone': employee.phone,
                    'address': employee.address,
                    'hire_date': employee.hire_date.isoformat() if employee.hire_date else None,
                    'notes': employee.notes
                }

                # إضافة أرقام الهواتف المتعددة
                phones = self.session.query(EmployeePhone).filter_by(employee_id=employee.id).all()
                employee_data['phones'] = [
                    {
                        'phone_number': phone.phone_number,
                        'label': phone.label,
                        'is_primary': phone.is_primary
                    } for phone in phones
                ]

                backup_data['employees'].append(employee_data)

            # حفظ البيانات
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)

            show_info_message("تم", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_employees_data(self):
        try:

            # عرض مربع حوار فتح الملف
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختيار النسخة الاحتياطية",
                "",
                "ملفات JSON (*.json)"
            )
            if not file_path:
                return

            # تأكيد الاستعادة
            if not show_confirmation_message(
                "تأكيد الاستعادة",
                "هل أنت متأكد من استعادة البيانات؟\nسيتم استبدال البيانات الحالية."
            ):
                return

            # قراءة البيانات
            with open(file_path, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)

            # التحقق من صحة البيانات
            if 'employees' not in backup_data:
                show_error_message("خطأ", "ملف النسخة الاحتياطية غير صحيح")
                return

            # استعادة البيانات
            restored_count = 0
            for employee_data in backup_data['employees']:
                # التحقق من وجود العامل
                existing_employee = self.session.query(Employee).filter_by(name=employee_data['name']).first()

                if not existing_employee:
                    # إنشاء عامل جديد
                    hire_date = None
                    if employee_data.get('hire_date'):
                        hire_date = datetime.fromisoformat(employee_data['hire_date']).date()

                    employee = Employee(
                        name=employee_data['name'],
                        position=employee_data.get('position'),
                        phone=employee_data.get('phone'),
                        address=employee_data.get('address'),
                        hire_date=hire_date,
                        notes=employee_data.get('notes')
                    )
                    self.session.add(employee)
                    self.session.flush()

                    # إضافة أرقام الهواتف
                    for phone_data in employee_data.get('phones', []):
                        phone = EmployeePhone(
                            employee_id=employee.id,
                            phone_number=phone_data['phone_number'],
                            label=phone_data.get('label'),
                            is_primary=phone_data.get('is_primary', False)
                        )
                        self.session.add(phone)

                    restored_count += 1

            self.session.commit()
            show_info_message("تم", f"تم استعادة {restored_count} عامل بنجاح")
            self.refresh_data()

        except Exception as e:
            self.session.rollback()
            show_error_message("خطأ", f"حدث خطأ أثناء استعادة البيانات: {str(e)}")

    def generate_employees_report(self):
        """إنشاء تقرير شامل للعمال"""
        try:

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ تقرير العمال",
                "تقرير_العمال.pdf",
                "ملفات PDF (*.pdf)"
            )
            if not file_path:
                return

            # إنشاء طابعة PDF
            printer = QPrinter(QPrinter.HighResolution)
            printer.setOutputFormat(QPrinter.PdfFormat)
            printer.setOutputFileName(file_path)
            printer.setPageSize(QPrinter.A4)
            printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

            # إنشاء مستند نصي
            document = QTextDocument()

            # إنشاء محتوى HTML للتقرير
            html_content = self.generate_employees_report_html()
            document.setHtml(html_content)

            # طباعة المستند إلى PDF
            document.print_(printer)

            show_info_message("تم", f"تم إنشاء التقرير بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنشاء التقرير: {str(e)}")

    def generate_employees_report_html(self):
        try:

            # الحصول على جميع العمال
            employees = self.session.query(Employee).all()

            # حساب الإحصائيات
            total_employees = len(employees)

            # إحصائيات الأجور
            total_wages = self.session.query(func.sum(DailyWage.net_amount)).scalar() or 0
            total_work_days = self.session.query(func.sum(DailyWage.work_days)).scalar() or 0

            html_content = f"""
            <html dir="rtl">
            <head>
                <meta charset="utf-8">
                <title>تقرير العمال</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ color: #2c3e50; text-align: center; }}
                    h2 {{ color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px; }}
                    table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                    th {{ background-color: #3498db; color: white; }}
                    .summary {{ background-color: #ecf0f1; padding: 15px; border-radius: 5px; margin: 20px 0; }}
                </style>
            </head>
            <body>
                <h1>👷 تقرير العمال</h1>
                <p style="text-align: center;">تاريخ التقرير: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M')}</p>

                <div class="summary">
                    <h2>📊 ملخص الإحصائيات</h2>
                    <p>إجمالي عدد العمال: <strong>{total_employees}</strong></p>
                    <p>إجمالي أيام العمل: <strong>{total_work_days:.1f}</strong> يوم</p>
                    <p>إجمالي الأجور المدفوعة: <strong>{total_wages:.2f}</strong> ج.م</p>
                    <p>متوسط الأجر للعامل: <strong>{(total_wages / max(total_employees, 1)):.2f}</strong> ج.م</p>
                </div>

                <h2>📋 قائمة العمال</h2>
                <table>
                    <tr>
                        <th>الرقم</th>
                        <th>الاسم</th>
                        <th>المنصب</th>
                        <th>رقم الهاتف</th>
                        <th>تاريخ التوظيف</th>
                        <th>العنوان</th>
                        <th>ملاحظات</th>
                    </tr>

            # إضافة بيانات العمال
            for employee in employees:
                html_content += f"""
                    <tr>
                        <td>{employee.id}</td>
                        <td>{employee.name}</td>
                        <td>{employee.position or 'غير محدد'}</td>
                        <td>{employee.phone or 'غير متوفر'}</td>
                        <td>{employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else 'غير محدد'}</td>
                        <td>{employee.address or 'غير متوفر'}</td>
                        <td>{employee.notes or 'لا توجد ملاحظات'}</td>
                    </tr>

            html_content += """
                </table>
            </body>
            </html>

            return html_content
        except Exception as e:
            return f"""
            <html dir="rtl">
            <body>
                <h1>خطأ في إنشاء التقرير</h1>
                <p>حدث خطأ أثناء إنشاء تقرير العمال: {str(e)}</p>
            </body>
            </html>

    def show_contact_details(self):
        """عرض تفاصيل الاتصال للموظف"""
        current_row = self.employees_table.currentRow()
        if current_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عامل من الجدول")
            return

        # استخراج الرقم من النص الذي قد يحتوي على أيقونة
        id_text = self.employees_table.item(current_row, 0).text()
        # استخراج الرقم فقط من النص (إزالة الأيقونات والمسافات)
        employee_id = int(re.findall(r'\d+', id_text)[0])
        employee = self.session.query(Employee).get(employee_id)

        if not employee:
            show_error_message("خطأ", "العامل غير موجود")
            return

        try:
            # البحث عن أرقام هواتف الموظف
            phone_records = self.session.query(EmployeePhone).filter_by(employee_id=employee.id).all()

            # إنشاء نص تفاصيل الاتصال مختصر
            primary_phone = employee.phone or 'غير متوفر'
            if phone_records:
                primary_phone = phone_records[0].phone_number

            contact_info = f"📞 {employee.name} | 📱 {primary_phone} | 💼 {employee.position or 'غير محدد'} | 📍 {employee.address or 'غير متوفر'}"

            show_info_message("تفاصيل الاتصال", contact_info)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء عرض تفاصيل الاتصال: {str(e)}")

    def show_work_history(self):
        current_row = self.employees_table.currentRow()
        if current_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عامل من الجدول")
            return

        # استخراج الرقم من النص الذي قد يحتوي على أيقونة
        id_text = self.employees_table.item(current_row, 0).text()
        # استخراج الرقم فقط من النص (إزالة الأيقونات والمسافات)
        employee_id = int(re.findall(r'\d+', id_text)[0])
        employee = self.session.query(Employee).get(employee_id)

        if not employee:
            show_error_message("خطأ", "العامل غير موجود")
            return

        try:
            # البحث عن أجور الموظف
            wages = self.session.query(DailyWage).filter_by(employee_id=employee.id).order_by(DailyWage.wage_date.desc()).all()

            # إنشاء نص سجل العمل مختصر
            if wages:
                total_days = sum(wage.work_days for wage in wages)
                total_amount = sum(wage.total_amount for wage in wages)
                net_total = sum(wage.net_amount for wage in wages)
                history_info = f"📋 {employee.name} | 💼 {employee.position or 'غير محدد'} | 📊 اليوميات: {len(wages)} | 🕐 الأيام: {total_days:.1f} | 💰 الإجمالي: {total_amount:.2f} ر.س | 💵 الصافي: {net_total:.2f} ر.س"
            else:
                history_info = f"📋 {employee.name} | 💼 {employee.position or 'غير محدد'} | 📊 لا توجد يوميات مسجلة"

            show_info_message("سجل العمل", history_info)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء عرض سجل العمل: {str(e)}")

    def manage_attachments(self):
        """إدارة المرفقات"""
        current_row = self.employees_table.currentRow()
        if current_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عامل من الجدول")
            return

        try:
            # الحصول على معرف العامل
            employee_id = int(self.employees_table.item(current_row, 0).text())
            employee = self.session.query(Employee).get(employee_id)

            if not employee:
                show_error_message("خطأ", "لم يتم العثور على العامل")
                return

            # إنشاء نافذة إدارة المرفقات
            dialog = EmployeeAttachmentsDialog(self, employee, self.session)
            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء فتح نافذة المرفقات: {str(e)}")

    def show_advanced_details(self):
        current_row = self.employees_table.currentRow()
        if current_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عامل من الجدول")
            return
        show_info_message("قريباً", "ستتم إضافة التفاصيل المتقدمة قريباً")

    def make_direct_call(self):
        """اتصال مباشر"""
        current_row = self.employees_table.currentRow()
        if current_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عامل من الجدول")
            return
        show_info_message("قريباً", "ستتم إضافة ميزة الاتصال المباشر قريباً")

    def send_whatsapp(self):
        current_row = self.employees_table.currentRow()
        if current_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عامل من الجدول")
            return
        show_info_message("قريباً", "ستتم إضافة ميزة واتساب قريباً")

    def send_email(self):
        """إرسال إيميل"""
        current_row = self.employees_table.currentRow()
        if current_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عامل من الجدول")
            return
        show_info_message("قريباً", "ستتم إضافة ميزة الإيميل قريباً")

    def send_sms(self):
        current_row = self.employees_table.currentRow()
        if current_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عامل من الجدول")
            return
        show_info_message("قريباً", "ستتم إضافة ميزة الرسائل النصية قريباً")

    def show_call_history(self):
        """عرض سجل المكالمات"""
        current_row = self.employees_table.currentRow()
        if current_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عامل من الجدول")
            return
        show_info_message("قريباً", "ستتم إضافة سجل المكالمات قريباً")

    def show_performance_statistics(self):
        show_info_message("قريباً", "ستتم إضافة إحصائيات الأداء قريباً")

    def create_elegant_separator(self):
        """إنشاء فاصل أسود يملأ ارتفاع الإطار من الأعلى للأسفل - موحد مع العملاء والموردين"""
        try:
            separator = QFrame()
            separator.setFixedWidth(2)  # عرض رفيع جداً
            separator.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Expanding)  # يتمدد عمودياً
                QFrame {
                    background-color: #000000;
                    border: none;
                    border-radius: 1px;
                    margin: 2px 3px;
                }
            """)
            return separator
        except Exception as e:
            print(f"❌ خطأ في إنشاء الفاصل الأسود: {str(e)}")
            return QFrame()

    def style_advanced_button(self, button, button_type, has_menu=False):
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر
            colors = {
                'primary': {
                    'bg_start': '#1e3a8a',
                    'bg_mid': '#3b82f6',
                    'bg_end': '#1d4ed8',
                    'bg_bottom': '#1e40af',
                    'hover_start': '#3b82f6',
                    'hover_mid': '#60a5fa',
                    'hover_end': '#2563eb',
                    'hover_bottom': '#1d4ed8',
                    'hover_border': '#2563eb',
                    'pressed_start': '#1e40af',
                    'pressed_mid': '#1d4ed8',
                    'pressed_end': '#1e3a8a',
                    'pressed_bottom': '#172554',
                    'pressed_border': '#1e3a8a',
                    'border': '#1d4ed8',
                    'text': '#ffffff'
                },
                'emerald': {
                    'bg_start': '#064e3b',
                    'bg_mid': '#10b981',
                    'bg_end': '#059669',
                    'bg_bottom': '#022c22',
                    'hover_start': '#10b981',
                    'hover_mid': '#34d399',
                    'hover_end': '#059669',
                    'hover_bottom': '#064e3b',
                    'hover_border': '#059669',
                    'pressed_start': '#022c22',
                    'pressed_mid': '#064e3b',
                    'pressed_end': '#014737',
                    'pressed_bottom': '#014737',
                    'pressed_border': '#064e3b',
                    'border': '#059669',
                    'text': '#ffffff'
                },
                'danger': {
                    'bg_start': '#991b1b',
                    'bg_mid': '#ef4444',
                    'bg_end': '#dc2626',
                    'bg_bottom': '#7f1d1d',
                    'hover_start': '#ef4444',
                    'hover_mid': '#f87171',
                    'hover_end': '#dc2626',
                    'hover_bottom': '#991b1b',
                    'hover_border': '#dc2626',
                    'pressed_start': '#7f1d1d',
                    'pressed_mid': '#991b1b',
                    'pressed_end': '#450a0a',
                    'pressed_bottom': '#450a0a',
                    'pressed_border': '#991b1b',
                    'border': '#dc2626',
                    'text': '#ffffff'
                },
                'warning': {
                    'bg_start': '#92400e',
                    'bg_mid': '#f59e0b',
                    'bg_end': '#d97706',
                    'bg_bottom': '#78350f',
                    'hover_start': '#f59e0b',
                    'hover_mid': '#fbbf24',
                    'hover_end': '#d97706',
                    'hover_bottom': '#92400e',
                    'hover_border': '#d97706',
                    'pressed_start': '#78350f',
                    'pressed_mid': '#92400e',
                    'pressed_end': '#451a03',
                    'pressed_bottom': '#451a03',
                    'pressed_border': '#92400e',
                    'border': '#d97706',
                    'text': '#ffffff'
                },
                'purple': {
                    'bg_start': '#581c87',
                    'bg_mid': '#a855f7',
                    'bg_end': '#9333ea',
                    'bg_bottom': '#4c1d95',
                    'hover_start': '#a855f7',
                    'hover_mid': '#c084fc',
                    'hover_end': '#9333ea',
                    'hover_bottom': '#581c87',
                    'hover_border': '#9333ea',
                    'pressed_start': '#4c1d95',
                    'pressed_mid': '#581c87',
                    'pressed_end': '#312e81',
                    'pressed_bottom': '#312e81',
                    'pressed_border': '#581c87',
                    'border': '#9333ea',
                    'text': '#ffffff'
                },
                'indigo': {
                    'bg_start': '#3730a3',
                    'bg_mid': '#6366f1',
                    'bg_end': '#4f46e5',
                    'bg_bottom': '#312e81',
                    'hover_start': '#6366f1',
                    'hover_mid': '#818cf8',
                    'hover_end': '#4f46e5',
                    'hover_bottom': '#3730a3',
                    'hover_border': '#4f46e5',
                    'pressed_start': '#312e81',
                    'pressed_mid': '#3730a3',
                    'pressed_end': '#1e1b4b',
                    'pressed_bottom': '#1e1b4b',
                    'pressed_border': '#3730a3',
                    'border': '#4f46e5',
                    'text': '#ffffff'
                },
                'modern_teal': {
                    'bg_start': '#0891b2',
                    'bg_mid': '#0ea5e9',
                    'bg_end': '#0284c7',
                    'bg_bottom': '#075985',
                    'hover_start': '#0ea5e9',
                    'hover_mid': '#38bdf8',
                    'hover_end': '#0891b2',
                    'hover_bottom': '#0284c7',
                    'hover_border': '#0891b2',
                    'pressed_start': '#0284c7',
                    'pressed_mid': '#0891b2',
                    'pressed_end': '#075985',
                    'pressed_bottom': '#0c4a6e',
                    'pressed_border': '#075985',
                    'border': '#0891b2',
                    'text': '#ffffff'
                },
                'rose': {
                    'bg_start': '#9f1239',
                    'bg_mid': '#f43f5e',
                    'bg_end': '#e11d48',
                    'bg_bottom': '#881337',
                    'hover_start': '#f43f5e',
                    'hover_mid': '#fb7185',
                    'hover_end': '#e11d48',
                    'hover_bottom': '#9f1239',
                    'hover_border': '#e11d48',
                    'pressed_start': '#881337',
                    'pressed_mid': '#9f1239',
                    'pressed_end': '#4c0519',
                    'pressed_bottom': '#4c0519',
                    'pressed_border': '#9f1239',
                    'border': '#e11d48',
                    'text': '#ffffff'
                },
                'cyan': {
                    'bg_start': '#155e75',
                    'bg_mid': '#06b6d4',
                    'bg_end': '#0891b2',
                    'bg_bottom': '#164e63',
                    'hover_start': '#06b6d4',
                    'hover_mid': '#67e8f9',
                    'hover_end': '#0891b2',
                    'hover_bottom': '#155e75',
                    'hover_border': '#0891b2',
                    'pressed_start': '#164e63',
                    'pressed_mid': '#155e75',
                    'pressed_end': '#083344',
                    'pressed_bottom': '#083344',
                    'pressed_border': '#155e75',
                    'border': '#0891b2',
                    'text': '#ffffff'
                },
                'orange': {
                    'bg_start': '#9a3412',
                    'bg_mid': '#ea580c',
                    'bg_end': '#dc2626',
                    'bg_bottom': '#7c2d12',
                    'hover_start': '#ea580c',
                    'hover_mid': '#fb923c',
                    'hover_end': '#dc2626',
                    'hover_bottom': '#9a3412',
                    'hover_border': '#dc2626',
                    'pressed_start': '#7c2d12',
                    'pressed_mid': '#9a3412',
                    'pressed_end': '#431407',
                    'pressed_bottom': '#431407',
                    'pressed_border': '#9a3412',
                    'border': '#dc2626',
                    'text': '#ffffff'
                },
                'lime': {
                    'bg_start': '#365314',
                    'bg_mid': '#84cc16',
                    'bg_end': '#65a30d',
                    'bg_bottom': '#1a2e05',
                    'hover_start': '#84cc16',
                    'hover_mid': '#a3e635',
                    'hover_end': '#65a30d',
                    'hover_bottom': '#365314',
                    'hover_border': '#65a30d',
                    'pressed_start': '#1a2e05',
                    'pressed_mid': '#365314',
                    'pressed_end': '#14532d',
                    'pressed_bottom': '#14532d',
                    'pressed_border': '#365314',
                    'border': '#65a30d',
                    'text': '#ffffff'
                },
                'amber': {
                    'bg_start': '#92400e',
                    'bg_mid': '#f59e0b',
                    'bg_end': '#d97706',
                    'bg_bottom': '#78350f',
                    'hover_start': '#f59e0b',
                    'hover_mid': '#fbbf24',
                    'hover_end': '#d97706',
                    'hover_bottom': '#92400e',
                    'hover_border': '#d97706',
                    'pressed_start': '#78350f',
                    'pressed_mid': '#92400e',
                    'pressed_end': '#451a03',
                    'pressed_bottom': '#451a03',
                    'pressed_border': '#92400e',
                    'border': '#d97706',
                    'text': '#ffffff'
                },
                'gray': {
                    'bg_start': '#374151',
                    'bg_mid': '#6b7280',
                    'bg_end': '#4b5563',
                    'bg_bottom': '#1f2937',
                    'hover_start': '#6b7280',
                    'hover_mid': '#9ca3af',
                    'hover_end': '#4b5563',
                    'hover_bottom': '#374151',
                    'hover_border': '#4b5563',
                    'pressed_start': '#1f2937',
                    'pressed_mid': '#374151',
                    'pressed_end': '#111827',
                    'pressed_bottom': '#111827',
                    'pressed_border': '#374151',
                    'border': '#4b5563',
                    'text': '#ffffff'
                },
                'info': {
                    'bg_start': '#0891b2',
                    'bg_mid': '#0ea5e9',
                    'bg_end': '#0284c7',
                    'bg_bottom': '#075985',
                    'hover_start': '#0ea5e9',
                    'hover_mid': '#38bdf8',
                    'hover_end': '#0891b2',
                    'hover_bottom': '#0284c7',
                    'hover_border': '#0891b2',
                    'pressed_start': '#0284c7',
                    'pressed_mid': '#0891b2',
                    'pressed_end': '#075985',
                    'pressed_bottom': '#0c4a6e',
                    'pressed_border': '#075985',
                    'border': '#0891b2',
                    'text': '#ffffff'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة
            menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else ""

            # تطبيق التصميم المتطور
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.3 {color_scheme['bg_mid']},
                        stop:0.7 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 2px solid {color_scheme['border']};
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-weight: bold;
                    font-size: 13px;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.3 {color_scheme['hover_mid']},
                        stop:0.7 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 2px solid {color_scheme['hover_border']};
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.3 {color_scheme['pressed_mid']},
                        stop:0.7 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 2px solid {color_scheme['pressed_border']};
                }}
                {menu_indicator}

            button.setStyleSheet(style)
            print(f"✅ تم تطبيق التصميم المتطور على الزر: {button.text()} - نوع: {button_type}")

        except Exception as e:
            print(f"❌ خطأ في تطبيق التصميم على الزر: {str(e)}")

    def refresh_data(self):
        """تحديث البيانات - مطابق للعملاء والموردين"""
        try:
            employees = self.session.query(Employee).all()
            self.populate_table(employees)
            print("✅ تم تحديث بيانات العمال بنجاح")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")
            print(f"❌ خطأ في تحديث البيانات: {str(e)}")

    def adjust_balance(self):
        try:
            current_row = self.employees_table.currentRow()
            if current_row < 0:
                show_error_message("خطأ", "يرجى اختيار عامل أولاً")
                return

            # الحصول على معرف العامل من الجدول
            employee_id_item = self.employees_table.item(current_row, 0)
            if not employee_id_item:
                show_error_message("خطأ", "لا يمكن تحديد العامل المحدد")
                return

            # استخراج رقم العامل من النص (إزالة الأيقونات)
            employee_id_text = employee_id_item.text()
            employee_id_match = re.search(r'\d+', employee_id_text)
            if not employee_id_match:
                show_error_message("خطأ", "لا يمكن العثور على رقم العامل")
                return
            employee_id = int(employee_id_match.group())

            # البحث عن العامل في قاعدة البيانات
            employee = self.session.query(Employee).filter_by(id=employee_id).first()
            if not employee:
                show_error_message("خطأ", "لم يتم العثور على العامل في قاعدة البيانات")
                return

            # استخدام نفس نظام العملاء والموردين
            dialog = EmployeeBalanceAdjustmentDialog(self, employee)
            if dialog.exec_() == QDialog.Accepted:
                data = dialog.get_data()
                if data:
                    # تحديث المبلغ المستحق
                    success, new_balance = update_employee_balance(
                        self.session,
                        employee_id,
                        data['amount'],
                        data['operation']
                    )

                    if success:
                        show_info_message(
                            "تم",
                            f"تم تحديث المبلغ المستحق للعامل {employee.name} بنجاح.\nالمبلغ الجديد: {new_balance:.2f}"
                        )
                        self.refresh_data()
                    else:
                        show_error_message("خطأ", "حدث خطأ أثناء تحديث المبلغ المستحق")

        except Exception as e:
            self.session.rollback()
            print(f"❌ خطأ في تعديل الرصيد: {str(e)}")
            show_error_message("خطأ", f"حدث خطأ في تعديل الرصيد: {str(e)}")

    def show_advanced_employee_details(self):
        """عرض التفاصيل المتقدمة للعامل - مطابق للموردين"""
        try:
            current_row = self.employees_table.currentRow()
            if current_row < 0:
                show_error_message("خطأ", "يرجى اختيار عامل أولاً")
                return

            # الحصول على معرف العامل
            employee_id_item = self.employees_table.item(current_row, 0)
            if not employee_id_item:
                show_error_message("خطأ", "لا يمكن تحديد العامل المحدد")
                return

            # استخراج رقم العامل من النص
            employee_id_text = employee_id_item.text()
            employee_id = int(''.join(filter(str.isdigit, employee_id_text)))

            # البحث عن العامل في قاعدة البيانات
            employee = self.session.query(Employee).filter_by(id=employee_id).first()
            if not employee:
                show_error_message("خطأ", "لم يتم العثور على العامل في قاعدة البيانات")
                return

            # الحصول على الرصيد الحالي من الجدول
            balance_item = self.employees_table.item(current_row, 5)
            current_balance = 0
            if balance_item:
                try:
                    current_balance = float(balance_item.text().replace(',', ''))
                except:
                    current_balance = 0

            # تحديد حالة الرصيد
            if current_balance > 10000:
                balance_status = "💎 رصيد ممتاز"
                balance_color = "أخضر ذهبي"
            elif current_balance > 0:
                balance_status = "✅ رصيد موجب"
                balance_color = "أخضر"
            elif current_balance < -5000:
                balance_status = "🚨 رصيد مدين عالي"
                balance_color = "أحمر قوي"
            elif current_balance < 0:
                balance_status = "⚠️ رصيد مدين"
                balance_color = "برتقالي"
            else:
                balance_status = "➖ رصيد صفر"
                balance_color = "رمادي"

            # إنشاء نص التفاصيل المتقدمة
📊 التفاصيل المتقدمة للعامل

👤 اسم العامل: {employee.name}
🆔 رقم العامل: {employee.id}

💵 الرصيد الحالي: {current_balance:,.2f} ريال سعودي
📊 حالة الرصيد: {balance_status}
🎨 اللون المميز: {balance_color}

📈 تحليل الرصيد:
• المبلغ المطلق: {abs(current_balance):,.2f} ريال
• نوع الحساب: {"دائن" if current_balance >= 0 else "مدين"}
• التصنيف: {"عالي القيمة" if abs(current_balance) > 10000 else "متوسط القيمة" if abs(current_balance) > 1000 else "منخفض القيمة"}

💼 معلومات العمل:
• المنصب: {employee.position or 'غير محدد'}
• تاريخ التوظيف: {employee.hire_date if hasattr(employee, 'hire_date') and employee.hire_date else 'غير محدد'}

📞 معلومات الاتصال:
• الهاتف: {employee.phone or 'غير متوفر'}
• الإيميل: {employee.email if hasattr(employee, 'email') and employee.email else 'غير متوفر'}
• العنوان: {employee.address if hasattr(employee, 'address') and employee.address else 'غير متوفر'}

📝 ملاحظات إضافية:
{employee.notes or 'لا توجد ملاحظات'}

📅 تاريخ آخر تحديث: {employee.updated_at if hasattr(employee, 'updated_at') and employee.updated_at else 'غير محدد'}
            """

            show_info_message("📊 التفاصيل المتقدمة", advanced_info)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في عرض التفاصيل المتقدمة: {str(e)}")

    def export_to_pdf(self):
        try:

            # اختيار مكان الحفظ
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ ملف PDF",
                f"عمال_{QDate.currentDate().toString('yyyy-MM-dd')}.pdf",
                "PDF Files (*.pdf)"
            )

            if filename:
                # جمع البيانات من الجدول
                data = []
                headers = []

                # الحصول على عناوين الأعمدة
                for col in range(self.employees_table.columnCount()):
                    headers.append(self.employees_table.horizontalHeaderItem(col).text())

                # جمع البيانات من الجدول
                for row in range(self.employees_table.rowCount()):
                    row_data = []
                    for col in range(self.employees_table.columnCount()):
                        item = self.employees_table.item(row, col)
                        row_data.append(item.text() if item else "")
                    data.append(row_data)

                # إنشاء تقرير PDF بسيط

                # محاولة استخدام reportlab إذا كانت متاحة
                try:

                    # إنشاء المستند
                    doc = SimpleDocTemplate(filename, pagesize=A4)
                    story = []

                    # إضافة العنوان
                    styles = getSampleStyleSheet()
                    title_style = ParagraphStyle(
                        'CustomTitle',
                        parent=styles['Heading1'],
                        fontSize=18,
                        spaceAfter=30,
                        alignment=1  # توسيط
                    )

                    title = Paragraph(f"تقرير العمال - {datetime.now().strftime('%Y-%m-%d')}", title_style)
                    story.append(title)
                    story.append(Spacer(1, 12))

                    # إنشاء الجدول
                    table_data = [headers] + data
                    table = Table(table_data)

                    # تنسيق الجدول
                    table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('FONTSIZE', (0, 0), (-1, 0), 10),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))

                    story.append(table)

                    # بناء المستند
                    doc.build(story)

                    show_info_message("تم", f"تم تصدير {len(data)} عامل إلى PDF بنجاح\n{filename}")

                except ImportError:
                    # إذا لم تكن reportlab متاحة، استخدم طريقة بديلة
                    show_info_message("تم", f"سيتم تصدير {self.employees_table.rowCount()} عامل إلى PDF\n(يتطلب تثبيت مكتبة reportlab)")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير إلى PDF: {str(e)}")

    def show_performance_statistics(self):
        """عرض إحصائيات الأداء المتقدمة - مطابق للموردين"""
        try:

            # إحصائيات الأداء المتقدمة
            total_employees = self.session.query(Employee).count()

            # العمال النشطين في آخر 30 يوم
            thirty_days_ago = datetime.now() - timedelta(days=30)

            # حساب معدل الأداء

📊 الإحصائيات العامة:
• إجمالي عدد العمال: {total_employees}
• تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}

📈 مؤشرات الأداء:
• معدل النمو الشهري: قيد الحساب
• معدل الاحتفاظ بالعمال: قيد الحساب
• متوسط فترة العمل: قيد الحساب

💡 توصيات التحسين:
• مراجعة دورية لأداء العمال
• تطوير برامج التدريب
• تحسين بيئة العمل
• وضع نظام حوافز فعال

📋 ملاحظات:
• البيانات محدثة حتى تاريخ اليوم
• يُنصح بمراجعة هذا التقرير شهرياً
• لمزيد من التفاصيل، راجع التقارير المفصلة"""

            show_info_message("⚡ إحصائيات الأداء", performance_stats)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في عرض إحصائيات الأداء: {str(e)}")

    def filter_by_balance(self, balance_type):
        try:
            for row in range(self.employees_table.rowCount()):
                balance_item = self.employees_table.item(row, 5)  # عمود الرصيد
                show_row = False

                if balance_item:
                    try:
                        balance_text = balance_item.text().replace(',', '').replace('ر.س', '').strip()
                        balance = float(balance_text) if balance_text else 0.0

                        if balance_type == 'positive':
                            show_row = balance > 0
                        elif balance_type == 'negative':
                            show_row = balance < 0
                        elif balance_type == 'zero':
                            show_row = balance == 0
                        else:  # all
                            show_row = True

                    except ValueError:
                        show_row = balance_type == 'all'
                else:
                    show_row = balance_type == 'all'

                self.employees_table.setRowHidden(row, not show_row)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فلترة الرصيد: {str(e)}")

class DailyWagesWidget(QWidget):
    """تبويب الأجور اليومية"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()
        self.refresh_data()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # إضافة العنوان الرئيسي مطابق تماماً لبيانات العمال
        title_label = QLabel("💰 إدارة الأجور اليومية المتطورة - نظام شامل ومتقدم لإدارة الأجور اليومية مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e40af,
                    stop:0.2 #3b82f6,
                    stop:0.4 #6366f1,
                    stop:0.6 #8b5cf6,
                    stop:0.8 #a855f7,
                    stop:1 #c084fc);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;

            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب (صف واحد) مطابق لبيانات العمال
        top_frame = QFrame()
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة) مطابق لبيانات العمال
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي مطابق لبيانات العمال
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة ومتوسطة في الإطار - موحدة مع العملاء والموردين
        search_label = QLabel("🔍 بحث:")
            QLabel {
                color: #1f2937;
                font-size: 13px;
                font-weight: bold;
                padding: 2px 6px;
                background: transparent;
                border: none;
                min-width: 75px;
            }
        """)

        # حقل البحث محسن ومتوسط في الإطار
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث عن عامل...")
        self.search_edit.textChanged.connect(self.filter_wages)
            QLineEdit {
                padding: 6px 10px;
                border: 3px solid #000000;
                border-radius: 6px;
                font-size: 13px;
                font-weight: bold;
                background: white;
                color: #1f2937;
                min-width: 180px;
                max-width: 280px;
                min-height: 18px;
                max-height: 24px;
            }
            QLineEdit:focus {
                border: 3px solid #3b82f6;
                background: #f0f9ff;
            }
        """)

        # زر البحث متوسط في الإطار
        search_button = QPushButton("🔍")
        self.style_advanced_button(search_button, 'info')
        search_button.clicked.connect(self.filter_wages)
        search_button.setMinimumWidth(40)
        search_button.setMaximumHeight(30)

        # فاصل مرئي متوسط في الإطار
        separator = QLabel("|")
            QLabel {
                color: #6b7280;
                font-size: 18px;
                font-weight: bold;
                padding: 0 6px;
            }
        """)

        # أزرار التصفية متوسطة في الإطار
        filter_all_button = QPushButton("📋 الكل")
        self.style_advanced_button(filter_all_button, 'gray')
        filter_all_button.clicked.connect(lambda: self.filter_by_period('all'))
        filter_all_button.setMaximumHeight(30)

        filter_today_button = QPushButton("📅 اليوم")
        self.style_advanced_button(filter_today_button, 'emerald')
        filter_today_button.clicked.connect(lambda: self.filter_by_period('today'))
        filter_today_button.setMaximumHeight(30)

        filter_week_button = QPushButton("📊 الأسبوع")
        self.style_advanced_button(filter_week_button, 'primary')
        filter_week_button.clicked.connect(lambda: self.filter_by_period('week'))
        filter_week_button.setMaximumHeight(30)

        filter_month_button = QPushButton("📈 الشهر")
        self.style_advanced_button(filter_month_button, 'warning')
        filter_month_button.clicked.connect(lambda: self.filter_by_period('month'))
        filter_month_button.setMaximumHeight(30)

        # إضافة جميع العناصر للصف الواحد مطابق تماماً لباقي الأقسام
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(separator, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_all_button, 1, Qt.AlignVCenter)
        search_layout.addWidget(filter_today_button, 1, Qt.AlignVCenter)
        search_layout.addWidget(filter_week_button, 1, Qt.AlignVCenter)
        search_layout.addWidget(filter_month_button, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي
        top_frame.setLayout(top_container)
        main_layout.addWidget(top_frame)

        # إنشاء جدول الأجور اليومية مطابق لباقي الجداول في البرنامج
        self.wages_table = QTableWidget()
        self.wages_table.setColumnCount(6)
        self.wages_table.setHorizontalHeaderLabels([
            "الرقم", "اسم العامل", "عدد أيام العمل", "المبلغ اليومي",
            "السلف", "صافي المبلغ"
        ])

        # تحسين عرض الأعمدة مثل باقي الجداول
        header = self.wages_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # الرقم
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم العامل
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # عدد أيام العمل
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # المبلغ اليومي
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # السلف
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # صافي المبلغ

        # تحديد عرض الأعمدة الثابتة
        self.wages_table.setColumnWidth(0, 80)   # الرقم
        self.wages_table.setColumnWidth(2, 120)  # عدد أيام العمل
        self.wages_table.setColumnWidth(3, 120)  # المبلغ اليومي
        self.wages_table.setColumnWidth(4, 100)  # السلف
        self.wages_table.setColumnWidth(5, 120)  # صافي المبلغ

        self.wages_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.wages_table.setSelectionMode(QTableWidget.SingleSelection)
        self.wages_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.wages_table.setAlternatingRowColors(True)

        # إضافة الإطار الأسود للجدول مطابق للموردين والعمال
        self.apply_black_frame_to_wages_table()

        # تحسين تصميم الجدول مطابق لباقي الجداول
            QTableWidget {
                background-color: #ffffff;
                border: 3px solid #000000;
                border-radius: 8px;
                gridline-color: #e5e7eb;
                font-size: 13px;
                font-weight: bold;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e5e7eb;
                color: #000000;
                font-weight: bold;
            }
            QTableWidget::item:selected {
                background-color: #3b82f6;
                color: white;
            }
            QTableWidget::item:hover {
                background-color: #f3f4f6;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4f46e5, stop:1 #3730a3);
                color: white;
                padding: 8px;
                border: 1px solid #3730a3;
                font-weight: bold;
                font-size: 14px;
            }
        """)

        # إنشاء إطار الأزرار الأسود مطابق تماماً للموردين والعمال
        actions_frame = QFrame()
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        actions_layout.setSpacing(4)  # مسافة أكبر بين الأزرار لتوزيع أفضل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # تطبيق التخطيط على الإطار
        actions_frame.setLayout(bottom_container)

        # الأزرار الأساسية مثل المخزون والمبيعات والمشتريات
        self.add_wage_button = QPushButton("➕ إضافة يومية")
        self.style_advanced_button(self.add_wage_button, 'emerald')
        self.add_wage_button.clicked.connect(self.add_daily_wage)
        self.add_wage_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.edit_wage_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_wage_button, 'primary')
        self.edit_wage_button.clicked.connect(self.edit_daily_wage)
        self.edit_wage_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_wage_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_wage_button, 'danger')
        self.delete_wage_button.clicked.connect(self.delete_daily_wage)
        self.delete_wage_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.refresh_wages_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_wages_button, 'modern_teal')
        self.refresh_wages_button.clicked.connect(self.refresh_data)
        self.refresh_wages_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.view_details_button = QPushButton("👁️ عرض التفاصيل")
        self.style_advanced_button(self.view_details_button, 'indigo')
        self.view_details_button.clicked.connect(self.view_wage_details)
        self.view_details_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التصدير مطابق لباقي البرنامج
        self.export_wages_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_wages_button, 'info', has_menu=True)
        self.export_wages_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة للتصدير مع النمط الموحد
        export_menu = QMenu(self)
        export_menu.setStyleSheet(UnifiedStyles.get_menu_style('info', 'normal'))

        # إضافة خيارات التصدير
        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(self.export_wages_to_excel)
        export_menu.addAction(excel_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(self.export_wages_to_csv)
        export_menu.addAction(csv_action)

        json_action = QAction("🔗 تصدير إلى JSON", self)
        json_action.triggered.connect(self.export_wages_to_json)
        export_menu.addAction(json_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(self.export_wages_to_pdf)
        export_menu.addAction(pdf_action)

        # ربط القائمة بالزر
        self.export_wages_button.setMenu(export_menu)

        # زر الإحصائيات مطابق لباقي البرنامج
        self.statistics_button = QPushButton("📊 الإحصائيات ▼")
        self.style_advanced_button(self.statistics_button, 'rose', has_menu=True)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة للإحصائيات مع النمط الموحد
        statistics_menu = QMenu(self)
        statistics_menu.setStyleSheet(UnifiedStyles.get_menu_style('rose', 'normal'))

        # إضافة خيارات الإحصائيات
        basic_stats_action = QAction("📈 إحصائيات أساسية", self)
        basic_stats_action.triggered.connect(self.show_wages_statistics)
        statistics_menu.addAction(basic_stats_action)

        detailed_stats_action = QAction("📊 إحصائيات مفصلة", self)
        detailed_stats_action.triggered.connect(self.show_detailed_wages_statistics)
        statistics_menu.addAction(detailed_stats_action)

        performance_analysis_action = QAction("💰 تحليل الأداء", self)
        performance_analysis_action.triggered.connect(self.show_performance_analysis)
        statistics_menu.addAction(performance_analysis_action)

        monthly_report_action = QAction("📅 تقرير شهري", self)
        monthly_report_action.triggered.connect(self.show_wages_monthly_report)
        statistics_menu.addAction(monthly_report_action)

        # ربط القائمة بالزر
        self.statistics_button.setMenu(statistics_menu)

        # إضافة الأزرار بالترتيب المطلوب مع الفواصل الأنيقة مطابق لباقي الأقسام

        # المجموعة الأولى - العمليات الأساسية مع التوسيط العمودي
        actions_layout.addWidget(self.add_wage_button, 0, Qt.AlignVCenter)           # 1. ➕ إضافة يومية
        actions_layout.addWidget(self.edit_wage_button, 0, Qt.AlignVCenter)          # 2. ✏️ تعديل
        actions_layout.addWidget(self.delete_wage_button, 0, Qt.AlignVCenter)        # 3. 🗑️ حذف
        actions_layout.addWidget(self.refresh_wages_button, 0, Qt.AlignVCenter)      # 4. 🔄 تحديث

        # فاصل راقي 1
        actions_layout.addWidget(self.create_elegant_separator(), 0, Qt.AlignVCenter)

        # المجموعة الثانية - العمليات المتقدمة مع التوسيط العمودي
        actions_layout.addWidget(self.view_details_button, 0, Qt.AlignVCenter)       # 5. 👁️ عرض التفاصيل
        actions_layout.addWidget(self.export_wages_button, 0, Qt.AlignVCenter)       # 6. 📤 تصدير
        actions_layout.addWidget(self.statistics_button, 0, Qt.AlignVCenter)         # 7. 📊 الإحصائيات

        # إضافة ملخص الأجور مطابق لباقي البرنامج
        self.total_wages_label = QLabel("إجمالي العمال: 0 | أيام العمل: 0 | المبالغ: 0.00 ج.م | السلف: 0.00 ج.م")
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                padding: 8px 16px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f59e0b,
                    stop:0.5 #d97706,
                    stop:1 #b45309);
                border: 3px solid #92400e;
                border-radius: 12px;
                min-height: 34px;
                max-height: 38px;
            }
        """)
        self.total_wages_label.setAlignment(Qt.AlignCenter)

        actions_layout.addWidget(self.total_wages_label)

        # تجميع التخطيط النهائي
        main_layout.addWidget(self.wages_table)
        main_layout.addWidget(actions_frame)  # إضافة الإطار مطابق للموردين والعمال

        self.setLayout(main_layout)

    def create_elegant_separator(self):
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("""
            QFrame {
                color: #7f8c8d;
                background-color: #7f8c8d;
                border: none;
                width: 2px;
                margin: 8px 4px;
            }
        separator.setFixedWidth(2)
        separator.setMinimumHeight(40)
        separator.setMaximumHeight(50)
        return separator

    def style_advanced_button(self, button, button_type='primary', has_menu=False):
        """تطبيق التصميم المتطور على الأزرار - مطابق لباقي الأقسام"""
        try:
            # استخدام نفس النظام من UnifiedStyles
            button.setStyleSheet(UnifiedStyles.get_button_style(button_type, "normal"))

            # تطبيق الأحجام الموحدة
            button.setMinimumHeight(45)
            button.setMaximumHeight(55)
            button.setMinimumWidth(120)

            print(f"✅ تم تطبيق التصميم المتطور على الزر: {button.text()} - نوع: {button_type}")

        except Exception as e:
            print(f"❌ خطأ في تطبيق التصميم على الزر: {str(e)}")

    def apply_black_frame_to_wages_table(self):
        try:
            # الحصول على التنسيق الحالي
            current_style = self.wages_table.styleSheet()

            # إضافة الإطار الأسود فقط
            black_border_addition = """
                QTableWidget {
                    border: 3px solid #000000 !important;
                }

            # دمج التنسيق الحالي مع الإطار الأسود
            combined_style = current_style + black_border_addition
            self.wages_table.setStyleSheet(combined_style)

            print("✅ تم تطبيق الإطار الأسود على جدول الأجور اليومية")

        except Exception as e:
            print(f"❌ خطأ في تطبيق الإطار الأسود: {str(e)}")

    def refresh_data(self):
        """تحديث بيانات الأجور اليومية في الجدول"""
        try:
            # الحصول على جميع الأجور اليومية من قاعدة البيانات
            daily_wages = self.session.query(DailyWage).join(Employee).all()
            self.populate_table(daily_wages)
            self.update_summary(daily_wages)
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تحميل بيانات الأجور اليومية: {str(e)}")

    def populate_table(self, daily_wages):
        self.wages_table.setRowCount(0)

        for row, wage in enumerate(daily_wages):
            self.wages_table.insertRow(row)
            self.wages_table.setItem(row, 0, QTableWidgetItem(str(wage.id)))
            self.wages_table.setItem(row, 1, QTableWidgetItem(wage.employee.name if wage.employee else ""))
            self.wages_table.setItem(row, 2, QTableWidgetItem(str(int(wage.work_days))))
            self.wages_table.setItem(row, 3, QTableWidgetItem(f"{wage.daily_amount:.0f}"))
            self.wages_table.setItem(row, 4, QTableWidgetItem(f"{wage.advance:.0f}"))
            self.wages_table.setItem(row, 5, QTableWidgetItem(f"{wage.net_amount:.0f}"))

    def update_summary(self, daily_wages):
        """تحديث ملخص الأجور اليومية"""
        total_workers = len(set(wage.employee_id for wage in daily_wages if wage.employee_id))
        total_days = sum(wage.work_days for wage in daily_wages)
        total_amount = sum(wage.total_amount for wage in daily_wages)
        total_advances = sum(wage.advance for wage in daily_wages)

        self.total_wages_label.setText(f"إجمالي العمال: {total_workers} | أيام العمل: {int(total_days)} | المبالغ: {total_amount:.0f} ج.م | السلف: {total_advances:.0f} ج.م")

    def filter_wages(self):
        search_text = self.search_edit.text().strip().lower()
        month = self.month_filter.currentData()

        try:
            query = self.session.query(DailyWage).join(Employee)

            # تطبيق تصفية النص
            if search_text:
                query = query.filter(Employee.name.like(f"%{search_text}%"))

            # تطبيق تصفية الشهر
            if month:
                query = query.filter(DailyWage.wage_date.month == month)

            daily_wages = query.all()
            self.populate_table(daily_wages)
            self.update_summary(daily_wages)
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء البحث: {str(e)}")

    def filter_by_period(self, period_type='all'):
        """تصفية الأجور حسب الفترة الزمنية"""
        try:

            now = datetime.now()

            if period_type == 'today':
                # أجور اليوم
                start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
                end_date = now.replace(hour=23, minute=59, second=59, microsecond=999999)
                wages = self.session.query(DailyWage).filter(
                    DailyWage.wage_date >= start_date.date(),
                    DailyWage.wage_date <= end_date.date()
                ).all()
            elif period_type == 'week':
                # أجور هذا الأسبوع
                start_of_week = now - timedelta(days=now.weekday())
                start_date = start_of_week.replace(hour=0, minute=0, second=0, microsecond=0)
                wages = self.session.query(DailyWage).filter(
                    DailyWage.wage_date >= start_date.date()
                ).all()
            elif period_type == 'month':
                # أجور هذا الشهر
                start_of_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
                wages = self.session.query(DailyWage).filter(
                    DailyWage.wage_date >= start_of_month.date()
                ).all()
            else:
                # جميع الأجور
                wages = self.session.query(DailyWage).all()

            self.populate_table(wages)
            self.update_summary(wages)
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء التصفية: {str(e)}")

    def add_daily_wage(self):
        dialog = DailyWageDialog(self, session=self.session)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                try:
                    # إنشاء يومية جديدة
                    daily_wage = DailyWage(
                        employee_id=data['employee_id'],
                        daily_amount=data['daily_amount'],
                        work_days=data['work_days'],
                        total_amount=data['total_amount'],
                        advance=data['advance'],
                        net_amount=data['net_amount'],
                        wage_date=data['wage_date']
                    )

                    self.session.add(daily_wage)
                    self.session.commit()

                    show_info_message("نجح", "تم إضافة اليومية بنجاح")
                    self.refresh_data()

                except Exception as e:
                    self.session.rollback()
                    show_error_message("خطأ", f"حدث خطأ أثناء إضافة اليومية: {str(e)}")

    def edit_daily_wage(self):
        """تعديل يومية"""
        current_row = self.wages_table.currentRow()
        if current_row < 0:
            show_error_message("خطأ", "الرجاء اختيار يومية من الجدول")
            return

        wage_id = int(self.wages_table.item(current_row, 0).text())
        daily_wage = self.session.query(DailyWage).get(wage_id)

        if not daily_wage:
            show_error_message("خطأ", "اليومية غير موجودة")
            return

        dialog = DailyWageDialog(self, daily_wage=daily_wage, session=self.session)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                try:
                    # تحديث بيانات اليومية
                    daily_wage.employee_id = data['employee_id']
                    daily_wage.daily_amount = data['daily_amount']
                    daily_wage.work_days = data['work_days']
                    daily_wage.total_amount = data['total_amount']
                    daily_wage.advance = data['advance']
                    daily_wage.net_amount = data['net_amount']
                    daily_wage.wage_date = data['wage_date']

                    self.session.commit()

                    show_info_message("نجح", "تم تحديث اليومية بنجاح")
                    self.refresh_data()

                except Exception as e:
                    self.session.rollback()
                    show_error_message("خطأ", f"حدث خطأ أثناء تحديث اليومية: {str(e)}")

    def delete_daily_wage(self):
        current_row = self.wages_table.currentRow()
        if current_row < 0:
            show_error_message("خطأ", "الرجاء اختيار يومية من الجدول")
            return

        wage_id = int(self.wages_table.item(current_row, 0).text())
        daily_wage = self.session.query(DailyWage).get(wage_id)

        if not daily_wage:
            show_error_message("خطأ", "اليومية غير موجودة")
            return

        employee_name = daily_wage.employee.name if daily_wage.employee else "غير معروف"

        if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف يومية العامل '{employee_name}'؟"):
            try:
                self.session.delete(daily_wage)
                self.session.commit()

                show_info_message("نجح", "تم حذف اليومية بنجاح")
                self.refresh_data()

            except Exception as e:
                self.session.rollback()
                show_error_message("خطأ", f"حدث خطأ أثناء حذف اليومية: {str(e)}")

    def view_wage_details(self):
        """عرض تفاصيل اليومية"""
        current_row = self.wages_table.currentRow()
        if current_row < 0:
            show_error_message("خطأ", "الرجاء اختيار يومية من الجدول")
            return

        wage_id = int(self.wages_table.item(current_row, 0).text())
        daily_wage = self.session.query(DailyWage).get(wage_id)

        if not daily_wage:
            show_error_message("خطأ", "اليومية غير موجودة")
            return

        # عرض تفاصيل اليومية في نافذة للقراءة فقط
        dialog = DailyWageDialog(self, daily_wage=daily_wage, session=self.session)
        dialog.setWindowTitle("تفاصيل اليومية")

        # جعل الحقول للقراءة فقط
        dialog.employee_combo.setEnabled(False)
        dialog.daily_amount_spin.setReadOnly(True)
        dialog.work_days_spin.setReadOnly(True)
        dialog.advance_spin.setReadOnly(True)
        dialog.wage_date_edit.setReadOnly(True)

        dialog.exec_()

    def export_wages_to_excel(self):
        try:

            # الحصول على البيانات
            daily_wages = self.session.query(DailyWage).join(Employee).all()

            if not daily_wages:
                show_info_message("تنبيه", "لا توجد بيانات للتصدير")
                return

            # تحضير البيانات للتصدير
            data = []
            for wage in daily_wages:
                data.append({
                    'اسم العامل': wage.employee.name,
                    'التاريخ': wage.wage_date.strftime('%Y-%m-%d') if wage.wage_date else '',
                    'المبلغ اليومي': wage.daily_amount,
                    'أيام العمل': wage.work_days,
                    'إجمالي المبلغ': wage.total_amount,
                    'السلفة': wage.advance,
                    'صافي المبلغ': wage.net_amount
                })

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ ملف Excel",
                f"الأجور_اليومية_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel Files (*.xlsx)"
            )

            if file_path:
                # إنشاء DataFrame وحفظه
                df = pd.DataFrame(data)
                df.to_excel(file_path, index=False, engine='openpyxl')
                show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")

        except ImportError:
            show_error_message("خطأ", "يرجى تثبيت pandas و openpyxl لتصدير Excel:\npip install pandas openpyxl")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

    def export_wages_to_csv(self):
        """تصدير بيانات الأجور اليومية إلى CSV"""
        try:

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف CSV", "الأجور_اليومية.csv", "ملفات CSV (*.csv)")
            if not file_path:
                return

            # جمع البيانات من الجدول
            data = []
            headers = []

            # الحصول على عناوين الأعمدة
            for col in range(self.wages_table.columnCount()):
                headers.append(self.wages_table.horizontalHeaderItem(col).text())

            # جمع البيانات من الجدول
            for row in range(self.wages_table.rowCount()):
                row_data = []
                for col in range(self.wages_table.columnCount()):
                    item = self.wages_table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)

            # كتابة البيانات إلى ملف CSV
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(headers)
                writer.writerows(data)

            show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

    def export_wages_to_json(self):
        try:

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف JSON", "الأجور_اليومية.json", "ملفات JSON (*.json)")
            if not file_path:
                return

            # جمع البيانات
            wages = self.session.query(DailyWage).join(Employee).all()
            data = {
                'export_date': datetime.now().isoformat(),
                'total_wages': len(wages),
                'wages': []
            }

            for wage in wages:
                wage_data = {
                    'id': wage.id,
                    'employee_name': wage.employee.name if wage.employee else '',
                    'work_days': wage.work_days,
                    'daily_amount': wage.daily_amount,
                    'total_amount': wage.total_amount,
                    'advance': wage.advance,
                    'net_amount': wage.net_amount,
                    'wage_date': wage.wage_date.isoformat() if wage.wage_date else None
                }
                data['wages'].append(wage_data)

            # حفظ البيانات
            with open(file_path, 'w', encoding='utf-8') as jsonfile:
                json.dump(data, jsonfile, ensure_ascii=False, indent=2)

            show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

    def export_wages_to_pdf(self):
        """تصدير بيانات الأجور اليومية إلى PDF"""
        try:

            # الحصول على البيانات
            daily_wages = self.session.query(DailyWage).join(Employee).all()

            if not daily_wages:
                show_info_message("تنبيه", "لا توجد بيانات للتصدير")
                return

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ ملف PDF",
                f"الأجور_اليومية_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "PDF Files (*.pdf)"
            )

            if file_path:
                # إنشاء محتوى HTML للتقرير
                <html dir="rtl">
                <head>
                    <meta charset="utf-8">
                    <style>
                        body {{ font-family: Arial, sans-serif; direction: rtl; }}
                        table {{ border-collapse: collapse; width: 100%; }}
                        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                        th {{ background-color: #f2f2f2; }}
                        h1 {{ text-align: center; color: #2c3e50; }}
                    </style>
                </head>
                <body>
                    <h1>تقرير الأجور اليومية</h1>
                    <p>تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    <table>
                        <tr>
                            <th>اسم العامل</th>
                            <th>التاريخ</th>
                            <th>المبلغ اليومي</th>
                            <th>أيام العمل</th>
                            <th>إجمالي المبلغ</th>
                            <th>السلفة</th>
                            <th>صافي المبلغ</th>
                        </tr>
                """

                total_wages = 0
                total_advances = 0
                total_net = 0

                for wage in daily_wages:
                    total_wages += wage.total_amount
                    total_advances += wage.advance
                    total_net += wage.net_amount
                        <tr>
                            <td>{wage.employee.name}</td>
                            <td>{wage.wage_date.strftime('%Y-%m-%d') if wage.wage_date else ''}</td>
                            <td>{wage.daily_amount:.2f}</td>
                            <td>{wage.work_days:.1f}</td>
                            <td>{wage.total_amount:.2f}</td>
                            <td>{wage.advance:.2f}</td>
                            <td>{wage.net_amount:.2f}</td>
                        </tr>
                    """

                    </table>
                    <br>
                    <h3>الملخص:</h3>
                    <p>إجمالي الأجور: {total_wages:.2f} ج.م</p>
                    <p>إجمالي السلف: {total_advances:.2f} ج.م</p>
                    <p>صافي الأجور: {total_net:.2f} ج.م</p>
                </body>
                </html>
                """

                # إنشاء وطباعة المستند
                document = QTextDocument()
                document.setHtml(html_content)

                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)

                document.print_(printer)
                show_info_message("تم", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")

    def show_wages_statistics(self):
        try:

            # إجمالي الأجور
            total_wages = self.session.query(DailyWage).count()

            # إجمالي العمال النشطين
            active_workers = self.session.query(DailyWage.employee_id).distinct().count()

            # إجمالي أيام العمل
            total_work_days = self.session.query(func.sum(DailyWage.work_days)).scalar() or 0

            # إجمالي المبالغ
            total_amount = self.session.query(func.sum(DailyWage.total_amount)).scalar() or 0
            total_advances = self.session.query(func.sum(DailyWage.advance)).scalar() or 0
            net_total = self.session.query(func.sum(DailyWage.net_amount)).scalar() or 0

            # إحصائيات الشهر الحالي
            current_month_start = datetime.now().replace(day=1)
            monthly_wages = self.session.query(func.sum(DailyWage.net_amount)).filter(
                DailyWage.wage_date >= current_month_start.date()
            ).scalar() or 0

            stats_text = f"""📊 إحصائيات الأجور اليومية:

📈 الإحصائيات العامة:
• إجمالي عدد اليوميات: {total_wages}
• عدد العمال النشطين: {active_workers}
• إجمالي أيام العمل: {total_work_days:.1f} يوم
• متوسط أيام العمل للعامل: {(total_work_days / max(active_workers, 1)):.1f} يوم

💰 الإحصائيات المالية:
• إجمالي المبالغ: {total_amount:.2f} ج.م
• إجمالي السُلف: {total_advances:.2f} ج.م
• صافي المبالغ: {net_total:.2f} ج.م
• أجور الشهر الحالي: {monthly_wages:.2f} ج.م

📊 المتوسطات:
• متوسط الأجر اليومي: {(total_amount / max(total_work_days, 1)):.2f} ج.م
• متوسط الأجر للعامل: {(net_total / max(active_workers, 1)):.2f} ج.م

            show_info_message("إحصائيات الأجور", stats_text)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء حساب الإحصائيات: {str(e)}")

    def show_detailed_wages_statistics(self):
        """عرض الإحصائيات المفصلة للأجور اليومية"""
        try:

            # أكثر العمال نشاطاً
            top_workers = self.session.query(
                Employee.name,
                func.count(DailyWage.id).label('wage_count'),
                func.sum(DailyWage.work_days).label('total_days'),
                func.sum(DailyWage.net_amount).label('total_earnings')
            ).join(DailyWage).group_by(Employee.id).order_by(func.sum(DailyWage.net_amount).desc()).limit(5).all()

            # إحصائيات شهرية
            monthly_stats = self.session.query(
                func.extract('month', DailyWage.wage_date).label('month'),
                func.count(DailyWage.id).label('wage_count'),
                func.sum(DailyWage.net_amount).label('total_amount')
            ).group_by(func.extract('month', DailyWage.wage_date)).all()


🏆 أكثر العمال نشاطاً:"""

            for i, worker in enumerate(top_workers[:3], 1):
{i}. {worker.name}
    • عدد اليوميات: {worker.wage_count}
    • إجمالي أيام العمل: {worker.total_days:.1f}
    • إجمالي الأرباح: {worker.total_earnings:.2f} ج.م"""


📅 الإحصائيات الشهرية:"""
            months = ["", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                    "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]

            for stat in monthly_stats[:6]:
                month_name = months[int(stat.month)] if stat.month and stat.month <= 12 else f"الشهر {stat.month}"
• {month_name}: {stat.wage_count} يومية - {stat.total_amount:.2f} ج.م"""

            show_info_message("إحصائيات مفصلة", detailed_stats)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء حساب الإحصائيات المفصلة: {str(e)}")

    def show_performance_analysis(self):
        try:

            # تحليل الأداء
            current_month = datetime.now().replace(day=1)
            last_month = (current_month - timedelta(days=1)).replace(day=1)

            # أداء الشهر الحالي
            current_month_wages = self.session.query(func.sum(DailyWage.net_amount)).filter(
                DailyWage.wage_date >= current_month.date()
            ).scalar() or 0

            current_month_days = self.session.query(func.sum(DailyWage.work_days)).filter(
                DailyWage.wage_date >= current_month.date()
            ).scalar() or 0

            # أداء الشهر الماضي
            last_month_wages = self.session.query(func.sum(DailyWage.net_amount)).filter(
                DailyWage.wage_date >= last_month.date(),
                DailyWage.wage_date < current_month.date()
            ).scalar() or 0

            last_month_days = self.session.query(func.sum(DailyWage.work_days)).filter(
                DailyWage.wage_date >= last_month.date(),
                DailyWage.wage_date < current_month.date()
            ).scalar() or 0

            # حساب النسب
            wages_change = ((current_month_wages - last_month_wages) / max(last_month_wages, 1)) * 100
            days_change = ((current_month_days - last_month_days) / max(last_month_days, 1)) * 100

            analysis_text = f"""💰 تحليل الأداء:

📊 مقارنة الأداء الشهري:
• أجور الشهر الحالي: {current_month_wages:.2f} ج.م
• أجور الشهر الماضي: {last_month_wages:.2f} ج.م
• التغيير في الأجور: {wages_change:+.1f}%

📈 مقارنة أيام العمل:
• أيام العمل الحالية: {current_month_days:.1f} يوم
• أيام العمل الماضية: {last_month_days:.1f} يوم
• التغيير في أيام العمل: {days_change:+.1f}%

📝 تحليل الاتجاه:
• {'تحسن في الأداء' if wages_change > 0 else 'انخفاض في الأداء' if wages_change < 0 else 'أداء مستقر'}
• {'زيادة في النشاط' if days_change > 0 else 'انخفاض في النشاط' if days_change < 0 else 'نشاط مستقر'}

💡 توصيات:
• {'الحفاظ على الأداء الجيد' if wages_change > 0 else 'مراجعة استراتيجية العمل'}

            show_info_message("تحليل الأداء", analysis_text)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تحليل الأداء: {str(e)}")

    def show_wages_monthly_report(self):
        """عرض التقرير الشهري للأجور"""
        try:

            # الحصول على بداية ونهاية الشهر الحالي
            now = datetime.now()
            start_of_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            if now.month == 12:
                end_of_month = start_of_month.replace(year=now.year + 1, month=1) - timedelta(days=1)
            else:
                end_of_month = start_of_month.replace(month=now.month + 1) - timedelta(days=1)

            # الحصول على أجور الشهر الحالي
            monthly_wages = self.session.query(DailyWage).filter(
                DailyWage.wage_date >= start_of_month.date(),
                DailyWage.wage_date <= end_of_month.date()
            ).all()

            total_amount = sum(wage.total_amount for wage in monthly_wages)
            total_advances = sum(wage.advance for wage in monthly_wages)
            net_amount = sum(wage.net_amount for wage in monthly_wages)

            active_workers = len(set(wage.employee_id for wage in monthly_wages))
            total_work_days = sum(wage.work_days for wage in monthly_wages)


📊 إحصائيات الشهر:
• عدد العمال النشطين: {active_workers}
• إجمالي اليوميات: {len(monthly_wages)}
• إجمالي أيام العمل: {total_work_days:.1f} يوم

💰 الإحصائيات المالية:
• إجمالي الأجور: {total_amount:.2f} ج.م
• إجمالي السُلف: {total_advances:.2f} ج.م
• صافي المدفوعات: {net_amount:.2f} ج.م

📈 المتوسطات:
• متوسط الأجر للعامل: {(net_amount / max(active_workers, 1)):.2f} ج.م
• متوسط أيام العمل للعامل: {(total_work_days / max(active_workers, 1)):.1f} يوم
• متوسط الأجر اليومي: {(total_amount / max(total_work_days, 1)):.2f} ج.م

📝 ملاحظات:
• تم حساب البيانات من {start_of_month.strftime('%Y-%m-%d')} إلى {end_of_month.strftime('%Y-%m-%d')}
• البيانات تشمل جميع الأجور المسجلة في النظام"""

            show_info_message("التقرير الشهري", monthly_report)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنشاء التقرير الشهري: {str(e)}")

    def backup_wages_data(self):
        try:

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ النسخة الاحتياطية",
                f"نسخة_احتياطية_الأجور_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "ملفات JSON (*.json)"
            )
            if not file_path:
                return

            # جمع بيانات الأجور
            wages = self.session.query(DailyWage).join(Employee).all()
            backup_data = {
                'backup_date': datetime.now().isoformat(),
                'wages_count': len(wages),
                'wages': []
            }

            for wage in wages:
                wage_data = {
                    'employee_name': wage.employee.name if wage.employee else '',
                    'daily_amount': wage.daily_amount,
                    'work_days': wage.work_days,
                    'total_amount': wage.total_amount,
                    'advance': wage.advance,
                    'net_amount': wage.net_amount,
                    'wage_date': wage.wage_date.isoformat() if wage.wage_date else None
                }
                backup_data['wages'].append(wage_data)

            # حفظ البيانات
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)

            show_info_message("تم", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_wages_data(self):
        """استعادة بيانات الأجور من نسخة احتياطية"""
        show_info_message("قريباً", "ستتم إضافة هذه الميزة قريباً")

    def generate_wages_report(self):
        show_info_message("قريباً", "ستتم إضافة هذه الميزة قريباً")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر
            colors = {
                'primary': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #3b82f6, stop:0.5 #2563eb, stop:1 #1d4ed8)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #60a5fa, stop:0.5 #3b82f6, stop:1 #2563eb)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #1d4ed8, stop:1 #1e40af)',
                    'border': '#1e40af'
                },
                'emerald': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #10b981, stop:0.5 #059669, stop:1 #047857)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #34d399, stop:0.5 #10b981, stop:1 #059669)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #047857, stop:1 #065f46)',
                    'border': '#065f46'
                },
                'danger': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ef4444, stop:0.5 #dc2626, stop:1 #b91c1c)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f87171, stop:0.5 #ef4444, stop:1 #dc2626)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #b91c1c, stop:1 #991b1b)',
                    'border': '#991b1b'
                },
                'modern_teal': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #14b8a6, stop:0.5 #0d9488, stop:1 #0f766e)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #5eead4, stop:0.5 #14b8a6, stop:1 #0d9488)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0f766e, stop:1 #134e4a)',
                    'border': '#134e4a'
                },
                'indigo': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #6366f1, stop:0.5 #4f46e5, stop:1 #4338ca)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #a5b4fc, stop:0.5 #6366f1, stop:1 #4f46e5)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4338ca, stop:1 #3730a3)',
                    'border': '#3730a3'
                },
                'info': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #06b6d4, stop:0.5 #0891b2, stop:1 #0e7490)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #67e8f9, stop:0.5 #06b6d4, stop:1 #0891b2)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0e7490, stop:1 #155e75)',
                    'border': '#155e75'
                },
                'rose': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f43f5e, stop:0.5 #e11d48, stop:1 #be123c)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fb7185, stop:0.5 #f43f5e, stop:1 #e11d48)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #be123c, stop:1 #9f1239)',
                    'border': '#9f1239'
                },
                'gray': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #6b7280, stop:0.5 #4b5563, stop:1 #374151)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #9ca3af, stop:0.5 #6b7280, stop:1 #4b5563)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #374151, stop:1 #1f2937)',
                    'border': '#1f2937'
                },
                'warning': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f59e0b, stop:0.5 #d97706, stop:1 #b45309)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fbbf24, stop:0.5 #f59e0b, stop:1 #d97706)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #b45309, stop:1 #92400e)',
                    'border': '#92400e'
                }
            }

            # الحصول على ألوان الزر
            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق التصميم المتطور
                QPushButton {{
                    background: {color_scheme['normal']};
                    color: #ffffff;
                    border: 3px solid {color_scheme['border']};
                    border-radius: 12px;
                    padding: 10px 20px;
                    font-size: 14px;
                    font-weight: bold;
                    min-height: 35px;
                    max-height: 45px;
                }}
                QPushButton:hover {{
                    background: {color_scheme['hover']};
                    border: 3px solid {color_scheme['border']};
                }}
                QPushButton:pressed {{
                    background: {color_scheme['pressed']};
                    border: 3px solid {color_scheme['border']};
                }}
                QPushButton:disabled {{
                    background: #9ca3af;
                    color: #6b7280;
                    border: 3px solid #6b7280;
                }}
            """

            button.setStyleSheet(style)
            print(f"✅ تم تطبيق التصميم المتطور على الزر: {button.text()} - نوع: {button_type}")

        except Exception as e:
            print(f"❌ خطأ في تطبيق تصميم الزر: {str(e)}")
            # تطبيق تصميم بسيط في حالة الخطأ
                QPushButton {
                    background-color: #3b82f6;
                    color: white;
                    border: 2px solid #1e40af;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2563eb;
                }
            """)

class EmployeePhoneDialog(BaseDialog):

    def __init__(self, parent=None, phone=None):
        title = "تعديل رقم الهاتف" if phone else "إضافة رقم هاتف جديد"
        super().__init__(parent, title, "small")
        self.phone = phone
        self.init_ui()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # إنشاء نموذج البيانات
        form_layout = QFormLayout()
        form_layout.setSpacing(15)
        form_layout.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter)

        # تعيين نمط للعناوين
        label_font = QFont()
        label_font.setPointSize(14)
        label_font.setBold(True)

        # تعيين نمط موحد للحقول
        input_style = """
            QLineEdit, QComboBox, QCheckBox {
                padding: 8px;
                font-size: 14px;
                border: 2px solid #ccc;
                border-radius: 4px;
                min-height: 30px;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #0078D7;
            }

        # حقل رقم الهاتف
        self.phone_edit = QLineEdit()
        if self.phone:
            self.phone_edit.setText(self.phone.phone_number)
        self.phone_edit.setStyleSheet(input_style)
        self.phone_edit.setPlaceholderText("أدخل رقم الهاتف")
        phone_label = StyledLabel("رقم الهاتف:", 'subtitle')
        phone_label.setFont(label_font)
        form_layout.addRow(phone_label, self.phone_edit)

        # حقل وصف الرقم
        self.label_combo = QComboBox()
        self.label_combo.setEditable(True)
        self.label_combo.addItems(["شخصي", "عمل", "منزل", "طوارئ"])
        if self.phone and self.phone.label:
            self.label_combo.setCurrentText(self.phone.label)
        self.label_combo.setStyleSheet(input_style)
        label_label = StyledLabel("وصف الرقم:", 'subtitle')
        label_label.setFont(label_font)
        form_layout.addRow(label_label, self.label_combo)

        # خانة اختيار الرقم الرئيسي
        self.is_primary_check = QCheckBox("رقم رئيسي")
        if self.phone:
            self.is_primary_check.setChecked(self.phone.is_primary)
        self.is_primary_check
        form_layout.addRow("", self.is_primary_check)

        main_layout.addLayout(form_layout)

        # أزرار الحفظ والإلغاء
        button_layout = QHBoxLayout()

        save_button = QPushButton("💾 حفظ رقم الهاتف")
        save_button.clicked.connect(self.accept)
        save_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #10b981, stop:0.5 #059669, stop:1 #047857);
                color: #ffffff;
                border: 3px solid #065f46;
                border-radius: 12px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                min-height: 35px;
                max-height: 45px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #34d399, stop:0.5 #10b981, stop:1 #059669);
            }

        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.clicked.connect(self.reject)
        cancel_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ef4444, stop:0.5 #dc2626, stop:1 #b91c1c);
                color: #ffffff;
                border: 3px solid #991b1b;
                border-radius: 12px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                min-height: 35px;
                max-height: 45px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f87171, stop:0.5 #ef4444, stop:1 #dc2626);
            }

        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)

        main_layout.addLayout(button_layout)
        self.setLayout(main_layout)

    def validate_data(self):
        """التحقق من صحة البيانات"""
        errors = []

        # التحقق من الحقول المطلوبة
        required_fields = [
            ('phone_number', self.phone_edit, 'رقم الهاتف')
        ]
        errors.extend(self.validate_required_fields(required_fields))

        # التحقق من صحة رقم الهاتف
        phone = self.phone_edit.text().strip()
        if phone and not is_valid_phone(phone):
            errors.append("رقم الهاتف غير صحيح")

        return errors

    def get_data(self):
        phone_number = self.phone_edit.text().strip()
        label = self.label_combo.currentText().strip()
        is_primary = self.is_primary_check.isChecked()

        return {
            'phone_number': phone_number,
            'label': label,
            'is_primary': is_primary
        }

class EmployeeDialog(BaseDialog):
    """نافذة حوار لإضافة أو تعديل عامل"""

    def __init__(self, parent=None, employee=None, session=None):
        title = "تعديل بيانات العامل" if employee else "إضافة عامل جديد"
        super().__init__(parent, title)
        self.employee = employee
        self.session = session or (parent.session if parent else None)
        self.phones = []  # قائمة أرقام الهواتف المؤقتة

        # تعيين حجم النافذة لتكون كبيرة
        self.setMinimumSize(900, 600)
        self.resize(900, 600)

        self.init_ui()
        self.load_phones()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)

        # إنشاء تخطيط مقسم
        splitter = QSplitter(Qt.Horizontal)

        # الجزء الأيمن: معلومات العامل
        employee_info_widget = QWidget()
        form_layout = QFormLayout()
        form_layout.setContentsMargins(10, 10, 10, 10)
        form_layout.setSpacing(15)
        form_layout.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter)

        # تعيين نمط للعناوين
        label_font = QFont()
        label_font.setPointSize(14)
        label_font.setBold(True)

        # تعيين نمط موحد للحقول
            QLineEdit, QTextEdit, QDateEdit {
                padding: 8px;
                font-size: 14px;
                border: 2px solid #ccc;
                border-radius: 4px;
                min-height: 30px;
            }
            QLineEdit:focus, QTextEdit:focus, QDateEdit:focus {
                border-color: #0078D7;
            }
        """

        # حقل الاسم
        self.name_edit = QLineEdit()
        if self.employee:
            self.name_edit.setText(self.employee.name)
        self.name_edit.setStyleSheet(input_style)
        self.name_edit.setPlaceholderText("أدخل اسم العامل")
        name_label = QLabel("الاسم:")
        name_label.setFont(label_font)
        form_layout.addRow(name_label, self.name_edit)

        # حقل المنصب
        self.position_edit = QLineEdit()
        if self.employee and self.employee.position:
            self.position_edit.setText(self.employee.position)
        self.position_edit.setStyleSheet(input_style)
        self.position_edit.setPlaceholderText("أدخل منصب العامل")
        position_label = QLabel("المنصب:")
        position_label.setFont(label_font)
        form_layout.addRow(position_label, self.position_edit)

        # حقل رقم الهاتف الرئيسي (للتوافق مع الإصدارات السابقة)
        self.phone_edit = QLineEdit()
        if self.employee and self.employee.phone:
            self.phone_edit.setText(self.employee.phone)
        self.phone_edit.setStyleSheet(input_style)
        self.phone_edit.setPlaceholderText("أدخل رقم الهاتف الرئيسي")
        phone_label = QLabel("رقم الهاتف الرئيسي:")
        phone_label.setFont(label_font)
        form_layout.addRow(phone_label, self.phone_edit)

        # حقل العنوان
        self.address_edit = QLineEdit()
        if self.employee and self.employee.address:
            self.address_edit.setText(self.employee.address)
        self.address_edit.setStyleSheet(input_style)
        self.address_edit.setPlaceholderText("أدخل العنوان")
        address_label = QLabel("العنوان:")
        address_label.setFont(label_font)
        form_layout.addRow(address_label, self.address_edit)

        # حقل تاريخ التوظيف
        self.hire_date_edit = QDateEdit()
        self.hire_date_edit.setCalendarPopup(True)
        if self.employee and self.employee.hire_date:
            self.hire_date_edit.setDate(QDate.fromString(self.employee.hire_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))
        else:
            self.hire_date_edit.setDate(QDate.currentDate())
        self.hire_date_edit.setStyleSheet(input_style)
        hire_date_label = QLabel("تاريخ التوظيف:")
        hire_date_label.setFont(label_font)
        form_layout.addRow(hire_date_label, self.hire_date_edit)

        # حقل الملاحظات
        self.notes_edit = QTextEdit()
        if self.employee and self.employee.notes:
            self.notes_edit.setText(self.employee.notes)
        self.notes_edit.setStyleSheet(input_style)
        self.notes_edit.setPlaceholderText("أدخل ملاحظات إضافية")
        self.notes_edit.setMaximumHeight(100)
        notes_label = QLabel("ملاحظات:")
        notes_label.setFont(label_font)
        form_layout.addRow(notes_label, self.notes_edit)

        employee_info_widget.setLayout(form_layout)

        # الجزء الأيسر: أرقام الهواتف
        phones_widget = QWidget()
        phones_widget.setMaximumWidth(300)  # تحديد العرض الأقصى لقائمة أرقام الهواتف
        phones_layout = QVBoxLayout()
        phones_layout.setContentsMargins(5, 5, 5, 5)
        phones_layout.setSpacing(8)

        # عنوان قائمة أرقام الهواتف
        phones_title = QLabel("أرقام الهواتف:")
        phones_title.setFont(label_font)
        phones_layout.addWidget(phones_title)

        # قائمة أرقام الهواتف
        self.phones_list = QListWidget()
        self.phones_list
        self.phones_list.setAlternatingRowColors(True)
        self.phones_list.setMinimumHeight(250)
        phones_layout.addWidget(self.phones_list)

        # أزرار إدارة أرقام الهواتف باستخدام النمط الموحد
        phones_buttons_layout = QVBoxLayout()
        phones_buttons_layout.setSpacing(8)

        # إنشاء الأزرار باستخدام StyledButton
        self.add_phone_button = StyledButton("📞 إضافة رقم", "success", "normal")
        self.add_phone_button.clicked.connect(self.add_phone)

        self.edit_phone_button = StyledButton("✏️ تعديل رقم", "primary", "normal")
        self.edit_phone_button.clicked.connect(self.edit_phone)

        self.delete_phone_button = StyledButton("🗑️ حذف رقم", "danger", "normal")
        self.delete_phone_button.clicked.connect(self.delete_phone)

        self.view_phone_button = StyledButton("👁️ عرض التفاصيل", "info", "normal")
        self.view_phone_button.clicked.connect(self.view_phone_details)

        # إضافة الأزرار بعرض كامل
        phones_buttons_layout.addWidget(self.add_phone_button.button)
        phones_buttons_layout.addWidget(self.edit_phone_button.button)
        phones_buttons_layout.addWidget(self.delete_phone_button.button)
        phones_buttons_layout.addWidget(self.view_phone_button.button)

        phones_layout.addLayout(phones_buttons_layout)
        phones_widget.setLayout(phones_layout)

        # إضافة الأجزاء إلى المقسم
        splitter.addWidget(employee_info_widget)
        splitter.addWidget(phones_widget)

        # ضبط أحجام المقسم
        splitter.setSizes([600, 300])

        main_layout.addWidget(splitter)

        # أزرار الحفظ والإلغاء بعرض كامل وجانب بعض
        main_buttons_layout = QHBoxLayout()
        main_buttons_layout.setSpacing(15)
        main_buttons_layout.setContentsMargins(0, 20, 0, 0)

        # زر الحفظ
        self.save_button = StyledButton("💾 حفظ بيانات العامل", "success")
        self.save_button.clicked.connect(self.accept)
        main_buttons_layout.addWidget(self.save_button.button)

        # زر الإلغاء
        self.cancel_button = StyledButton("❌ إلغاء العملية", "secondary")
        self.cancel_button.clicked.connect(self.reject)
        main_buttons_layout.addWidget(self.cancel_button.button)

        main_layout.addLayout(main_buttons_layout)
        self.setLayout(main_layout)

    def validate_data(self):
        errors = []

        # التحقق من الحقول المطلوبة
        required_fields = [
            ('name', self.name_edit, 'اسم العامل')
        ]
        errors.extend(self.validate_required_fields(required_fields))

        # التحقق من صحة رقم الهاتف
        phone = self.phone_edit.text().strip()
        if phone and not is_valid_phone(phone):
            errors.append("رقم الهاتف غير صحيح")

        return errors

    def load_phones(self):
        """تحميل أرقام الهواتف من قاعدة البيانات"""
        if self.employee and self.session:
            try:
                phone_records = self.session.query(EmployeePhone).filter_by(employee_id=self.employee.id).all()
                for phone_record in phone_records:
                    phone = EmployeePhone(
                        phone_number=phone_record.phone_number,
                        label=phone_record.label,
                        is_primary=phone_record.is_primary
                    )
                    self.phones.append(phone)
                self.refresh_phones_list()
            except Exception as e:
                print(f"خطأ في تحميل أرقام الهواتف: {str(e)}")

    def refresh_phones_list(self):
        self.phones_list.clear()

        for phone in self.phones:
            # إنشاء عنصر في القائمة
            label_text = f"{phone.phone_number}"
            if phone.label:
                label_text += f" ({phone.label})"

            item = QListWidgetItem(label_text)

            # تعيين محاذاة النص في المنتصف
            item.setTextAlignment(Qt.AlignCenter)

            # تعيين نمط خاص للرقم الرئيسي
            if phone.is_primary:
                item.setText(f"{label_text} [رئيسي]")
                item.setForeground(QColor("#0078D7"))  # لون أزرق للرقم الرئيسي
                font = item.font()
                font.setBold(True)
                font.setPointSize(17)
                item.setFont(font)
            else:
                # تعيين حجم الخط للعناصر العادية أيضاً
                font = item.font()
                font.setPointSize(17)
                item.setFont(font)

            # تخزين معرف الهاتف في البيانات المخصصة للعنصر
            if hasattr(phone, 'id') and phone.id:
                item.setData(Qt.UserRole, phone.id)

            # إضافة العنصر إلى القائمة
            self.phones_list.addItem(item)

    def add_phone(self):
        """إضافة رقم هاتف جديد"""
        dialog = EmployeePhoneDialog(self)

        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # إضافة رقم الهاتف إلى القائمة المؤقتة
                phone = EmployeePhone(
                    phone_number=data['phone_number'],
                    label=data['label'],
                    is_primary=data['is_primary']
                )

                # إذا كان هذا الرقم رئيسياً، قم بإلغاء تعيين الأرقام الرئيسية الأخرى
                if data['is_primary']:
                    for p in self.phones:
                        p.is_primary = False

                self.phones.append(phone)

                # تحديث القائمة
                self.refresh_phones_list()

                # تحديث حقل رقم الهاتف الرئيسي إذا كان هذا هو الرقم الرئيسي
                if data['is_primary']:
                    self.phone_edit.setText(data['phone_number'])

    def edit_phone(self):
        current_row = self.phones_list.currentRow()
        if current_row < 0:
            show_error_message("خطأ", "الرجاء اختيار رقم هاتف من القائمة")
            return

        phone = self.phones[current_row]
        dialog = EmployeePhoneDialog(self, phone)

        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # تحديث بيانات رقم الهاتف
                old_phone_number = phone.phone_number
                phone.phone_number = data['phone_number']
                phone.label = data['label']

                # إذا كان هذا الرقم رئيسياً، قم بإلغاء تعيين الأرقام الرئيسية الأخرى
                if data['is_primary'] and not phone.is_primary:
                    for p in self.phones:
                        p.is_primary = False

                phone.is_primary = data['is_primary']

                # تحديث القائمة
                self.refresh_phones_list()

                # تحديث حقل رقم الهاتف الرئيسي
                if data['is_primary']:
                    self.phone_edit.setText(data['phone_number'])
                elif old_phone_number == self.phone_edit.text():
                    # إذا كان الرقم المعدل هو الرقم الرئيسي الحالي، ابحث عن رقم رئيسي آخر
                    primary_phone = next((p for p in self.phones if p.is_primary), None)
                    self.phone_edit.setText(primary_phone.phone_number if primary_phone else "")

    def view_phone_details(self):
        """عرض تفاصيل رقم الهاتف المحدد"""
        current_row = self.phones_list.currentRow()
        if current_row < 0:
            show_error_message("خطأ", "الرجاء اختيار رقم هاتف من القائمة")
            return

        phone = self.phones[current_row]

        # إنشاء نافذة حوار لعرض التفاصيل
        details_dialog = QDialog(self)
        details_dialog.setWindowTitle("تفاصيل رقم الهاتف")
        details_dialog.setFixedSize(450, 350)  # حجم أكبر لظهور الخط بشكل أفضل
        details_dialog.setModal(True)
        details_dialog.setWindowFlags(details_dialog.windowFlags() & ~Qt.WindowMaximizeButtonHint)  # إزالة زر التكبير

        # تطبيق نمط النافذة
        details_dialog

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)

        # عنوان النافذة
        title_label = QLabel("📱 رقم الهاتف")
        title_label.setAlignment(Qt.AlignCenter)
        title_label
        main_layout.addWidget(title_label)

        # منطقة عرض التفاصيل - مستطيل رئيسي واحد فقط
        details_frame = QWidget()
        details_frame

        # تخطيط بسيط بدون مستطيلات داخلية
        details_layout = QVBoxLayout()
        details_layout.setSpacing(15)
        details_layout.setContentsMargins(10, 10, 10, 10)

        # نمط موحد للعناوين والقيم بدون مستطيلات داخلية
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #0078D7;
                margin-bottom: 5px;
            }
        """

            QLabel {
                font-size: 16px;
                color: #333;
                padding: 10px;
                margin-bottom: 10px;
                background-color: transparent;
            }
        """

        # رقم الهاتف
        phone_label = QLabel("رقم الهاتف:")
        phone_label.setStyleSheet(label_style)
        phone_value = QLabel(phone.phone_number)
        phone_value
        phone_value.setAlignment(Qt.AlignCenter)
        details_layout.addWidget(phone_label)
        details_layout.addWidget(phone_value)

        # الوصف
        desc_label = QLabel("الوصف:")
        desc_label.setStyleSheet(label_style)
        desc_value = QLabel(phone.label or "غير محدد")
        desc_value.setStyleSheet(value_style)
        desc_value.setAlignment(Qt.AlignCenter)
        details_layout.addWidget(desc_label)
        details_layout.addWidget(desc_value)

        # النوع (رئيسي/إضافي)
        type_label = QLabel("نوع الرقم:")
        type_label.setStyleSheet(label_style)

        type_text = "🌟 رقم رئيسي" if phone.is_primary else "📞 رقم إضافي"
        type_color = "#4CAF50" if phone.is_primary else "#FF9800"

        type_value = QLabel(type_text)
        type_value
        type_value.setAlignment(Qt.AlignCenter)
        details_layout.addWidget(type_label)
        details_layout.addWidget(type_value)

        details_frame.setLayout(details_layout)
        main_layout.addWidget(details_frame)

        # زر الإغلاق بعرض القائمة
        close_button = QPushButton("🔙 إغلاق")
        close_button
        close_button.clicked.connect(details_dialog.accept)
        main_layout.addWidget(close_button)

        details_dialog.setLayout(main_layout)

        # توسيط النافذة في الشاشة بعد إنشاء المحتوى
        def center_dialog():
            screen = QApplication.desktop().screenGeometry()
            window = details_dialog.geometry()
            x = (screen.width() - window.width()) // 2
            y = (screen.height() - window.height()) // 2
            details_dialog.move(x, y)

        QTimer.singleShot(10, center_dialog)

        details_dialog.exec_()

    def delete_phone(self):
        current_row = self.phones_list.currentRow()
        if current_row < 0:
            show_error_message("خطأ", "الرجاء اختيار رقم هاتف من القائمة")
            return

        phone = self.phones[current_row]

        if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف رقم الهاتف {phone.phone_number}؟"):
            # إذا كان الرقم المحذوف هو الرقم الرئيسي، قم بتعيين أول رقم آخر كرئيسي
            was_primary = phone.is_primary
            self.phones.pop(current_row)

            if was_primary and self.phones:
                self.phones[0].is_primary = True
                self.phone_edit.setText(self.phones[0].phone_number)
            elif was_primary:
                self.phone_edit.setText("")

            # تحديث القائمة
            self.refresh_phones_list()

    def get_data(self):
        """الحصول على بيانات العامل من النموذج"""
        # تعيين رقم الهاتف الرئيسي من قائمة أرقام الهواتف
        primary_phone = None
        for p in self.phones:
            if p.is_primary:
                primary_phone = p.phone_number
                break

        # إذا لم يكن هناك رقم هاتف رئيسي، استخدم الرقم المدخل في حقل رقم الهاتف الرئيسي
        if not primary_phone:
            primary_phone = self.phone_edit.text().strip()

        return {
            'name': self.name_edit.text().strip(),
            'position': self.position_edit.text().strip(),
            'phone': primary_phone,  # استخدام رقم الهاتف الرئيسي
            'address': self.address_edit.text().strip(),
            'hire_date': self.hire_date_edit.date().toPyDate(),
            'notes': self.notes_edit.toPlainText().strip(),
            'phones': self.phones  # إضافة قائمة أرقام الهواتف
        }

class EmployeeDetailsDialog(QDialog):

    def __init__(self, parent=None, employee=None, session=None):
        super().__init__(parent)
        self.employee = employee
        self.session = session
        self.setWindowTitle(f"تفاصيل العامل: {employee.name}")
        self.setFixedSize(650, 650)  # حجم ثابت لاستيعاب أرقام الهواتف المتعددة
        self.setModal(True)
        # إزالة جميع أزرار التحكم في الحجم ومنع تغيير الحجم نهائياً
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setSizePolicy(self.sizePolicy().Fixed, self.sizePolicy().Fixed)
        self.setMinimumSize(650, 650)
        self.setMaximumSize(650, 650)
        # منع تغيير الحجم بأي طريقة
        self.setFixedWidth(650)
        self.setFixedHeight(650)
        self.init_ui()

        # توسيط النافذة في الشاشة بعد إنشاء المحتوى
        self.center_window()

    def center_window(self):
        """توسيط النافذة في الشاشة مع ضمان الحجم الثابت"""

        def do_center():
            # التأكد من الحجم الثابت قبل التوسيط
            self.setFixedSize(650, 650)
            self.setMinimumSize(650, 650)
            self.setMaximumSize(650, 650)

            screen = QApplication.desktop().screenGeometry()
            window = self.geometry()
            x = (screen.width() - window.width()) // 2
            y = (screen.height() - window.height()) // 2
            self.move(x, y)

            # التأكد مرة أخرى من الحجم الثابت بعد التوسيط
            self.setFixedSize(650, 650)

        # تأخير التوسيط قليلاً للتأكد من أن النافذة جاهزة
        QTimer.singleShot(10, do_center)

    def init_ui(self):
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # عنوان النافذة
        title_label = QLabel(f"👤 {self.employee.name}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label
        main_layout.addWidget(title_label)

        # منطقة عرض التفاصيل - بدون حدود
        details_frame = QWidget()
        details_frame

        # تخطيط بسيط بدون مستطيلات داخلية
        details_layout = QVBoxLayout()
        details_layout.setSpacing(15)
        details_layout.setContentsMargins(15, 15, 15, 15)

        # نمط موحد للعناوين والقيم بدون مستطيلات داخلية
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #0078D7;
                margin-bottom: 8px;
                min-height: 25px;
            }
        """

            QLabel {
                font-size: 18px;
                color: #333;
                padding: 12px;
                margin-bottom: 15px;
                background-color: transparent;
                font-weight: 500;
                min-height: 35px;
                word-wrap: true;
            }
        """

        # الاسم
        name_label = QLabel("الاسم:")
        name_label.setStyleSheet(label_style)
        name_label.setWordWrap(True)
        name_value = QLabel(self.employee.name)
        name_value.setStyleSheet(value_style)
        name_value.setAlignment(Qt.AlignCenter)
        name_value.setWordWrap(True)
        details_layout.addWidget(name_label)
        details_layout.addWidget(name_value)

        # المنصب
        position_label = QLabel("المنصب:")
        position_label.setStyleSheet(label_style)
        position_label.setWordWrap(True)
        position_value = QLabel(self.employee.position or "غير محدد")
        position_value.setStyleSheet(value_style)
        position_value.setAlignment(Qt.AlignCenter)
        position_value.setWordWrap(True)
        details_layout.addWidget(position_label)
        details_layout.addWidget(position_value)

        # رقم الهاتف الرئيسي
        phone_label = QLabel("رقم الهاتف:")
        phone_label.setStyleSheet(label_style)
        phone_label.setWordWrap(True)
        phone_value = QLabel(self.employee.phone or "غير محدد")
        phone_value.setStyleSheet(value_style)
        phone_value.setAlignment(Qt.AlignCenter)
        phone_value.setWordWrap(True)
        details_layout.addWidget(phone_label)
        details_layout.addWidget(phone_value)

        # العنوان
        address_label = QLabel("العنوان:")
        address_label.setStyleSheet(label_style)
        address_label.setWordWrap(True)
        address_value = QLabel(self.employee.address or "غير محدد")
        address_value.setStyleSheet(value_style)
        address_value.setAlignment(Qt.AlignCenter)
        address_value.setWordWrap(True)
        details_layout.addWidget(address_label)
        details_layout.addWidget(address_value)

        # تاريخ التوظيف
        hire_date = self.employee.hire_date.strftime("%Y-%m-%d") if self.employee.hire_date else "غير محدد"
        date_label = QLabel("تاريخ التوظيف:")
        date_label.setStyleSheet(label_style)
        date_label.setWordWrap(True)
        date_value = QLabel(hire_date)
        date_value.setStyleSheet(value_style)
        date_value.setAlignment(Qt.AlignCenter)
        date_value.setWordWrap(True)
        details_layout.addWidget(date_label)
        details_layout.addWidget(date_value)

        # أرقام الهواتف المتعددة
        phones_label = QLabel("أرقام الهواتف:")
        phones_label.setStyleSheet(label_style)
        phones_label.setWordWrap(True)
        details_layout.addWidget(phones_label)

        # إنشاء قائمة أرقام الهواتف
        phones_list = QListWidget()
        phones_list.setFixedHeight(150)  # ارتفاع ثابت لمنع التكبير التلقائي
        phones_list.setFixedWidth(580)   # عرض ثابت لمنع التكبير التلقائي
        phones_list.setSizePolicy(self.sizePolicy().Fixed, self.sizePolicy().Fixed)
        phones_list.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        phones_list.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        phones_list

        # جمع جميع أرقام الهواتف بدون تكرار
        added_phones = set()  # لتجنب التكرار

        # إضافة الأرقام من جدول أرقام الهواتف أولاً
        if self.session:
            try:
                phone_records = self.session.query(EmployeePhone).filter_by(employee_id=self.employee.id).all()
                for phone in phone_records:
                    if phone.phone_number not in added_phones:
                        phone_text = f"{phone.phone_number}"
                        if phone.label:
                            phone_text += f" ({phone.label})"
                        if phone.is_primary:
                            phone_text += " [رئيسي]"

                        item = QListWidgetItem(phone_text)
                        item.setTextAlignment(Qt.AlignCenter)
                        if phone.is_primary:
                            item.setForeground(QColor("#0078D7"))
                            font = item.font()
                            font.setBold(True)
                            font.setPointSize(16)
                            item.setFont(font)
                        else:
                            font = item.font()
                            font.setPointSize(16)
                            item.setFont(font)
                        phones_list.addItem(item)
                        added_phones.add(phone.phone_number)
            except Exception as e:
                print(f"خطأ في تحميل أرقام الهواتف: {str(e)}")

        # إضافة الرقم الرئيسي من جدول العمال إذا لم يكن موجوداً
        if self.employee.phone and self.employee.phone not in added_phones:
            main_phone_item = QListWidgetItem(f"{self.employee.phone} (رئيسي)")
            main_phone_item.setTextAlignment(Qt.AlignCenter)
            main_phone_item.setForeground(QColor("#0078D7"))
            font = main_phone_item.font()
            font.setBold(True)
            font.setPointSize(16)
            main_phone_item.setFont(font)
            phones_list.addItem(main_phone_item)
            added_phones.add(self.employee.phone)

        # إضافة رسالة إذا لم توجد أرقام
        if phones_list.count() == 0:
            no_phones_item = QListWidgetItem("لا توجد أرقام هواتف")
            no_phones_item.setTextAlignment(Qt.AlignCenter)
            font = no_phones_item.font()
            font.setPointSize(16)
            font.setItalic(True)
            no_phones_item.setFont(font)
            no_phones_item.setForeground(QColor("#999"))
            phones_list.addItem(no_phones_item)

        details_layout.addWidget(phones_list)

        details_frame.setLayout(details_layout)
        main_layout.addWidget(details_frame)

        # زر الإغلاق فقط
        close_button = QPushButton("🔙 إغلاق")
        close_button
        close_button.clicked.connect(self.accept)
        main_layout.addWidget(close_button)

        self.setLayout(main_layout)

    def resizeEvent(self, event):
        # تجاهل أي محاولة لتغيير الحجم والحفاظ على الحجم الثابت
        self.setFixedSize(650, 650)
        super().resizeEvent(event)

class DailyWageDialog(BaseDialog):
    """نافذة حوار لإضافة أو تعديل يومية عامل"""

    def __init__(self, parent=None, daily_wage=None, session=None):
        title = "تعديل اليومية" if daily_wage else "إضافة يومية جديدة"
        super().__init__(parent, title)
        self.daily_wage = daily_wage
        self.session = session

        # تعيين حجم النافذة لتكون متوسطة
        self.setMinimumSize(500, 400)
        self.resize(500, 400)

        self.init_ui()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # إنشاء نموذج البيانات
        form_layout = QFormLayout()
        form_layout.setSpacing(15)
        form_layout.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter)

        # تعيين نمط للعناوين
        label_font = QFont()
        label_font.setPointSize(14)
        label_font.setBold(True)

        # تعيين نمط موحد للحقول
            QComboBox, QDoubleSpinBox, QDateEdit {
                padding: 8px;
                font-size: 14px;
                border: 2px solid #ccc;
                border-radius: 4px;
                min-height: 30px;
            }
            QComboBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {
                border-color: #0078D7;
            }
        """

        # اختيار العامل
        self.employee_combo = QComboBox()
        self.employee_combo.setStyleSheet(input_style)
        self.load_employees()
        employee_label = StyledLabel("العامل:", 'subtitle')
        employee_label.label.setFont(label_font)
        form_layout.addRow(employee_label.label, self.employee_combo)

        # المبلغ اليومي
        self.daily_amount_spin = QDoubleSpinBox()
        self.daily_amount_spin.setRange(0, 999999)
        self.daily_amount_spin.setDecimals(2)
        self.daily_amount_spin.setSuffix(" ج.م")
        if self.daily_wage:
            self.daily_amount_spin.setValue(self.daily_wage.daily_amount)
        self.daily_amount_spin.setStyleSheet(input_style)
        daily_amount_label = StyledLabel("المبلغ اليومي:", 'subtitle')
        daily_amount_label.label.setFont(label_font)
        form_layout.addRow(daily_amount_label.label, self.daily_amount_spin)

        # عدد أيام العمل
        self.work_days_spin = QDoubleSpinBox()
        self.work_days_spin.setRange(0.5, 31)
        self.work_days_spin.setDecimals(1)
        self.work_days_spin.setSingleStep(0.5)
        if self.daily_wage:
            self.work_days_spin.setValue(self.daily_wage.work_days)
        else:
            self.work_days_spin.setValue(1.0)
        self.work_days_spin.setStyleSheet(input_style)
        work_days_label = StyledLabel("عدد أيام العمل:", 'subtitle')
        work_days_label.label.setFont(label_font)
        form_layout.addRow(work_days_label.label, self.work_days_spin)

        # السُلف
        self.advance_spin = QDoubleSpinBox()
        self.advance_spin.setRange(0, 999999)
        self.advance_spin.setDecimals(2)
        self.advance_spin.setSuffix(" ج.م")
        if self.daily_wage:
            self.advance_spin.setValue(self.daily_wage.advance)
        self.advance_spin.setStyleSheet(input_style)
        advance_label = StyledLabel("السُلف:", 'subtitle')
        advance_label.label.setFont(label_font)
        form_layout.addRow(advance_label.label, self.advance_spin)

        # تاريخ اليومية
        self.wage_date_edit = QDateEdit()
        self.wage_date_edit.setCalendarPopup(True)
        if self.daily_wage and self.daily_wage.wage_date:
            self.wage_date_edit.setDate(QDate.fromString(self.daily_wage.wage_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))
        else:
            self.wage_date_edit.setDate(QDate.currentDate())
        self.wage_date_edit.setStyleSheet(input_style)
        wage_date_label = StyledLabel("تاريخ اليومية:", 'subtitle')
        wage_date_label.label.setFont(label_font)
        form_layout.addRow(wage_date_label.label, self.wage_date_edit)

        # ربط الحقول لحساب المجموع تلقائياً
        self.daily_amount_spin.valueChanged.connect(self.calculate_totals)
        self.work_days_spin.valueChanged.connect(self.calculate_totals)
        self.advance_spin.valueChanged.connect(self.calculate_totals)

        main_layout.addLayout(form_layout)

        # عرض المجاميع
        totals_layout = QHBoxLayout()

        self.total_amount_label = StyledLabel("إجمالي المبلغ: 0.00 ج.م", 'subtitle')
        totals_layout.addWidget(self.total_amount_label.label)

        self.net_amount_label = StyledLabel("صافي المبلغ: 0.00 ج.م", 'subtitle')
        totals_layout.addWidget(self.net_amount_label.label)

        main_layout.addLayout(totals_layout)

        # حساب المجاميع الأولية
        self.calculate_totals()

        # أزرار الحفظ والإلغاء
        button_layout = QHBoxLayout()

        save_button = QPushButton("💾 حفظ اليومية")
        save_button.clicked.connect(self.accept)
        self.style_advanced_button(save_button, 'emerald')

        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.clicked.connect(self.reject)
        self.style_advanced_button(cancel_button, 'danger')

        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)

        main_layout.addLayout(button_layout)
        self.setLayout(main_layout)

    def load_employees(self):
        try:
            employees = self.session.query(Employee).all()
            for employee in employees:
                self.employee_combo.addItem(employee.name, employee.id)

            # تحديد العامل المحدد إذا كان في وضع التعديل
            if self.daily_wage:
                for i in range(self.employee_combo.count()):
                    if self.employee_combo.itemData(i) == self.daily_wage.employee_id:
                        self.employee_combo.setCurrentIndex(i)
                        break
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تحميل قائمة العمال: {str(e)}")

    def calculate_totals(self):
        """حساب المجاميع تلقائياً"""
        daily_amount = self.daily_amount_spin.value()
        work_days = self.work_days_spin.value()
        advance = self.advance_spin.value()

        total_amount = daily_amount * work_days
        net_amount = total_amount - advance

        self.total_amount_label.label.setText(f"إجمالي المبلغ: {total_amount:.2f} ج.م")
        self.net_amount_label.label.setText(f"صافي المبلغ: {net_amount:.2f} ج.م")

    def validate_data(self):
        errors = []

        # التحقق من اختيار العامل
        if self.employee_combo.currentData() is None:
            errors.append("يجب اختيار العامل")

        # التحقق من المبلغ اليومي
        if self.daily_amount_spin.value() <= 0:
            errors.append("المبلغ اليومي يجب أن يكون أكبر من الصفر")

        # التحقق من عدد أيام العمل
        if self.work_days_spin.value() <= 0:
            errors.append("عدد أيام العمل يجب أن يكون أكبر من الصفر")

        return errors

    def get_data(self):
        """الحصول على بيانات اليومية من النموذج"""
        daily_amount = self.daily_amount_spin.value()
        work_days = self.work_days_spin.value()
        advance = self.advance_spin.value()
        total_amount = daily_amount * work_days
        net_amount = total_amount - advance

        return {
            'employee_id': self.employee_combo.currentData(),
            'daily_amount': daily_amount,
            'work_days': work_days,
            'total_amount': total_amount,
            'advance': advance,
            'net_amount': net_amount,
            'wage_date': self.wage_date_edit.date().toPyDate()
        }

    def style_advanced_button(self, button, button_type, has_menu=False):
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر
            colors = {
                'primary': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #3b82f6, stop:0.5 #2563eb, stop:1 #1d4ed8)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #60a5fa, stop:0.5 #3b82f6, stop:1 #2563eb)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #1d4ed8, stop:1 #1e40af)',
                    'border': '#1e40af'
                },
                'emerald': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #10b981, stop:0.5 #059669, stop:1 #047857)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #34d399, stop:0.5 #10b981, stop:1 #059669)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #047857, stop:1 #065f46)',
                    'border': '#065f46'
                },
                'danger': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ef4444, stop:0.5 #dc2626, stop:1 #b91c1c)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f87171, stop:0.5 #ef4444, stop:1 #dc2626)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #b91c1c, stop:1 #991b1b)',
                    'border': '#991b1b'
                },
                'modern_teal': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #14b8a6, stop:0.5 #0d9488, stop:1 #0f766e)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #5eead4, stop:0.5 #14b8a6, stop:1 #0d9488)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0f766e, stop:1 #134e4a)',
                    'border': '#134e4a'
                },
                'indigo': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #6366f1, stop:0.5 #4f46e5, stop:1 #4338ca)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #a5b4fc, stop:0.5 #6366f1, stop:1 #4f46e5)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4338ca, stop:1 #3730a3)',
                    'border': '#3730a3'
                },
                'info': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #06b6d4, stop:0.5 #0891b2, stop:1 #0e7490)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #67e8f9, stop:0.5 #06b6d4, stop:1 #0891b2)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0e7490, stop:1 #155e75)',
                    'border': '#155e75'
                },
                'rose': {
                    'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f43f5e, stop:0.5 #e11d48, stop:1 #be123c)',
                    'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fb7185, stop:0.5 #f43f5e, stop:1 #e11d48)',
                    'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #be123c, stop:1 #9f1239)',
                    'border': '#9f1239'
                }
            }

            # الحصول على ألوان الزر
            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق التصميم المتطور
            style = f"""
                QPushButton {{
                    background: {color_scheme['normal']};
                    color: #ffffff;
                    border: 3px solid {color_scheme['border']};
                    border-radius: 12px;
                    padding: 10px 20px;
                    font-size: 14px;
                    font-weight: bold;
                    min-height: 35px;
                    max-height: 45px;
                }}
                QPushButton:hover {{
                    background: {color_scheme['hover']};
                    border: 3px solid {color_scheme['border']};
                }}
                QPushButton:pressed {{
                    background: {color_scheme['pressed']};
                    border: 3px solid {color_scheme['border']};
                }}
                QPushButton:disabled {{
                    background: #9ca3af;
                    color: #6b7280;
                    border: 3px solid #6b7280;
                }}

            button.setStyleSheet(style)
            print(f"✅ تم تطبيق التصميم المتطور على الزر: {button.text()} - نوع: {button_type}")

        except Exception as e:
            print(f"❌ خطأ في تطبيق تصميم الزر: {str(e)}")
            # تطبيق تصميم بسيط في حالة الخطأ
            button.setStyleSheet("""
                QPushButton {
                    background-color: #3b82f6;
                    color: white;
                    border: 2px solid #1e40af;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2563eb;
                }

class EmployeeAttachmentsDialog(QDialog):
    """نافذة إدارة المرفقات للعمال"""

    def __init__(self, parent, employee, session):
        super().__init__(parent)
        self.employee = employee
        self.session = session
        self.attachments = []

        self.setWindowTitle(f"إدارة مرفقات العامل: {employee.name}")
        self.setModal(True)
        self.resize(800, 600)

        self.init_ui()
        self.load_attachments()

    def init_ui(self):
        layout = QVBoxLayout()

        # عنوان النافذة
        title_label = QLabel(f"📎 إدارة مرفقات العامل: {self.employee.name}")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        layout.addWidget(title_label)

        # قائمة المرفقات
        self.attachments_list = QListWidget()
        self.attachments_list.setStyleSheet("""
            QListWidget {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background: white;
                font-size: 12px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QListWidget::item:selected {
                background: #3498db;
                color: white;
            }
        layout.addWidget(self.attachments_list)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        add_file_btn = QPushButton("📁 إضافة ملف")
        add_file_btn.clicked.connect(self.add_file)
        add_file_btn.setStyleSheet("""
            QPushButton {
                background: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2ecc71;
            }

        view_file_btn = QPushButton("👁️ عرض")
        view_file_btn.clicked.connect(self.view_file)
        view_file_btn.setStyleSheet("""
            QPushButton {
                background: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #5dade2;
            }

        delete_file_btn = QPushButton("🗑️ حذف")
        delete_file_btn.clicked.connect(self.delete_file)
        delete_file_btn.setStyleSheet("""
            QPushButton {
                background: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #ec7063;
            }

        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.accept)
        close_btn.setStyleSheet("""
            QPushButton {
                background: #95a5a6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #bdc3c7;
            }

        buttons_layout.addWidget(add_file_btn)
        buttons_layout.addWidget(view_file_btn)
        buttons_layout.addWidget(delete_file_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def load_attachments(self):
        """تحميل قائمة المرفقات"""
        self.attachments_list.clear()

        # هنا يمكن إضافة كود لتحميل المرفقات من قاعدة البيانات
        # في الوقت الحالي سنعرض رسالة توضيحية
        item = QListWidgetItem("لا توجد مرفقات حالياً - اضغط 'إضافة ملف' لإضافة مرفق جديد")
        item.setData(Qt.UserRole, None)
        self.attachments_list.addItem(item)

    def add_file(self):

        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختر ملف لإضافته",
            "",
            "جميع الملفات (*.*)"
        )

        if file_path:
            file_name = os.path.basename(file_path)

            # إضافة الملف للقائمة
            item = QListWidgetItem(f"📄 {file_name}")
            item.setData(Qt.UserRole, file_path)
            self.attachments_list.addItem(item)

            # إزالة الرسالة التوضيحية إذا كانت موجودة
            if self.attachments_list.count() > 1:
                first_item = self.attachments_list.item(0)
                if first_item.data(Qt.UserRole) is None:
                    self.attachments_list.takeItem(0)

            show_info_message("تم", f"تم إضافة الملف: {file_name}")

    def view_file(self):
        """عرض الملف المحدد"""

        current_item = self.attachments_list.currentItem()
        if not current_item:
            show_error_message("خطأ", "الرجاء اختيار ملف من القائمة")
            return

        file_path = current_item.data(Qt.UserRole)
        if not file_path:
            show_error_message("خطأ", "لا يوجد ملف محدد")
            return

        try:

            if platform.system() == 'Windows':
                os.startfile(file_path)
            elif platform.system() == 'Darwin':  # macOS
                subprocess.call(['open', file_path])
            else:  # Linux
                subprocess.call(['xdg-open', file_path])

        except Exception as e:
            show_error_message("خطأ", f"لا يمكن فتح الملف: {str(e)}")

    def delete_file(self):

        current_item = self.attachments_list.currentItem()
        if not current_item:
            show_error_message("خطأ", "الرجاء اختيار ملف من القائمة")
            return

        file_path = current_item.data(Qt.UserRole)
        if not file_path:
            show_error_message("خطأ", "لا يوجد ملف محدد")
            return

        if show_confirmation_message("تأكيد الحذف", "هل أنت متأكد من حذف هذا المرفق؟"):
            row = self.attachments_list.row(current_item)
            self.attachments_list.takeItem(row)

            # إضافة الرسالة التوضيحية إذا لم تعد هناك مرفقات
            if self.attachments_list.count() == 0:
                item = QListWidgetItem("لا توجد مرفقات حالياً - اضغط 'إضافة ملف' لإضافة مرفق جديد")
                item.setData(Qt.UserRole, None)
                self.attachments_list.addItem(item)

            show_info_message("تم", "تم حذف المرفق بنجاح")

class EmployeeBalanceAdjustmentDialog(QDialog):
    """نافذة حوار لتعديل المبلغ المستحق للعامل - مطابق للعملاء"""

    def __init__(self, parent=None, employee=None):
        super().__init__(parent)
        self.setWindowTitle("تعديل المبلغ المستحق")
        self.setModal(True)
        self.setMinimumSize(400, 300)
        self.employee = employee

        # تطبيق نمط أساسي للنافذة
            QDialog {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
            }
            QLabel {
                color: #495057;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: 12px;
            }
            QLineEdit {
                padding: 8px;
                border: 2px solid #ced4da;
                border-radius: 6px;
                background-color: white;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #007bff;
            }
            QPushButton {
                padding: 8px 16px;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
        """)

        self.init_ui()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # عرض معلومات العامل
        employee_info = QLabel(f"العامل: {self.employee.name}")
        main_layout.addWidget(employee_info)

        current_balance = QLabel(f"المبلغ المستحق الحالي: {self.employee.balance:.2f}")
        main_layout.addWidget(current_balance)

        # إضافة شرح للمبلغ المستحق
        balance_explanation = QLabel("ملاحظة: القيمة الموجبة تعني أن المبلغ للعامل، والقيمة السالبة تعني أن المبلغ على العامل")
        balance_explanation.setWordWrap(True)
        main_layout.addWidget(balance_explanation)

        # إنشاء مجموعة الخيارات
        operation_group = QGroupBox("نوع العملية")
        operation_layout = QVBoxLayout()

        self.add_radio = QRadioButton("إضافة مبلغ")
        self.add_radio.setChecked(True)
        self.subtract_radio = QRadioButton("خصم مبلغ")
        self.set_radio = QRadioButton("تعيين قيمة جديدة")

        operation_layout.addWidget(self.add_radio)
        operation_layout.addWidget(self.subtract_radio)
        operation_layout.addWidget(self.set_radio)

        operation_group.setLayout(operation_layout)
        main_layout.addWidget(operation_group)

        # إنشاء مجموعة أزرار الاختيار
        self.operation_group = QButtonGroup()
        self.operation_group.addButton(self.add_radio, 1)
        self.operation_group.addButton(self.subtract_radio, 2)
        self.operation_group.addButton(self.set_radio, 3)

        # حقل إدخال المبلغ
        amount_layout = QFormLayout()
        self.amount_edit = QLineEdit()
        self.amount_edit.setPlaceholderText("أدخل المبلغ")
        amount_layout.addRow("المبلغ:", self.amount_edit)
        main_layout.addLayout(amount_layout)

        # أزرار الحفظ والإلغاء باستخدام النمط الموحد
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        save_button = QPushButton("💾 تطبيق")
        save_button.clicked.connect(self.accept)
        save_button.setStyleSheet(UnifiedStyles.get_button_style("success", "normal"))

        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.clicked.connect(self.reject)
        cancel_button.setStyleSheet(UnifiedStyles.get_button_style("secondary", "normal"))

        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)

        main_layout.addLayout(button_layout)
        self.setLayout(main_layout)

    def validate_data(self):
        errors = []

        # التحقق من أن حقل المبلغ ليس فارغاً
        amount_text = self.amount_edit.text().strip()
        if not amount_text:
            errors.append("يرجى إدخال المبلغ")
        else:
            # التحقق من صحة المبلغ
            try:
                amount = float(amount_text)
                if amount < 0:
                    errors.append("المبلغ يجب أن يكون صفر أو أكبر")
            except ValueError:
                errors.append("المبلغ يجب أن يكون رقماً صحيحاً")

        return errors

    def get_data(self):
        """الحصول على بيانات التعديل"""
        # التحقق من صحة البيانات أولاً
        errors = self.validate_data()
        if errors:
            QMessageBox.warning(self, "خطأ في البيانات", "\n".join(errors))
            return None

        # تحويل النص إلى رقم بعد التأكد من صحته
        amount_text = self.amount_edit.text().strip()
        if not amount_text:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال المبلغ")
            return None

        try:
            amount = float(amount_text)
        except ValueError:
            QMessageBox.warning(self, "خطأ", "المبلغ المدخل غير صحيح")
            return None

        if amount < 0:
            QMessageBox.warning(self, "خطأ", "المبلغ يجب أن يكون صفر أو أكبر")
            return None

        operation_id = self.operation_group.checkedId()

        if operation_id == 1:
            # إضافة مبلغ
            return {'amount': amount, 'operation': 'add'}
        elif operation_id == 2:
            # خصم مبلغ
            return {'amount': amount, 'operation': 'subtract'}
        elif operation_id == 3:
            # تعيين قيمة جديدة
            current_balance = self.employee.balance
            # حساب الفرق بين القيمة الحالية والقيمة الجديدة
            if amount == current_balance:
                # لا يوجد تغيير
                return {'amount': 0, 'operation': 'add'}
            elif amount > current_balance:
                return {'amount': amount - current_balance, 'operation': 'add'}
            else:
                return {'amount': current_balance - amount, 'operation': 'subtract'}

        return None

class EmployeeBalanceAdjustmentDialog(QDialog):

    def __init__(self, parent=None, employee=None):
        super().__init__(parent)
        self.setWindowTitle("تعديل المبلغ المستحق")
        self.setModal(True)
        self.setMinimumSize(400, 300)
        self.employee = employee

        # تطبيق نمط أساسي للنافذة
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
            }
            QLabel {
                color: #495057;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: 12px;
            }
            QLineEdit {
                padding: 8px;
                border: 2px solid #ced4da;
                border-radius: 6px;
                background-color: white;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #007bff;
            }
            QPushButton {
                padding: 8px 16px;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }

        self.init_ui()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # عرض معلومات العامل
        employee_info = QLabel(f"العامل: {self.employee.name}")
        main_layout.addWidget(employee_info)

        current_balance = QLabel(f"المبلغ المستحق الحالي: {self.employee.balance:.2f}")
        main_layout.addWidget(current_balance)

        # إضافة شرح للمبلغ المستحق
        balance_explanation = QLabel("ملاحظة: القيمة الموجبة تعني أن المبلغ للعامل، والقيمة السالبة تعني أن المبلغ على العامل")
        balance_explanation.setWordWrap(True)
        main_layout.addWidget(balance_explanation)

        # إنشاء مجموعة الخيارات
        operation_group = QGroupBox("نوع العملية")
        operation_layout = QVBoxLayout()

        self.add_radio = QRadioButton("إضافة مبلغ")
        self.add_radio.setChecked(True)
        self.subtract_radio = QRadioButton("خصم مبلغ")
        self.set_radio = QRadioButton("تعيين قيمة جديدة")

        operation_layout.addWidget(self.add_radio)
        operation_layout.addWidget(self.subtract_radio)
        operation_layout.addWidget(self.set_radio)

        operation_group.setLayout(operation_layout)
        main_layout.addWidget(operation_group)

        # إنشاء مجموعة أزرار الاختيار
        self.operation_group = QButtonGroup()
        self.operation_group.addButton(self.add_radio, 1)
        self.operation_group.addButton(self.subtract_radio, 2)
        self.operation_group.addButton(self.set_radio, 3)

        # حقل إدخال المبلغ
        amount_layout = QFormLayout()
        self.amount_edit = QLineEdit()
        self.amount_edit.setPlaceholderText("أدخل المبلغ")
        amount_layout.addRow("المبلغ:", self.amount_edit)
        main_layout.addLayout(amount_layout)

        # أزرار الحفظ والإلغاء باستخدام النمط الموحد
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        save_button = QPushButton("💾 تطبيق")
        save_button.clicked.connect(self.accept)
        save_button.setStyleSheet(UnifiedStyles.get_button_style("success", "normal"))

        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.clicked.connect(self.reject)
        cancel_button.setStyleSheet(UnifiedStyles.get_button_style("secondary", "normal"))

        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)

        main_layout.addLayout(button_layout)
        self.setLayout(main_layout)

    def validate_data(self):
        """التحقق من صحة البيانات"""
        errors = []

        # التحقق من أن حقل المبلغ ليس فارغاً
        amount_text = self.amount_edit.text().strip()
        if not amount_text:
            errors.append("يرجى إدخال المبلغ")
        else:
            # التحقق من صحة المبلغ
            try:
                amount = float(amount_text)
                if amount < 0:
                    errors.append("المبلغ يجب أن يكون صفر أو أكبر")
            except ValueError:
                errors.append("المبلغ يجب أن يكون رقماً صحيحاً")

        return errors

    def get_data(self):
        # التحقق من صحة البيانات أولاً
        errors = self.validate_data()
        if errors:
            QMessageBox.warning(self, "خطأ في البيانات", "\n".join(errors))
            return None

        # تحويل النص إلى رقم بعد التأكد من صحته
        amount_text = self.amount_edit.text().strip()
        if not amount_text:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال المبلغ")
            return None

        try:
            amount = float(amount_text)
        except ValueError:
            QMessageBox.warning(self, "خطأ", "المبلغ المدخل غير صحيح")
            return None

        if amount < 0:
            QMessageBox.warning(self, "خطأ", "المبلغ يجب أن يكون صفر أو أكبر")
            return None

        operation_id = self.operation_group.checkedId()

        if operation_id == 1:
            # إضافة مبلغ
            return {'amount': amount, 'operation': 'add'}
        elif operation_id == 2:
            # خصم مبلغ
            return {'amount': amount, 'operation': 'subtract'}
        elif operation_id == 3:
            # تعيين قيمة جديدة
            current_balance = self.employee.balance
            # حساب الفرق بين القيمة الحالية والقيمة الجديدة
            if amount == current_balance:
                # لا يوجد تغيير
                return {'amount': 0, 'operation': 'add'}
            elif amount > current_balance:
                return {'amount': amount - current_balance, 'operation': 'add'}
            else:
                return {'amount': current_balance - amount, 'operation': 'subtract'}

        return None