"""
            from PyQt5.QtWidgets import <PERSON><PERSON>rame
            from PyQt5.QtWidgets import QGroupBox
            from PyQt5.QtWidgets import Q<PERSON>abel
            from PyQt5.QtWidgets import QPushButton
            from PyQt5.QtWidgets import QTabWidget
            from PyQt5.QtWidgets import QTableWidget, QHeaderView
        from PyQt5.QtWidgets import QHeaderView
from PyQt5.QtWidgets import QDialog

أنماط موحدة مبسطة ومتوافقة مع PyQt5
"""


class UnifiedStyles:

    COLORS = {
        # الألوان الأساسية
        'primary': '#2563EB',
        'primary_dark': '#1D4ED8',
        'primary_light': '#3B82F6',
        'primary_ultra': '#1E40AF',
        'primary_glow': '#60A5FA',

        'secondary': '#8B5CF6',
        'secondary_dark': '#7C3AED',
        'secondary_light': '#A78BFA',
        'secondary_ultra': '#6D28D9',
        'secondary_glow': '#C4B5FD',

        'success': '#059669',
        'success_dark': '#047857',
        'success_light': '#10B981',
        'success_ultra': '#065F46',
        'success_glow': '#34D399',

        'warning': '#D97706',
        'warning_dark': '#B45309',
        'warning_light': '#F59E0B',
        'warning_ultra': '#92400E',
        'warning_glow': '#FBBF24',

        'danger': '#DC2626',
        'danger_dark': '#B91C1C',
        'danger_light': '#EF4444',
        'danger_ultra': '#991B1B',
        'danger_glow': '#F87171',

        'info': '#0891B2',
        'info_dark': '#0E7490',
        'info_light': '#06B6D4',
        'info_ultra': '#155E75',
        'info_glow': '#22D3EE',

        # ألوان الخلفيات
        'background': '#F8FAFC',
        'background_dark': '#E2E8F0',
        'surface': '#FFFFFF',
        'surface_dark': '#F1F5F9',

        # ألوان النصوص
        'text_primary': '#0F172A',
        'text_secondary': '#475569',
        'text_muted': '#64748B',
        'text_inverse': '#FFFFFF',

        # ألوان الحدود
        'border': '#E2E8F0',
        'border_dark': '#CBD5E1',
        'border_light': '#F1F5F9',

        # تدرجات بسيطة
        'gradient_primary': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #2563EB, stop:1 #1D4ED8)',
        'gradient_secondary': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #8B5CF6, stop:1 #7C3AED)',
        'gradient_success': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #059669, stop:1 #047857)',
        'gradient_warning': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #D97706, stop:1 #B45309)',
        'gradient_danger': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #DC2626, stop:1 #B91C1C)',
        'gradient_info': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0891B2, stop:1 #0E7490)',
    }

    FONT_SIZES = {
        'small': '16px',
        'normal': '18px',
        'medium': '20px',
        'lg': '24px',
        'large': '26px',
        'xl': '30px',
        'xlarge': '30px',
        'xxl': '34px',
        'xxlarge': '34px'
    }

    FONT_WEIGHTS = {
        'normal': '400',
        'medium': '500',
        'semibold': '600',
        'bold': '700'
    }

    SPACING = {
        'xs': '4px',
        'sm': '6px',
        'md': '8px',
        'lg': '12px',
        'xl': '16px',
        'xxl': '20px'
    }

    BORDER_RADIUS = {
        'sm': '4px',
        'md': '6px',
        'lg': '8px',
        'xl': '12px'
    }

    @staticmethod
    def get_button_style(button_type='primary', size='normal'):
        """نمط الأزرار المبسط والمتوافق مع PyQt5"""
        colors = UnifiedStyles.COLORS
        font_size = UnifiedStyles.FONT_SIZES[size]
        spacing = UnifiedStyles.SPACING

            QPushButton {{
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: {font_size};
                font-weight: {UnifiedStyles.FONT_WEIGHTS['bold']};
                padding: {spacing['md']} {spacing['xl']};
                min-height: 38px;
                max-height: 38px;
                min-width: 120px;
                border-radius: {UnifiedStyles.BORDER_RADIUS['xl']};
                border: 2px solid transparent;
                text-align: center;
                outline: none;
            }}
            QPushButton:disabled {{
                opacity: 0.5;
                background-color: {colors['surface_dark']};
                color: {colors['text_secondary']};
                border: 2px solid {colors['border']};
            }}
            QPushButton:focus {{
                outline: none;
                border: 2px solid {colors['primary_glow']};
            }}
        """

        if button_type == 'primary':
                QPushButton {{
                    background: {colors['gradient_primary']};
                    color: {colors['text_inverse']};
                    border: 2px solid {colors['primary']};
                }}
                QPushButton:hover {{
                    background: {colors['primary_light']};
                    border: 2px solid {colors['primary_light']};
                }}
                QPushButton:pressed {{
                    background: {colors['primary_dark']};
                    border: 2px solid {colors['primary_dark']};
                }}
            """
        elif button_type == 'success':
                QPushButton {{
                    background: {colors['gradient_success']};
                    color: {colors['text_inverse']};
                    border: 2px solid {colors['success']};
                }}
                QPushButton:hover {{
                    background: {colors['success_light']};
                    border: 2px solid {colors['success_light']};
                }}
                QPushButton:pressed {{
                    background: {colors['success_dark']};
                    border: 2px solid {colors['success_dark']};
                }}
            """
        elif button_type == 'warning':
                QPushButton {{
                    background: {colors['gradient_warning']};
                    color: {colors['text_inverse']};
                    border: 2px solid {colors['warning']};
                }}
                QPushButton:hover {{
                    background: {colors['warning_light']};
                    border: 2px solid {colors['warning_light']};
                }}
                QPushButton:pressed {{
                    background: {colors['warning_dark']};
                    border: 2px solid {colors['warning_dark']};
                }}
            """
        elif button_type == 'danger':
                QPushButton {{
                    background: {colors['gradient_danger']};
                    color: {colors['text_inverse']};
                    border: 2px solid {colors['danger']};
                }}
                QPushButton:hover {{
                    background: {colors['danger_light']};
                    border: 2px solid {colors['danger_light']};
                }}
                QPushButton:pressed {{
                    background: {colors['danger_dark']};
                    border: 2px solid {colors['danger_dark']};
                }}
            """
        elif button_type == 'secondary':
                QPushButton {{
                    background-color: {colors['surface']};
                    color: {colors['text_primary']};
                    border: 2px solid {colors['border_dark']};
                }}
                QPushButton:hover {{
                    background-color: {colors['surface_dark']};
                    border: 2px solid {colors['primary']};
                }}
                QPushButton:pressed {{
                    background-color: {colors['background_dark']};
                }}
            """
        elif button_type == 'info':
                QPushButton {{
                    background: {colors['gradient_info']};
                    color: {colors['text_inverse']};
                    border: 2px solid {colors['info']};
                }}
                QPushButton:hover {{
                    background: {colors['info_light']};
                    border: 2px solid {colors['info_light']};
                }}
                QPushButton:pressed {{
                    background: {colors['info_dark']};
                    border: 2px solid {colors['info_dark']};
                }}
            """
        else:
            return base_style

    @staticmethod
    def get_group_box_style(color='primary'):
        colors = UnifiedStyles.COLORS
        border_color = colors.get(color, colors['primary'])

        return f"""
            QGroupBox {{
                font-size: {UnifiedStyles.FONT_SIZES['normal']};
                font-weight: bold;
                color: {colors['text_primary']};
                border: 2px solid {border_color};
                border-radius: {UnifiedStyles.BORDER_RADIUS['lg']};
                margin-top: 15px;
                padding-top: 15px;
                background-color: {colors['surface']};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: {colors['surface']};
                color: {border_color};
                font-size: {UnifiedStyles.FONT_SIZES['medium']};
            }}

    @staticmethod
    def get_table_style():
        """نمط الجداول المتطور والمتقدم مع إطارات وحدود جميلة"""
        colors = UnifiedStyles.COLORS
        spacing = UnifiedStyles.SPACING

            QTableWidget {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:0.1 #f8f9fa, stop:0.9 #f1f3f4, stop:1 #e8eaed);
                border: 3px solid transparent;
                border-image: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:0.25 #764ba2, stop:0.5 #f093fb,
                    stop:0.75 #f5576c, stop:1 #4facfe) 1;
                border-radius: 15px;
                gridline-color: rgba(102, 126, 234, 0.3);
                font-size: {UnifiedStyles.FONT_SIZES['normal']};
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-weight: {UnifiedStyles.FONT_WEIGHTS['medium']};
                outline: none;
                selection-background-color: {colors['primary_light']};
                alternate-background-color: rgba(102, 126, 234, 0.05);
                margin: 0px;
                padding: 0px;
                min-width: 100%;
                width: 100%;
            }}
            QTableWidget::item {{
                padding: {spacing['lg']} {spacing['xl']};
                border: 2px solid rgba(102, 126, 234, 0.2);
                border-left: 4px solid rgba(102, 126, 234, 0.4);
                border-right: 4px solid rgba(102, 126, 234, 0.4);
                text-align: center;
                min-height: 35px;
                max-height: 50px;
                color: {colors['text_primary']};
                font-weight: {UnifiedStyles.FONT_WEIGHTS['semibold']};
                font-size: {UnifiedStyles.FONT_SIZES['large']};
                font-family: 'Segoe UI', 'Arial', sans-serif;
                /* تم إزالة الخلفية الثابتة للسماح بألوان الرصيد المخصصة */
                border-radius: 8px;
                margin: 2px;
            }}
            QTableWidget::item:selected {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                color: white;
                border: 3px solid rgba(255, 255, 255, 0.8);
                border-left: 6px solid #ffd700;
                border-right: 6px solid #ffd700;
                border-radius: 10px;
                font-weight: {UnifiedStyles.FONT_WEIGHTS['bold']};
                font-size: {UnifiedStyles.FONT_SIZES['large']};
                margin: 1px;
                padding: {spacing['lg']} {spacing['xl']};
            }}
            QTableWidget::item:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.1),
                    stop:1 rgba(102, 126, 234, 0.2));
                color: {colors['text_primary']};
                border: 3px solid rgba(102, 126, 234, 0.6);
                border-left: 5px solid #4facfe;
                border-right: 5px solid #4facfe;
                border-radius: 10px;
                font-weight: {UnifiedStyles.FONT_WEIGHTS['bold']};
                margin: 1px;
                padding: {spacing['lg']} {spacing['xl']};
            }}
            QTableWidget::item:selected:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4facfe, stop:0.5 #00f2fe, stop:1 #667eea);
                color: white;
                border: 4px solid rgba(255, 255, 255, 0.9);
                border-left: 7px solid #ffd700;
                border-right: 7px solid #ffd700;
                font-weight: {UnifiedStyles.FONT_WEIGHTS['bold']};
            }}
            QTableWidget::item:alternate {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.03),
                    stop:1 rgba(102, 126, 234, 0.08));
                border-bottom: 2px solid rgba(102, 126, 234, 0.2);
            }}
            QHeaderView::section {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:0.2 #764ba2, stop:0.4 #f093fb,
                    stop:0.6 #f5576c, stop:0.8 #4facfe, stop:1 #00f2fe);
                color: white;
                padding: {spacing['xl']} {spacing['xxl']};
                font-weight: {UnifiedStyles.FONT_WEIGHTS['bold']};
                font-size: {UnifiedStyles.FONT_SIZES['large']};
                font-family: 'Segoe UI', 'Arial', sans-serif;
                min-height: 55px;
                max-height: 55px;
                text-align: center;
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-bottom: 4px solid rgba(255, 255, 255, 0.5);
                border-radius: 12px 12px 0 0;
                margin: 0px;
            }}
            QHeaderView::section:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4facfe, stop:0.3 #00f2fe, stop:0.6 #667eea, stop:1 #764ba2);
                color: white;
                font-weight: {UnifiedStyles.FONT_WEIGHTS['bold']};
                border: 4px solid rgba(255, 255, 255, 0.6);
                border-bottom: 5px solid #ffd700;
                margin: 0px;
                padding: {spacing['xl']} {spacing['xxl']};
            }}
            QHeaderView::section:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2563EB, stop:0.5 #1D4ED8, stop:1 #1E40AF);
                border: 3px solid rgba(255, 255, 255, 0.8);
                border-bottom: 6px solid #ffd700;
            }}
            QScrollBar:vertical {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(102, 126, 234, 0.1),
                    stop:1 rgba(102, 126, 234, 0.2));
                width: 20px;
                border-radius: 10px;
                margin: 3px;
                border: 2px solid rgba(102, 126, 234, 0.3);
            }}
            QScrollBar::handle:vertical {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                border-radius: 8px;
                min-height: 40px;
                border: 2px solid rgba(255, 255, 255, 0.4);
                margin: 2px;
            }}
            QScrollBar::handle:vertical:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4facfe, stop:0.5 #00f2fe, stop:1 #667eea);
                border: 2px solid rgba(255, 255, 255, 0.6);
            }}
            QScrollBar::handle:vertical:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2563EB, stop:0.5 #1D4ED8, stop:1 #1E40AF);
                border: 2px solid rgba(255, 255, 255, 0.8);
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                height: 0px;
            }}
            QScrollBar:horizontal {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.1),
                    stop:1 rgba(102, 126, 234, 0.2));
                height: 20px;
                border-radius: 10px;
                margin: 3px;
                border: 2px solid rgba(102, 126, 234, 0.3);
            }}
            QScrollBar::handle:horizontal {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                border-radius: 8px;
                min-width: 40px;
                border: 2px solid rgba(255, 255, 255, 0.4);
                margin: 2px;
            }}
            QScrollBar::handle:horizontal:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4facfe, stop:0.5 #00f2fe, stop:1 #667eea);
                border: 2px solid rgba(255, 255, 255, 0.6);
            }}
            QScrollBar::handle:horizontal:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2563EB, stop:0.5 #1D4ED8, stop:1 #1E40AF);
                border: 2px solid rgba(255, 255, 255, 0.8);
            }}
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
                width: 0px;
            }}

        """

    @staticmethod
    def get_tab_widget_style():
        colors = UnifiedStyles.COLORS
        return f"""
            QTabWidget::pane {{
                border: 2px solid {colors['border_dark']};
                border-radius: {UnifiedStyles.BORDER_RADIUS['lg']};
                background-color: {colors['surface']};
            }}
            QTabBar::tab {{
                background-color: {colors['background']};
                color: {colors['text_primary']};
                border: 1px solid {colors['border']};
                padding: 8px 16px;
                margin-right: 2px;
                border-radius: 4px 4px 0 0;
                font-size: {UnifiedStyles.FONT_SIZES['normal']};
                font-weight: {UnifiedStyles.FONT_WEIGHTS['medium']};
            }}
            QTabBar::tab:selected {{
                background-color: {colors['primary']};
                color: {colors['text_inverse']};
                border-bottom: 2px solid {colors['primary']};
            }}
            QTabBar::tab:hover {{
                background-color: {colors['primary_light']};
                color: {colors['text_inverse']};
            }}

    @staticmethod
    def get_title_style():
        """نمط العناوين المبسط"""
        colors = UnifiedStyles.COLORS
            QLabel {{
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: {UnifiedStyles.FONT_SIZES['xxlarge']};
                font-weight: {UnifiedStyles.FONT_WEIGHTS['bold']};
                color: {colors['primary']};
                padding: {UnifiedStyles.SPACING['lg']};
                text-align: center;
                background-color: {colors['surface']};
                border-radius: {UnifiedStyles.BORDER_RADIUS['lg']};
                margin: {UnifiedStyles.SPACING['md']};
            }}
        """

    @staticmethod
    def get_input_style():
        colors = UnifiedStyles.COLORS
        return f"""
            QLineEdit {{
                padding: {UnifiedStyles.SPACING['md']};
                font-size: {UnifiedStyles.FONT_SIZES['normal']};
                border: 2px solid {colors['border']};
                border-radius: {UnifiedStyles.BORDER_RADIUS['md']};
                background-color: {colors['surface']};
                color: {colors['text_primary']};
                min-height: 30px;
            }}
            QLineEdit:focus {{
                border: 2px solid {colors['primary']};
            }}

    @staticmethod
    def get_menu_style(color_scheme='primary', size='normal'):
        """نمط القوائم المنسدلة المحسن والموحد"""
        colors = UnifiedStyles.COLORS

        # تحديد الأحجام
        sizes = {
            'small': {
                'font_size': '14px',
                'padding': '8px 12px',
                'margin': '2px',
                'border_radius': '8px',
                'min_width': '180px',
                'item_height': '28px'
            },
            'normal': {
                'font_size': '16px',
                'padding': '12px 16px',
                'margin': '3px',
                'border_radius': '12px',
                'min_width': '220px',
                'item_height': '36px'
            },
            'large': {
                'font_size': '18px',
                'padding': '16px 20px',
                'margin': '4px',
                'border_radius': '16px',
                'min_width': '260px',
                'item_height': '44px'
            }
        }

        size_config = sizes.get(size, sizes['normal'])

        # تحديد ألوان النظام
        scheme_colors = {
            'primary': {
                'bg': colors['surface'],
                'border': colors['primary'],
                'text': colors['text_primary'],
                'hover_bg': colors['primary'],
                'hover_text': colors['text_inverse']
            },
            'success': {
                'bg': '#f0fdf4',
                'border': colors['success'],
                'text': colors['text_primary'],
                'hover_bg': colors['success'],
                'hover_text': colors['text_inverse']
            },
            'warning': {
                'bg': '#fffbeb',
                'border': colors['warning'],
                'text': colors['text_primary'],
                'hover_bg': colors['warning'],
                'hover_text': colors['text_inverse']
            },
            'danger': {
                'bg': '#fef2f2',
                'border': colors['danger'],
                'text': colors['text_primary'],
                'hover_bg': colors['danger'],
                'hover_text': colors['text_inverse']
            }
        }

        scheme = scheme_colors.get(color_scheme, scheme_colors['primary'])

            QMenu {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {scheme['bg']},
                    stop:0.05 rgba(255, 255, 255, 0.95),
                    stop:0.1 rgba(248, 250, 252, 0.9),
                    stop:0.85 rgba(226, 232, 240, 0.85),
                    stop:0.95 rgba(203, 213, 225, 0.9),
                    stop:1 {scheme['bg']});
                border: 3px solid {scheme['border']};
                border-radius: {size_config['border_radius']};
                padding: 12px 8px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: {size_config['font_size']};
                font-weight: 700;
                min-width: {size_config['min_width']};
                max-width: 350px;
                /* box-shadow غير مدعوم في PyQt5 */
                selection-background-color: {scheme['hover_bg']};
            }}

            QMenu::item {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(255, 255, 255, 0.05),
                    stop:1 rgba(255, 255, 255, 0.1));
                padding: {size_config['padding']};
                border-radius: 10px;
                margin: {size_config['margin']};
                color: {scheme['text']};
                font-size: {size_config['font_size']};
                font-weight: 700;
                border: 2px solid rgba(255, 255, 255, 0.2);
                min-height: {size_config['item_height']};
                text-align: center;
                qproperty-alignment: AlignCenter;
                letter-spacing: 0.5px;
                text-decoration: none;
            }}

            QMenu::item:selected {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {scheme['hover_bg']},
                    stop:0.3 rgba(255, 255, 255, 0.2),
                    stop:0.7 rgba(255, 255, 255, 0.1),
                    stop:1 {scheme['border']});
                color: {scheme['hover_text']};
                border: 3px solid rgba(255, 255, 255, 0.8);
                font-weight: 900;
                border-radius: 12px;
                text-align: center;
                qproperty-alignment: AlignCenter;
                letter-spacing: 1px;
                /* box-shadow غير مدعوم في PyQt5 */
            }}

            QMenu::item:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {scheme['border']},
                    stop:0.5 rgba(0, 0, 0, 0.1),
                    stop:1 {scheme['hover_bg']});
                color: {scheme['hover_text']};
                border: 3px solid rgba(255, 255, 255, 0.9);
                font-weight: 900;
                border-radius: 8px;
                text-align: center;
                qproperty-alignment: AlignCenter;
                letter-spacing: 0.8px;
                /* box-shadow غير مدعوم في PyQt5 */
            }}

            QMenu::item:disabled {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {colors['surface_dark']},
                    stop:1 rgba(148, 163, 184, 0.3));
                color: {colors['text_muted']};
                border: 2px solid rgba(148, 163, 184, 0.2);
                font-weight: 400;
                text-align: center;
                qproperty-alignment: AlignCenter;
                opacity: 0.6;
            }}

            QMenu::separator {{
                height: 3px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.3),
                    stop:0.2 {scheme['border']},
                    stop:0.5 rgba(255, 255, 255, 0.5),
                    stop:0.8 {scheme['border']},
                    stop:1 rgba(255, 255, 255, 0.3));
                margin: 10px 20px;
                border-radius: 2px;
                /* box-shadow غير مدعوم في PyQt5 */
            }}

            QMenu::icon {{
                padding-left: 12px;
                padding-right: 12px;
                width: 20px;
                height: 20px;
                margin-right: 8px;
            }}

            QMenu::right-arrow {{
                image: none;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-top: 6px solid {scheme['border']};
                margin-right: 8px;
            }}
        """

# فئات مخصصة للعناصر
class StyledButton:
    def __init__(self, button_or_text, button_type='primary', size='normal'):
        # إذا كان المدخل نص، إنشاء زر جديد
        if isinstance(button_or_text, str):
            self.button = QPushButton(button_or_text)
        else:
            # إذا كان المدخل كائن زر، استخدامه مباشرة
            self.button = button_or_text

        self.button.setStyleSheet(UnifiedStyles.get_button_style(button_type, size))

    # إضافة خصائص للوصول المباشر لخصائص الزر
    @property
    def clicked(self):
        return self.button.clicked

    def setMinimumWidth(self, width):
        return self.button.setMinimumWidth(width)

    def setMaximumWidth(self, width):
        return self.button.setMaximumWidth(width)

    def setMenu(self, menu):
        return self.button.setMenu(menu)

    def setText(self, text):
        return self.button.setText(text)

    def text(self):
        return self.button.text()

class StyledTable:
    def __init__(self, table_or_create=None):
        # إذا لم يتم تمرير جدول، إنشاء جدول جديد
        if table_or_create is None:
            self.table = QTableWidget()
        else:
            # إذا تم تمرير جدول، استخدامه مباشرة
            self.table = table_or_create

        # تطبيق النمط المتطور (بدون خلفية ثابتة للخلايا للسماح بألوان مخصصة)
        self.table.setStyleSheet(UnifiedStyles.get_table_style())

        # توسيع الجدول ليملأ العرض المتاح
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        # إعدادات إضافية لتحسين العرض
        self.table.setAlternatingRowColors(True)
        self.table.verticalHeader().setVisible(False)
        self.table.setSelectionBehavior(self.table.SelectRows)
        self.table.setSelectionMode(self.table.SingleSelection)
        self.table.setEditTriggers(self.table.NoEditTriggers)

class StyledGroupBox:
    def __init__(self, title_or_group_box, color='primary'):
        # إذا كان المدخل نص، إنشاء GroupBox جديد
        if isinstance(title_or_group_box, str):
            self.group_box = QGroupBox(title_or_group_box)
        else:
            # إذا كان المدخل كائن GroupBox، استخدامه مباشرة
            self.group_box = title_or_group_box

        self.group_box.setStyleSheet(UnifiedStyles.get_group_box_style(color))

    # إضافة خصائص للوصول المباشر لخصائص GroupBox
    def setLayout(self, layout):
        return self.group_box.setLayout(layout)

    def layout(self):
        """الحصول على التخطيط من الـ GroupBox"""
        return self.group_box.layout()

    def setTitle(self, title):
        return self.group_box.setTitle(title)

    def title(self):
        """الحصول على العنوان من الـ GroupBox"""
        return self.group_box.title()

class StyledTableFrame:
    def __init__(self, frame_or_create=None):
        # إذا لم يتم تمرير إطار، إنشاء إطار جديد
        if frame_or_create is None:
            self.frame = QFrame()
        else:
            # إذا تم تمرير إطار، استخدامه مباشرة
            self.frame = frame_or_create

        colors = UnifiedStyles.COLORS
            QFrame {{
                background-color: {colors['surface']};
                border: 2px solid {colors['border_dark']};
                border-radius: {UnifiedStyles.BORDER_RADIUS['lg']};
                padding: {UnifiedStyles.SPACING['md']};
                margin: {UnifiedStyles.SPACING['sm']};
            }}
        """)

class StyledTabWidget:
    def __init__(self, tab_widget_or_create=None):
        # إذا لم يتم تمرير تبويب، إنشاء تبويب جديد
        if tab_widget_or_create is None:
            self.tab_widget = QTabWidget()
        else:
            # إذا تم تمرير تبويب، استخدامه مباشرة
            self.tab_widget = tab_widget_or_create

        self.tab_widget.setStyleSheet(UnifiedStyles.get_tab_widget_style())

    # إضافة خصائص للوصول المباشر لخصائص التبويب
    def addTab(self, widget, label):
        return self.tab_widget.addTab(widget, label)

    def setCurrentIndex(self, index):
        return self.tab_widget.setCurrentIndex(index)

    def currentIndex(self):
        return self.tab_widget.currentIndex()

class StyledLabel:
    def __init__(self, label_or_text, label_type='normal'):
        # إذا كان المدخل نص، إنشاء تسمية جديدة
        if isinstance(label_or_text, str):
            self.label = QLabel(label_or_text)
        else:
            # إذا كان المدخل كائن تسمية، استخدامه مباشرة
            self.label = label_or_text

        # تطبيق النمط حسب النوع
        self._apply_style(label_type)

    def _apply_style(self, label_type):
        colors = UnifiedStyles.COLORS

        # تحديد الألوان والخصائص حسب النوع
        style_configs = {
            'title': {
                'color': colors['text_primary'],
                'font_size': UnifiedStyles.FONT_SIZES['xl'],
                'font_weight': UnifiedStyles.FONT_WEIGHTS['bold'],
                'padding': f"{UnifiedStyles.SPACING['md']} {UnifiedStyles.SPACING['lg']}"
            },
            'heading': {
                'color': colors['text_primary'],
                'font_size': UnifiedStyles.FONT_SIZES['lg'],
                'font_weight': UnifiedStyles.FONT_WEIGHTS['bold'],
                'padding': f"{UnifiedStyles.SPACING['sm']} {UnifiedStyles.SPACING['md']}"
            },
            'hero': {
                'color': colors['primary'],
                'font_size': UnifiedStyles.FONT_SIZES['xxl'],
                'font_weight': UnifiedStyles.FONT_WEIGHTS['bold'],
                'padding': f"{UnifiedStyles.SPACING['lg']} {UnifiedStyles.SPACING['xl']}"
            },
            'success': {
                'color': colors['success'],
                'font_size': UnifiedStyles.FONT_SIZES['normal'],
                'font_weight': UnifiedStyles.FONT_WEIGHTS['medium'],
                'padding': f"{UnifiedStyles.SPACING['sm']} {UnifiedStyles.SPACING['md']}"
            },
            'danger': {
                'color': colors['danger'],
                'font_size': UnifiedStyles.FONT_SIZES['normal'],
                'font_weight': UnifiedStyles.FONT_WEIGHTS['medium'],
                'padding': f"{UnifiedStyles.SPACING['sm']} {UnifiedStyles.SPACING['md']}"
            },
            'warning': {
                'color': colors['warning'],
                'font_size': UnifiedStyles.FONT_SIZES['normal'],
                'font_weight': UnifiedStyles.FONT_WEIGHTS['medium'],
                'padding': f"{UnifiedStyles.SPACING['sm']} {UnifiedStyles.SPACING['md']}"
            },
            'info': {
                'color': colors['info'],
                'font_size': UnifiedStyles.FONT_SIZES['normal'],
                'font_weight': UnifiedStyles.FONT_WEIGHTS['medium'],
                'padding': f"{UnifiedStyles.SPACING['sm']} {UnifiedStyles.SPACING['md']}"
            },
            'normal': {
                'color': colors['text_primary'],
                'font_size': UnifiedStyles.FONT_SIZES['normal'],
                'font_weight': UnifiedStyles.FONT_WEIGHTS['medium'],
                'padding': f"{UnifiedStyles.SPACING['sm']} {UnifiedStyles.SPACING['md']}"
            }
        }

        # الحصول على التكوين أو استخدام النمط العادي كافتراضي
        config = style_configs.get(label_type, style_configs['normal'])

        # تطبيق النمط
        self.label.setStyleSheet(f"""
            QLabel {{
                font-family: 'Segoe UI', 'Arial', sans-serif;
                color: {config['color']};
                font-size: {config['font_size']};
                font-weight: {config['font_weight']};
                padding: {config['padding']};
            }}

    # إضافة خصائص للوصول المباشر لخصائص التسمية
    def setText(self, text):
        """تعيين النص للتسمية"""
        return self.label.setText(text)

    def text(self):
        return self.label.text()

    def setAlignment(self, alignment):
        """تعيين محاذاة النص"""
        return self.label.setAlignment(alignment)

    def setWordWrap(self, wrap):
        return self.label.setWordWrap(wrap)

    def setMinimumWidth(self, width):
        """تعيين الحد الأدنى للعرض"""
        return self.label.setMinimumWidth(width)

    def setMaximumWidth(self, width):
        return self.label.setMaximumWidth(width)

    def setMinimumHeight(self, height):
        """تعيين الحد الأدنى للارتفاع"""
        return self.label.setMinimumHeight(height)

    def setMaximumHeight(self, height):
        return self.label.setMaximumHeight(height)

class BaseDialog(QDialog):
    """حوار أساسي موحد"""

    def __init__(self, parent=None, title="حوار"):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.setMinimumSize(400, 300)

        # تطبيق النمط الموحد
        colors = UnifiedStyles.COLORS
            QDialog {{
                background-color: {colors['surface']};
                border: 2px solid {colors['border']};
                border-radius: {UnifiedStyles.BORDER_RADIUS['md']};
            }}
            QLabel {{
                color: {colors['text_primary']};
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: {UnifiedStyles.FONT_SIZES['normal']};
            }}
        """)

# دوال مساعدة بسيطة
def apply_table_style(table):
    table.setStyleSheet(UnifiedStyles.get_table_style())

def apply_button_style(button, button_type='primary', size='normal'):
    """تطبيق النمط على الزر"""
    button.setStyleSheet(UnifiedStyles.get_button_style(button_type, size))