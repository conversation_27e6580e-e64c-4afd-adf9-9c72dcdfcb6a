#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
import os

import sqlite3

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
سكريبت تحديث قاعدة البيانات لإضافة عمود الرصيد للعمال
"""


def update_database():
    db_path = "accounting.db"

    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود")
        return False

    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # التحقق من وجود عمود balance في جدول employees
        cursor.execute("PRAGMA table_info(employees)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'balance' not in columns:
            print("🔧 إضافة عمود الرصيد لجدول العمال...")
            cursor.execute("ALTER TABLE employees ADD COLUMN balance REAL DEFAULT 0.0")
            print("✅ تم إضافة عمود الرصيد بنجاح")
        else:
            print("✅ عمود الرصيد موجود بالفعل")

        # حفظ التغييرات
        conn.commit()
        conn.close()

        print("✅ تم تحديث قاعدة البيانات بنجاح")
        return True

    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {str(e)}")
        return False

if __name__ == "__main__":
    update_database()